/**
 * @file wifi_task.c
 * <AUTHOR> (<EMAIL>)
 * @brief WiFi任务实现
 * @version 0.1
 * @date 2025-05-06
 *
 * @copyright Copyright (c) 2025
 *
 */
#include "wifi_task.h"
#include "led_task.h"
#include "param_task.h"
#include "protocol.h"

// ===================== 配置 =====================
#define WIFI_USART_PORT             USART3
#define WIFI_USART_BAUDRATE         115200
#define WIFI_USART_CONFIG(baudrate) bsp_Usart3Init(baudrate)
#define WIFI_AT_PROCESS_INTERVAL    5U     // AT处理周期(ms)
#define WIFI_INIT_PROCESS_INTERVAL  10U    // 初始化流程处理周期(ms)
#define WIFI_MQTT_PUB_INTERVAL      1000U  // MQTT定时发布周期(ms)
#define WIFI_CONNECT_TIMEOUT        30000U // 30秒连接超时

// ===================== 任务上下文 =====================
static WifiTaskCtx wifi_ctx; // WiFi任务上下文结构体

// FreeRTOS定时器句柄
static TimerHandle_t at_process_timer   = NULL; // AT命令处理定时器
static TimerHandle_t init_process_timer = NULL; // 初始化流程定时器
static TimerHandle_t mqtt_pub_timer     = NULL; // MQTT定时发布定时器

// MQTT数据发布队列
static QueueHandle_t mqtt_pub_queue = NULL; // 用于任务间异步发布MQTT数据

// AT互斥量
static SemaphoreHandle_t at_mutex = NULL;

static int hexstr_to_bytes(const char *hex_str, uint8_t *out_buf, int out_len);

/**
 * @brief AT框架加锁回调
 */
static void at_lock(void)
{
    if (at_mutex) {
        xSemaphoreTake(at_mutex, portMAX_DELAY);
    }
}

/**
 * @brief AT框架解锁回调
 */
static void at_unlock(void)
{
    if (at_mutex) {
        xSemaphoreGive(at_mutex);
    }
}

/**
 * @brief 串口写入回调
 * @param buf 数据缓冲区
 * @param len 数据长度
 * @return 实际写入字节数
 */
static unsigned int wifi_uart_write(const void *buf, unsigned int len)
{
    return usart_send_buf(WIFI_USART_PORT, (unsigned char *)buf, len);
}

/**
 * @brief 串口读取回调
 * @param buf 数据缓冲区
 * @param len 数据长度
 * @return 实际读取字节数
 */
static unsigned int wifi_uart_read(void *buf, unsigned int len)
{
    return usart_recv_buf(WIFI_USART_PORT, (unsigned char *)buf, len);
}

/**
 * @brief AT调试信息输出（可选实现）
 */
static void wifi_at_debug(const char *fmt, ...)
{
    va_list args;
    va_start(args, fmt);
    // 如需调试可打开
    // vprintf(fmt, args);
    va_end(args);
}

/**
 * @brief AT通信错误回调
 */
static void wifi_at_error(at_response_t *r)
{
    WIFI_LOG_ERROR("AT通信错误");
}

/**
 * @brief AT适配器结构体，配置串口读写、调试、错误回调等
 */
static const at_adapter_t wifi_at_adapter = {
    .lock         = at_lock,
    .unlock       = at_unlock,
    .write        = wifi_uart_write,
    .read         = wifi_uart_read,
    .error        = wifi_at_error,
    .debug        = wifi_at_debug,
    .urc_bufsize  = 256,
    .recv_bufsize = 256,
};

// ===================== URC处理函数 =====================
/**
 * @brief 配网参数下发处理
 */
static int wifi_config_handler(at_urc_info_t *ctx)
{
    if (wifi_ctx.init_step == AT_CMD_MAX) return 0;
    if (strstr(ctx->urcbuf, "ssid=") != NULL) {
        if (sscanf(ctx->urcbuf, "ssid=\"%[^\"]\",password=\"%[^\"]\",server=\"%[^\"]\",port=%d,user=\"%[^\"]\",pw=\"%[^\"]\"", wifi_ctx.wifi_ssid, wifi_ctx.wifi_passwd, wifi_ctx.mqtt_server_addr, (int *)&wifi_ctx.mqtt_server_port, wifi_ctx.mqtt_username, wifi_ctx.mqtt_password) == 6) {
            wifi_param_t wifi_param;
            strcpy(wifi_param.ssid, wifi_ctx.wifi_ssid);
            strcpy(wifi_param.password, wifi_ctx.wifi_passwd);

            mqtt_param_t mqtt_param;
            strcpy(mqtt_param.server, wifi_ctx.mqtt_server_addr);
            strcpy(mqtt_param.username, wifi_ctx.mqtt_username);
            strcpy(mqtt_param.password, wifi_ctx.mqtt_password);

            param_save_wifi(&wifi_param);
            WIFI_LOG_INFO("WiFi参数保存成功");
            param_save_mqtt(&mqtt_param);
            WIFI_LOG_INFO("MQTT参数保存成功");
            usart_send_buf(WIFI_USART_PORT, (unsigned char *)"+++", 3);
            wifi_ctx.init_step = AT_CONN_AP;
        }
    }
    return 0;
}

/**
 * @brief MQTT数据接收处理
 */
static int mqtt_data_handler(at_urc_info_t *ctx)
{
    protocol_event_t event;
    char buf[255] = {0};
    uint8_t len = 0;
 
    WIFI_LOG_INFO("接收到的数据: %s", ctx->urcbuf);

    if (sscanf(ctx->urcbuf, "+MQTTSUBRECV:%s", buf) == 1) {
        len = hexstr_to_bytes(buf, event.data, sizeof(event.data));
    }

    // 处理MQTT数据
    protocol_event_handle(&event, len);

    return 0;
}

/**
 * @brief 蓝牙连接成功处理
 */
static int ble_connected_handler(at_urc_info_t *info)
{
    WIFI_LOG_INFO("蓝牙连接成功");
    return 0;
}

/**
 * @brief WiFi连接成功处理
 */
static int wifi_connected_handler(at_urc_info_t *info)
{
    WIFI_LOG_INFO("wifi连接成功");
    return 0;
}

/**
 * @brief WiFi断开处理
 */
static int wifi_disconnected_handler(at_urc_info_t *info)
{
    WIFI_LOG_INFO("wifi断开");
    return 0;
}

/**
 * @brief URC处理表，注册所有异步事件处理函数
 */
static const urc_item_t wifi_urc_table[] = {
    {.prefix = "WIFI CONNECTED", .endmark = '\n', .handler = wifi_connected_handler},
    {.prefix = "WIFI DISCONNECT", .endmark = '\n', .handler = wifi_disconnected_handler},
    {.prefix = "+CONNECTED", .endmark = '\n', .handler = ble_connected_handler},
    {.prefix = "ssid=", .endmark = '\n', .handler = wifi_config_handler},
    {.prefix = "+MQTTSUBRECV", .endmark = '\n', .handler = mqtt_data_handler},
};

// ===================== AT命令回调 =====================
/**
 * @brief AT测试命令回调
 */
static void at_test_callback(at_response_t *r)
{
    if (r->code == AT_RESP_OK) {
        WIFI_LOG_INFO("获取蓝牙地址");
        wifi_ctx.init_step = AT_GET_BLEADDR;
    } else {
        WIFI_LOG_ERROR("AT测试回调失败,重试");
        wifi_ctx.init_step = AT_TEST;
    }
}

/**
 * @brief 获取蓝牙地址命令回调
 */
static void at_get_ble_addr_callback(at_response_t *r)
{
    if (r->code == AT_RESP_OK) {
        char *start = NULL;
        if ((start = strstr(r->recvbuf, "+BLEADDR:")) != NULL) {
            if (sscanf(start, "+BLEADDR:%x:%x:%x:%x:%x:%x", (uint32_t *)&wifi_ctx.ble_mac_addr[0], (uint32_t *)&wifi_ctx.ble_mac_addr[1], (uint32_t *)&wifi_ctx.ble_mac_addr[2], (uint32_t *)&wifi_ctx.ble_mac_addr[3], (uint32_t *)&wifi_ctx.ble_mac_addr[4], (uint32_t *)&wifi_ctx.ble_mac_addr[5]) == 6) {
                WIFI_LOG_INFO("获取蓝牙名称");
                wifi_ctx.init_step = AT_GET_BLENAME;
            }
        } else {
            WIFI_LOG_ERROR("AT+BLEADDR回调失败,重试");
            wifi_ctx.init_step = AT_GET_BLEADDR;
        }
    } else {
        WIFI_LOG_ERROR("AT+BLEADDR回调失败,重试");
        wifi_ctx.init_step = AT_GET_BLEADDR;
    }
}

/**
 * @brief 获取蓝牙名称命令回调
 */
static void at_get_ble_name_callback(at_response_t *r)
{
    char name[32];
    if (r->code == AT_RESP_OK) {
        char *start = NULL;
        if ((start = strstr(r->recvbuf, "+BLENAME:")) != NULL) {
            if (sscanf(start, "+BLENAME:%s", name)) {
                if (strstr(name, "DREB") != NULL) {
                    WIFI_LOG_INFO("设置WIFI模式");
                    wifi_ctx.init_step = AT_SET_WIFIMODE;
                } else {
                    WIFI_LOG_INFO("设置蓝牙名称");
                    wifi_ctx.init_step = AT_SET_BLENAME;
                }
            }
        } else {
            WIFI_LOG_ERROR("AT+BLENAME回调失败,重试");
            wifi_ctx.init_step = AT_GET_BLENAME;
        }
    } else {
        WIFI_LOG_ERROR("AT+BLENAME回调失败,重试");
        wifi_ctx.init_step = AT_GET_BLENAME;
    }
}

/**
 * @brief 设置蓝牙名称命令回调
 */
static void at_set_ble_name_callback(at_response_t *r)
{
    if (r->code == AT_RESP_OK) {
        WIFI_LOG_INFO("设置蓝牙名称成功");
        wifi_ctx.init_step = AT_SET_WIFIMODE;
    } else {
        WIFI_LOG_ERROR("AT+BLENAME回调失败,重试");
        wifi_ctx.init_step = AT_SET_BLENAME;
    }
}

/**
 * @brief 设置WIFI模式命令回调
 */
static void at_set_wifi_mode_callback(at_response_t *r)
{
    if (r->code == AT_RESP_OK) {
        WIFI_LOG_INFO("断开WIFI连接");
        wifi_ctx.init_step = AT_DISCONN_AP;
    } else {
        WIFI_LOG_ERROR("AT+CWMODE回调失败,重试");
        wifi_ctx.init_step = AT_SET_WIFIMODE;
    }
}

/**
 * @brief 断开WIFI连接命令回调
 */
static void at_disconn_ap_callback(at_response_t *r)
{
    if (r->code == AT_RESP_OK) {
        wifi_ctx.init_step = AT_CHECK_SAVED_PARAM;
    } else {
        WIFI_LOG_ERROR("AT+CWQAP回调失败,重试");
        wifi_ctx.init_step = AT_DISCONN_AP;
    }
}

/**
 * @brief 连接WIFI命令回调
 */
static void at_conn_ap_callback(at_response_t *r)
{
    if (r->code == AT_RESP_OK) {
        WIFI_LOG_INFO("配置MQTT参数");
        wifi_ctx.init_step = AT_MQTT_CFG;
    } else {
        uint32_t current_time = xTaskGetTickCount();
        if (wifi_ctx.use_saved_param && (current_time - wifi_ctx.connect_start_time > WIFI_CONNECT_TIMEOUT)) {
            WIFI_LOG_INFO("WiFi连接超时，切换到配网模式");
            wifi_ctx.init_step = AT_WAIT_APP_CFG;
        } else {
            WIFI_LOG_ERROR("WiFi连接失败，重试");
            wifi_ctx.init_step = AT_CONN_AP;
        }
    }
}

/**
 * @brief 配置MQTT用户命令回调
 */
static void at_mqtt_user_cfg_callback(at_response_t *r)
{
    if (r->code == AT_RESP_OK) {
        WIFI_LOG_INFO("连接MQTT服务器");
        wifi_ctx.init_step = AT_MQTTCONN_BROKER;
    } else {
        WIFI_LOG_ERROR("AT+MQTTUSERCFG回调失败,重试");
        wifi_ctx.init_step = AT_MQTT_CFG;
    }
}

/**
 * @brief 连接MQTT服务器命令回调
 */
static void at_mqtt_conn_callback(at_response_t *r)
{
    if (r->code == AT_RESP_OK) {
        WIFI_LOG_INFO("订阅MQTT主题");
        wifi_ctx.init_step = AT_MQTT_SUB;
    } else {
        WIFI_LOG_ERROR("AT+MQTTCONN回调失败,重试");
        wifi_ctx.init_step = AT_MQTTCONN_BROKER;
    }
}

/**
 * @brief 订阅MQTT主题命令回调
 */
static void at_mqtt_sub_callback(at_response_t *r)
{
    if (r->code == AT_RESP_OK) {
        WIFI_LOG_INFO("初始化完成");
        wifi_ctx.init_step = AT_CMD_MAX;
    } else {
        WIFI_LOG_ERROR("AT+MQTTSUB回调失败,重试");
        wifi_ctx.init_step = AT_MQTT_SUB;
    }
}

// ===================== AT命令发送函数 =====================
/**
 * @brief 发送设置蓝牙名称命令
 */
static void at_set_ble_name_sender(at_env_t *env)
{
    env->println(env, "AT+BLENAME=\"DREB_%X:%X:%X:%X:%X:%X\"\r\n", wifi_ctx.ble_mac_addr[0], wifi_ctx.ble_mac_addr[1], wifi_ctx.ble_mac_addr[2], wifi_ctx.ble_mac_addr[3], wifi_ctx.ble_mac_addr[4], wifi_ctx.ble_mac_addr[5]);
}

/**
 * @brief 发送WiFi连接命令
 */
static void at_conn_ap_sender(at_env_t *env)
{
    env->println(env, "AT+CWJAP=\"%s\",\"%s\",\"s.n\"\r\n", wifi_ctx.wifi_ssid, wifi_ctx.wifi_passwd);
}

/**
 * @brief 发送MQTT连接命令
 */
static void at_mqtt_conn_sender(at_env_t *env)
{
    env->println(env, "AT+MQTTCONN=0,\"%s\",%d,\"\",1\r\n", wifi_ctx.mqtt_server_addr, wifi_ctx.mqtt_server_port);
}

/**
 * @brief 发送MQTT用户配置命令
 */
static void at_mqtt_user_cfg_sender(at_env_t *env)
{
    env->println(env, "AT+MQTTUSERCFG=0,1,\"NULL\",\"%s\",\"%s\",0,0,\"\"\r\n", wifi_ctx.mqtt_username, wifi_ctx.mqtt_password);
}

/**
 * @brief 发送MQTT订阅命令
 */
static void at_mqtt_sub_sender(at_env_t *env)
{
    env->println(env, "AT+MQTTSUB=0,\"d/c/electricBed/%s\",0\r\n", wifi_ctx.ble_mac_addr);
}

/**
 * @brief 发送MQTT发布命令
 */
static void at_mqtt_pub_sender(at_env_t *env)
{
    char pub_data[64] = "test"; // 实际数据可根据需要赋值
    env->println(env, "AT+MQTTPUB=0,\"d/c/electricBed/%s\",0,0\r\n", wifi_ctx.ble_mac_addr);
}

// ===================== 参数检查/加载 =====================
/**
 * @brief 检查保存的WiFi/MQTT参数（如需实际存储请补全）
 * @return true-有参数，false-无参数
 */
static bool check_saved_wifi_param(void)
{
    bool ret = false;
    wifi_param_t wifi_param;
    mqtt_param_t mqtt_param;
    if (param_load_wifi(&wifi_param)) {
        WIFI_LOG_INFO("已找到已保存的WiFi配置");
    }
    if (param_load_mqtt(&mqtt_param)) {
        WIFI_LOG_INFO("已找到已保存的MQTT配置");
        ret = true;
    }
    if (ret) {
        strcpy(wifi_ctx.wifi_ssid, wifi_param.ssid);
        strcpy(wifi_ctx.wifi_passwd, wifi_param.password);
        strcpy(wifi_ctx.mqtt_server_addr, mqtt_param.server);
        strcpy(wifi_ctx.mqtt_username, mqtt_param.username);
        strcpy(wifi_ctx.mqtt_password, mqtt_param.password);
        WIFI_LOG_INFO("已加载保存的配置参数,WiFi:%s,MQTT:%s:%d,用户名:%s,密码:%s", wifi_ctx.wifi_ssid, wifi_ctx.mqtt_server_addr, wifi_ctx.mqtt_server_port, wifi_ctx.mqtt_username, wifi_ctx.mqtt_password);
    }
    return ret;
}

// ===================== WiFi状态对应LED显示 =====================
/**
 * @brief 更新WiFi状态对应的LED显示（仅状态变化时切换）
 */
static void wifi_led_update(void)
{
    static uint8_t last_wifi_led_mode = 0xFF; // 初始无效值
    uint8_t mode                      = 0;
    switch (wifi_ctx.init_step) {
        case AT_IDLE:
            mode = 0;
            break;
        case AT_TEST:
        case AT_GET_BLEADDR:
        case AT_GET_BLENAME:
        case AT_SET_BLENAME:
        case AT_SET_WIFIMODE:
        case AT_DISCONN_AP:
            mode = 1;
            break;
        case AT_CHECK_SAVED_PARAM:
        case AT_WAIT_APP_CFG:
            mode = 2;
            break;
        case AT_CONN_AP:
            mode = 3;
            break;
        case AT_MQTT_CFG:
        case AT_MQTTCONN_BROKER:
        case AT_MQTT_SUB:
            mode = 4;
            break;
        default:
            mode = 0;
            break;
    }
    if (mode == last_wifi_led_mode) return; // 状态未变，不重复发消息
    last_wifi_led_mode = mode;

    switch (mode) {
        case 0:
            LED_off(LED_WIFI);
            break;
        case 1:
            LED_BLINK_FAST(LED_WIFI);
            break;
        case 2:
            LED_BLINK_SLOW(LED_WIFI);
            break;
        case 3:
            LED_BLINK_NORMAL(LED_WIFI);
            break;
        case 4:
            LED_on(LED_WIFI);
            break;
        default:
            LED_off(LED_WIFI);
            break;
    }
}

// ===================== 状态机推进主流程 =====================
/**
 * @brief WiFi初始化流程状态机，定时推进
 */
static void wifi_init_config(void)
{
    at_attr_t attr;
    switch (wifi_ctx.init_step) {
        case AT_IDLE:
            wifi_led_update();
            break;
        case AT_TEST:
            at_attr_deinit(&attr);
            attr.retry   = 3;
            attr.timeout = 1000;
            attr.cb      = at_test_callback;
            at_send_singlline(wifi_ctx.at_obj, &attr, "AT\r\n");
            wifi_ctx.init_step = AT_IDLE;
            wifi_led_update();
            break;
        case AT_GET_BLEADDR:
            at_attr_deinit(&attr);
            attr.retry   = 3;
            attr.timeout = 3000;
            attr.cb      = at_get_ble_addr_callback;
            at_send_singlline(wifi_ctx.at_obj, &attr, "AT+BLEADDR\r\n");
            wifi_ctx.init_step = AT_IDLE;
            wifi_led_update();
            break;
        case AT_GET_BLENAME:
            at_attr_deinit(&attr);
            attr.retry   = 3;
            attr.timeout = 3000;
            attr.cb      = at_get_ble_name_callback;
            at_send_singlline(wifi_ctx.at_obj, &attr, "AT+BLENAME\r\n");
            wifi_ctx.init_step = AT_IDLE;
            wifi_led_update();
            break;
        case AT_SET_BLENAME:
            at_attr_deinit(&attr);
            attr.retry   = 3;
            attr.timeout = 3000;
            attr.cb      = at_set_ble_name_callback;
            at_custom_cmd(wifi_ctx.at_obj, &attr, at_set_ble_name_sender);
            wifi_ctx.init_step = AT_IDLE;
            wifi_led_update();
            break;
        case AT_SET_WIFIMODE:
            at_attr_deinit(&attr);
            attr.retry   = 3;
            attr.timeout = 3000;
            attr.cb      = at_set_wifi_mode_callback;
            at_send_singlline(wifi_ctx.at_obj, &attr, "AT+CWMODE=1,\"s.y\"\r\n");
            wifi_ctx.init_step = AT_IDLE;
            wifi_led_update();
            break;
        case AT_DISCONN_AP:
            at_attr_deinit(&attr);
            attr.retry   = 3;
            attr.timeout = 5000;
            attr.cb      = at_disconn_ap_callback;
            at_send_singlline(wifi_ctx.at_obj, &attr, "AT+CWQAP\r\n");
            wifi_ctx.init_step = AT_IDLE;
            wifi_led_update();
            break;
        case AT_CHECK_SAVED_PARAM:
            if (check_saved_wifi_param()) {
                wifi_ctx.use_saved_param = true;
                wifi_ctx.init_step       = AT_LOAD_SAVED_PARAM;
            } else {
                wifi_ctx.use_saved_param = false;
                wifi_ctx.init_step       = AT_WAIT_APP_CFG;
                WIFI_LOG_INFO("未找到已保存的配置参数,等待APP配网");
            }
            wifi_led_update();
            break;
        case AT_LOAD_SAVED_PARAM:
            wifi_ctx.connect_start_time = xTaskGetTickCount();
            wifi_ctx.init_step          = AT_CONN_AP;
            wifi_led_update();
            break;
        case AT_WAIT_APP_CFG:
            wifi_led_update();
            // 等待APP配网，不做操作
            break;
        case AT_CONN_AP:
            at_attr_deinit(&attr);
            attr.retry   = 3;
            attr.timeout = 10000;
            attr.cb      = at_conn_ap_callback;
            at_custom_cmd(wifi_ctx.at_obj, &attr, at_conn_ap_sender);
            wifi_ctx.init_step = AT_IDLE;
            wifi_led_update();
            break;
        case AT_MQTT_CFG:
            at_attr_deinit(&attr);
            attr.retry   = 3;
            attr.timeout = 10000;
            attr.cb      = at_mqtt_user_cfg_callback;
            at_custom_cmd(wifi_ctx.at_obj, &attr, at_mqtt_user_cfg_sender);
            wifi_ctx.init_step = AT_IDLE;
            wifi_led_update();
            break;
        case AT_MQTTCONN_BROKER:
            at_attr_deinit(&attr);
            attr.retry   = 3;
            attr.timeout = 10000;
            attr.cb      = at_mqtt_conn_callback;
            at_custom_cmd(wifi_ctx.at_obj, &attr, at_mqtt_conn_sender);
            wifi_ctx.init_step = AT_IDLE;
            wifi_led_update();
            break;
        case AT_MQTT_SUB:
            at_attr_deinit(&attr);
            attr.retry   = 3;
            attr.timeout = 10000;
            attr.cb      = at_mqtt_sub_callback;
            at_custom_cmd(wifi_ctx.at_obj, &attr, at_mqtt_sub_sender);
            wifi_ctx.init_step = AT_IDLE;
            wifi_led_update();
            break;
        default:
            wifi_led_update();
            xTimerStop(init_process_timer, 0);
            xTimerDelete(init_process_timer, 0);
            break;
    }
}

// ===================== 定时器回调 =====================
/**
 * @brief AT命令处理定时器回调，周期性驱动AT命令状态机
 */
static void at_process_timer_cb(TimerHandle_t xTimer)
{
    if (wifi_ctx.at_obj) {
        at_obj_process(wifi_ctx.at_obj);
    }
}

/**
 * @brief 初始化流程定时器回调，周期性推进WiFi初始化状态机
 */
static void init_process_timer_cb(TimerHandle_t xTimer)
{
    wifi_init_config();
}

// ===================== 任务外部接口 =====================
/**
 * @brief 任务外部接口，异步请求发布MQTT数据
 * @param data 待发布数据
 */
void wifi_task_send_mqtt_data(const char *data)
{
    if (mqtt_pub_queue && data) {
        xQueueSend(mqtt_pub_queue, data, 0);
    }
}

// ===================== WiFi任务主函数 =====================
/**
 * @brief WiFi任务主循环，负责处理MQTT数据队列
 */
static void wifi_task_entry(void *pvParameters)
{
    for (;;) {
        // 处理MQTT数据发布请求
        // char pub_data[128];
        // if (xQueueReceive(mqtt_pub_queue, pub_data, pdMS_TO_TICKS(10)) == pdPASS) {
        //     strncpy((char *)wifi_ctx.mqtt_pub_data, pub_data, sizeof(wifi_ctx.mqtt_pub_data) - 1);
        //     wifi_ctx.mqtt_pub_data[sizeof(wifi_ctx.mqtt_pub_data) - 1] = '\0';
        //     at_attr_t attr;
        //     at_attr_deinit(&attr);
        //     attr.retry   = 3;
        //     attr.timeout = 5000;
        //     at_custom_cmd(wifi_ctx.at_obj, &attr, at_mqtt_pub_sender);
        //     WIFI_LOG_INFO("通过队列发布MQTT: %s", pub_data);
        // }
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}

/**
 * @brief WiFi任务初始化，创建定时器、队列并启动主任务
 */
void wifi_task_init(void)
{
    WIFI_USART_CONFIG(WIFI_USART_BAUDRATE);

    // 创建AT互斥量
    at_mutex = xSemaphoreCreateMutex();

    // 1. 初始化AT对象
    wifi_ctx.at_obj = at_obj_create(&wifi_at_adapter);
    if (!wifi_ctx.at_obj) {
        WIFI_LOG_ERROR("AT对象创建失败");
        vTaskDelete(NULL);
    }
    // 2. 注册URC表、初始化参数
    at_obj_set_urc(wifi_ctx.at_obj, wifi_urc_table, sizeof(wifi_urc_table) / sizeof(wifi_urc_table[0]));
    wifi_ctx.mqtt_pub_qos       = QOS0;
    wifi_ctx.mqtt_pub_retain    = 0;
    wifi_ctx.init_step          = AT_TEST;
    wifi_ctx.use_saved_param    = false;
    wifi_ctx.connect_start_time = 0;
    // 3. 创建定时器、队列
    at_process_timer = xTimerCreate("ATProc", pdMS_TO_TICKS(WIFI_AT_PROCESS_INTERVAL), pdTRUE, NULL, at_process_timer_cb);
    if (at_process_timer == NULL) {
        WIFI_LOG_ERROR("wifi_task_init: xTimerCreate failed");
    } else {
        xTimerStart(at_process_timer, 0);
    }
    init_process_timer = xTimerCreate("InitProc", pdMS_TO_TICKS(WIFI_INIT_PROCESS_INTERVAL), pdTRUE, NULL, init_process_timer_cb);
    if (init_process_timer == NULL) {
        WIFI_LOG_ERROR("wifi_task_init: xTimerCreate failed");
    } else {
        xTimerStart(init_process_timer, 0);
    }

    // mqtt_pub_queue     = xQueueCreate(4, 128);

    // 5. 启动主任务循环
    BaseType_t xReturn = xTaskCreate(wifi_task_entry,
                                     "wifi_task",
                                     APP_WIFI_TASK_STACK_SIZE,
                                     NULL,
                                     APP_WIFI_PRIO,
                                     NULL);
    if (xReturn != pdPASS) {
        WIFI_LOG_ERROR("WiFi任务创建失败");
    }
    LED_off(LED_WIFI);
}

/**
 * @brief 将16进制字符串转为字节流
 * @param hex_str 输入的16进制字符串
 * @param out_buf 输出的字节流缓冲区
 * @param out_len 输出字节数
 * @return 实际转换的字节数
 */
static int hexstr_to_bytes(const char *hex_str, uint8_t *out_buf, int out_len)
{
    int i = 0;
    while (hex_str[0] && hex_str[1] && i < out_len) {
        sscanf(hex_str, "%2hhx", &out_buf[i]);
        hex_str += 2;
        i++;
    }
    return i;
}