#include "bsp_buzzer.h"
#include "bsp_pwm.h"

// 音色控制相关变量
static uint8_t g_buzzer_attack     = 10;                 // 起音时间(ms)
static uint8_t g_buzzer_decay      = 20;                 // 衰减时间(ms)
static uint8_t g_buzzer_sustain    = 80;                 // 持续音量(0-100)
static uint8_t g_buzzer_release    = 200;                // 释放时间(ms)
static buzzer_tone_t g_buzzer_tone = BUZZER_TONE_SQUARE; // 当前音色
static uint8_t g_buzzer_mod_depth  = 0;                  // 调制深度(0-100)
static uint8_t g_buzzer_mod_rate   = 0;                  // 调制速率(0-100)

// 正弦波表（256点，0-255范围）
static const uint8_t g_sin_table[256] = {
    128, 131, 134, 137, 140, 143, 146, 149, 152, 155, 158, 162, 165, 167, 170, 173,
    176, 179, 182, 185, 188, 190, 193, 196, 198, 201, 203, 206, 208, 211, 213, 215,
    218, 220, 222, 224, 226, 228, 230, 232, 234, 235, 237, 238, 240, 241, 243, 244,
    245, 246, 248, 249, 250, 250, 251, 252, 253, 253, 254, 254, 254, 255, 255, 255,
    255, 255, 255, 255, 254, 254, 254, 253, 253, 252, 251, 250, 250, 249, 248, 246,
    245, 244, 243, 241, 240, 238, 237, 235, 234, 232, 230, 228, 226, 224, 222, 220,
    218, 215, 213, 211, 208, 206, 203, 201, 198, 196, 193, 190, 188, 185, 182, 179,
    176, 173, 170, 167, 165, 162, 158, 155, 152, 149, 146, 143, 140, 137, 134, 131,
    128, 124, 121, 118, 115, 112, 109, 106, 103, 100, 97, 93, 90, 88, 85, 82,
    79, 76, 73, 70, 67, 65, 62, 59, 57, 54, 52, 49, 47, 44, 42, 40,
    37, 35, 33, 31, 29, 27, 25, 23, 21, 20, 18, 17, 15, 14, 12, 11,
    10, 9, 7, 6, 5, 5, 4, 3, 2, 2, 1, 1, 1, 0, 0, 0,
    0, 0, 0, 0, 1, 1, 1, 2, 2, 3, 4, 5, 5, 6, 7, 9,
    10, 11, 12, 14, 15, 17, 18, 20, 21, 23, 25, 27, 29, 31, 33, 35,
    37, 40, 42, 44, 47, 49, 52, 54, 57, 59, 62, 65, 67, 70, 73, 76,
    79, 82, 85, 88, 90, 93, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124};

/**
 * @brief ATIM0初始化函数 - 用于蜂鸣器PWM控制
 */
void bsp_BuzzerPwmInit(void)
{
    stc_gpio_init_t stcGpioInit;
    uint16_t u16CntValue;
    stc_atim0_mode23_cfg_t stcAtim0BaseCfg         = {0};
    stc_atim0_m23_compare_cfg_t stcAtim0ChxBCmpCfg = {0};

    // 使能外设时钟
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpioa);
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralATim0);

    // 配置PA11引脚(ATIM0_CH0B)为复用功能
    DDL_ZERO_STRUCT(stcGpioInit);
    stcGpioInit.u32Pin         = GPIO_PIN_11;         // PA11
    stcGpioInit.u32Mode        = GPIO_MODE_OUTPUT_PP; // 先设置为推挽输出
    stcGpioInit.u32DriverLevel = GPIO_Driver_Level_H; // 高驱动能力
    stcGpioInit.u32PullUpDn    = GPIO_PULL_NONE;      // 无上下拉
    GPIOA_Init(&stcGpioInit);

    // 设置PA11为ATIM0_CH0B功能
    GPIO_PA11_RESET();             // 端口初始电平->低
    GPIO_PA11_AF_ATIM0_CH0B_SET(); // PA11复用为ATIM0_CH0B

    // 配置ATIM0参数，工作在PWM模式
    DDL_ZERO_STRUCT(stcAtim0BaseCfg);                                            // 结构体初始化清零
    stcAtim0BaseCfg.u32WorkMode    = ATIM0_M23_M23CR_WORK_MODE_SAWTOOTH;         // 锯齿波模式
    stcAtim0BaseCfg.u32CountClkSel = ATIM0_M23_M23CR_CT_PCLK;                    // 定时器功能，计数时钟为内部PCLK
    stcAtim0BaseCfg.u32PRS         = ATIM0_M23_M23CR_ATIM0CLK_PRS4;              // PCLK/4=12MHz (假设PCLK=48MHz)
    stcAtim0BaseCfg.u32CntDir      = ATIM0_M23_M23CR_DIR_UP_CNT;                 // 向上计数，在三角波模式时只读
    stcAtim0BaseCfg.u32PWMTypeSel  = ATIM0_M23_M23CR_COMP_PWM_INDEPT;            // 独立输出PWM
    stcAtim0BaseCfg.u32PWM2sSel    = ATIM0_M23_M23CR_PWM2S_COMPARE_SINGLE_POINT; // 单点比较功能
    stcAtim0BaseCfg.u32ShotMode    = ATIM0_M23_M23CR_SHOT_CYCLE;                 // 循环计数
    stcAtim0BaseCfg.u32URSSel      = ATIM0_M23_M23CR_URS_OV_UND;                 // 上下溢更新
    ATIM0_Mode23_Init(&stcAtim0BaseCfg);                                         // ATIM0的模式23功能初始化

    // 设置自动重载值(ARR) - 根据蜂鸣器频率计算
    ATIM0_Mode23_ARRSet(BUZZER_PWM_PERIOD);
    ATIM0_Mode23_ARR_Buffer_Enable(TRUE); // 设置重载值,并使能缓存

    // 设置比较值为50%占空比
    ATIM0_Mode23_Channel_Compare_Value_Set(ATIM0_COMPARE_CAPTURE_CH0B, BUZZER_PWM_PERIOD / 2);

    // 配置CH0B通道输出
    DDL_ZERO_STRUCT(stcAtim0ChxBCmpCfg);                                       // 结构体初始化清零
    stcAtim0ChxBCmpCfg.u32CHxCmpCap      = ATIM0_M23_CRCHx_CSA_CSB_COMPARE;    // 比较模式
    stcAtim0ChxBCmpCfg.u32CHxCmpModeCtrl = ATIM0_M23_FLTR_OCMxx_PWM_MODE_2;    // OCREFB输出控制OCMB:PWM模式2
    stcAtim0ChxBCmpCfg.u32CHxPolarity    = ATIM0_M23_FLTR_CCPxx_NORMAL_IN_OUT; // 正常输出
    stcAtim0ChxBCmpCfg.u32CHxCmpBufEn    = ATIM0_M23_CRCHx_BUFEx_ENABLE;       // B通道缓存控制使能
    stcAtim0ChxBCmpCfg.u32CHxCmpIntSel   = ATIM0_M23_CRCHx_CISBx_DISABLE_IRQ;  // B通道比较中断控制:无
    ATIM0_Mode23_PortOutput_CHB_Cfg(&stcAtim0ChxBCmpCfg);                      // CHB比较输出端口配置

    // 设置初始计数值
    u16CntValue = 0;
    ATIM0_Mode23_Cnt16Set(u16CntValue); // 设置计数初值

    // 启动定时器
    ATIM0_Mode23_Run(); // 运行

    // 初始状态下不开启PWM输出
    ATIM0_Mode23_Manual_PWM_Output_Enable(FALSE); // 端口输出禁止
}

/**
 * @brief 蜂鸣器端口初始化
 */
static void bsp_buzzer_port_init(void)
{
    stc_gpio_init_t stcGpioInit;

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpiod);

    DDL_ZERO_STRUCT(stcGpioInit);                     /* 结构体初始化清零 */
    stcGpioInit.u32Pin         = BUZZER_PWR_PIN;      /* 端口 */
    stcGpioInit.u32Mode        = GPIO_MODE_OUTPUT_PP; /* 端口方向配置->推挽输出 */
    stcGpioInit.u32DriverLevel = GPIO_Driver_Level_H; /* 端口驱动能力配置->高驱动能力 */
    stcGpioInit.u32PullUpDn    = GPIO_PULL_NONE;      /* 端口上下拉配置->无上下拉 */
    GPIO_Init(BUZZER_PWR_PORT, &stcGpioInit);         /* 端口初始化 */

    // 初始状态为关闭
    BUZZER_PWR_OFF();
}

/**
 * @brief 蜂鸣器初始化
 */
void bsp_BuzzerInit(void)
{
    // 初始化蜂鸣器端口
    bsp_buzzer_port_init();

    // 初始化PWM
    bsp_BuzzerPwmInit();
}

/**
 * @brief 开启蜂鸣器
 *
 * @param freq 蜂鸣器频率(Hz)
 */
void bsp_BuzzerOn(uint16_t freq)
{
    // 设置蜂鸣器频率，使用方波(50%占空比)
    bsp_BuzzerSetFreq(freq, 50, 0, 0);

    // 开启蜂鸣器电源
    BUZZER_PWR_ON();

    // 开启PWM输出
    ATIM0_Mode23_Manual_PWM_Output_Enable(TRUE);

    // 初始化ADSR状态
    static uint32_t adsr_time = 0;
    static uint8_t adsr_phase = 0;
    adsr_time                 = 0;
    adsr_phase                = 0;
}

/**
 * @brief 关闭蜂鸣器
 */
void bsp_BuzzerOff(void)
{
    // 关闭PWM输出
    ATIM0_Mode23_Manual_PWM_Output_Enable(FALSE);

    // 关闭蜂鸣器电源
    BUZZER_PWR_OFF();

    // 重置ADSR状态
    static uint32_t adsr_time = 0;
    static uint8_t adsr_phase = 0;
    adsr_time                 = 0;
    adsr_phase                = 0;
}

/**
 * @brief 设置ADSR包络
 *
 * @param attack 起音时间(ms)
 * @param decay 衰减时间(ms)
 * @param sustain 持续音量(0-100)
 * @param release 释放时间(ms)
 */
void bsp_BuzzerSetADSR(uint8_t attack, uint8_t decay, uint8_t sustain, uint8_t release)
{
    g_buzzer_attack  = attack;
    g_buzzer_decay   = decay;
    g_buzzer_sustain = sustain;
    g_buzzer_release = release;
}

/**
 * @brief 设置蜂鸣器频率
 * @param freq 频率，单位Hz
 * @param duty 占空比(0-100)
 */
void bsp_BuzzerSetFreq(uint16_t freq, uint8_t duty, uint16_t elapsed_time_ms, uint16_t note_duration_ms)
{
    // 确保非零频率
    if (freq == 0) {
        ATIM0_Mode23_Manual_PWM_Output_Enable(FALSE);
        return;
    }

    // 限制频率范围
    if (freq < BUZZER_FREQ_MIN)
        freq = BUZZER_FREQ_MIN;
    else if (freq > BUZZER_FREQ_MAX)
        freq = BUZZER_FREQ_MAX;

    // 计算PWM周期，使用浮点数保持精度
    uint16_t period = (uint16_t)(12000000.0f / freq - 1.0f);

    // 先根据音色类型给基础duty
    switch (g_buzzer_tone) {
        case BUZZER_TONE_SQUARE: duty = 50; break;
        case BUZZER_TONE_PULSE:  duty = 25; break;
        case BUZZER_TONE_BRIGHT: duty = 75; break;
        case BUZZER_TONE_SOFT:   duty = 40; break;
    }

    // 计算ADSR包络值(0-100)
    uint8_t adsr_value = 100;
    if (elapsed_time_ms < g_buzzer_attack) {
        // Attack阶段
        adsr_value = (elapsed_time_ms * 100) / (g_buzzer_attack ? g_buzzer_attack : 1);
    } else if (elapsed_time_ms < (g_buzzer_attack + g_buzzer_decay)) {
        // Decay阶段
        uint16_t decay_time = elapsed_time_ms - g_buzzer_attack;
        adsr_value = 100 - ((decay_time * (100 - g_buzzer_sustain)) / (g_buzzer_decay ? g_buzzer_decay : 1));
    } else if (elapsed_time_ms < (note_duration_ms > g_buzzer_release ? (note_duration_ms - g_buzzer_release) : note_duration_ms)) {
        // Sustain阶段
        adsr_value = g_buzzer_sustain;
    } else if (elapsed_time_ms < note_duration_ms) {
        // Release阶段
        uint16_t release_time = elapsed_time_ms - (note_duration_ms > g_buzzer_release ? (note_duration_ms - g_buzzer_release) : note_duration_ms);
        adsr_value = (g_buzzer_sustain * (g_buzzer_release - release_time)) / (g_buzzer_release ? g_buzzer_release : 1);
    } else {
        adsr_value = 0;
    }

    // 用ADSR包络修饰duty
    duty = (duty * adsr_value) / 100;

    // 应用调制效果
    if (g_buzzer_mod_depth > 0 && g_buzzer_mod_rate > 0) {
        uint8_t mod_value = (g_buzzer_mod_depth * g_sin_table[(elapsed_time_ms * g_buzzer_mod_rate) % 256]) / 255;
        duty = duty + ((duty * mod_value) / 100);
    }

    // 确保占空比在有效范围内
    if (duty < 10) duty = 10;
    if (duty > 95) duty = 95;

    // 计算比较值
    uint16_t compare_value = (period * duty) / 100;

    // 配置PWM参数
    ATIM0_Mode23_ARRSet(period);
    ATIM0_Mode23_ARR_Buffer_Enable(TRUE);
    ATIM0_Mode23_Channel_Compare_Value_Set(ATIM0_COMPARE_CAPTURE_CH0B, compare_value);

    // 确保定时器运行
    ATIM0_Mode23_Run();
}
