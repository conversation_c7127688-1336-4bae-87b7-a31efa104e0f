/**
 * @file buzzer_music.c
 * @brief 蜂鸣器音乐数据实现
 */
#include "buzzer_music.h"

// 提示音音符定义（上行琶音+和弦，明快上扬）
const buzzer_note_t g_alert_notes[] = {
    {BUZZER_NOTE_C6, 100, 90, 1, 0, 10, 40, 85, 200, BUZZER_TONE_BRIGHT}, // 主音，快速起音，中等衰减
    {BUZZER_NOTE_E6, 100, 90, 1, 0, 10, 40, 85, 200, BUZZER_TONE_BRIGHT}, // 大三度，保持连贯
    {BUZZER_NOTE_G6, 100, 90, 1, 0, 10, 40, 85, 200, BUZZER_TONE_BRIGHT}, // 纯五度，保持连贯
    {BUZZER_NOTE_C7, 200, 90, 0, 0, 10, 40, 85, 400, BUZZER_TONE_BRIGHT}, // 高八度，延长尾音
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                       // 结束标记
};

// 成功提示音音符定义（上行三和弦，明亮欢快）
const buzzer_note_t g_success_notes[] = {
    {BUZZER_NOTE_C6, 100, 90, 1, 0, 10, 40, 85, 200, BUZZER_TONE_BRIGHT}, // 主音
    {BUZZER_NOTE_E6, 100, 90, 1, 0, 10, 40, 85, 200, BUZZER_TONE_BRIGHT}, // 大三度
    {BUZZER_NOTE_G6, 150, 90, 0, 0, 10, 40, 85, 300, BUZZER_TONE_BRIGHT}, // 纯五度，结尾
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                       // 结束标记
};

// 错误提示音音符定义（下行小三度，紧张感）
const buzzer_note_t g_error_notes[] = {
    {BUZZER_NOTE_E6, 150, 90, 1, 0, 15, 50, 85, 200, BUZZER_TONE_PULSE}, // 小三度高音
    {BUZZER_NOTE_C6, 200, 90, 0, 0, 15, 50, 85, 400, BUZZER_TONE_PULSE}, // 主音
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                      // 结束标记
};

// 开机提示音音符定义（完整大调琶音，华丽）
const buzzer_note_t g_power_on_notes[] = {
    {BUZZER_NOTE_C5, 80, 90, 1, 0, 10, 30, 85, 150, BUZZER_TONE_SOFT},    // 低八度主音
    {BUZZER_NOTE_E5, 80, 90, 1, 0, 10, 30, 85, 150, BUZZER_TONE_SOFT},    // 低八度大三度
    {BUZZER_NOTE_G5, 80, 90, 1, 0, 10, 30, 85, 150, BUZZER_TONE_SOFT},    // 低八度纯五度
    {BUZZER_NOTE_C6, 80, 90, 1, 0, 10, 30, 85, 150, BUZZER_TONE_SOFT},    // 主音
    {BUZZER_NOTE_E6, 80, 90, 1, 0, 10, 30, 85, 150, BUZZER_TONE_SOFT},    // 大三度
    {BUZZER_NOTE_G6, 80, 90, 1, 0, 10, 30, 85, 150, BUZZER_TONE_SOFT},    // 纯五度
    {BUZZER_NOTE_C7, 200, 90, 0, 0, 10, 30, 85, 500, BUZZER_TONE_BRIGHT}, // 高八度，延长音
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                       // 结束标记
};

// 按键点击音符定义（高音大三度，清脆）
const buzzer_note_t g_key_click_notes[] = {
    {BUZZER_NOTE_G6, 30, 90, 1, 0, 5, 40, 80, 1000, BUZZER_TONE_PULSE}, // 纯五度，快速起音，超长拖音
    {BUZZER_NOTE_C7, 40, 90, 0, 0, 5, 40, 80, 1200, BUZZER_TONE_PULSE}, // 高八度，快速起音，更长拖音
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                     // 结束标记
};

// 按键长按音符定义（上行琶音，渐强）
const buzzer_note_t g_key_long_press_notes[] = {
    {BUZZER_NOTE_C6, 100, 90, 1, 0, 5, 40, 80, 1000, BUZZER_TONE_SOFT}, // 主音，快速起音，超长拖音
    {BUZZER_NOTE_E6, 100, 90, 1, 0, 5, 40, 80, 1000, BUZZER_TONE_SOFT}, // 大三度，快速起音，超长拖音
    {BUZZER_NOTE_G6, 200, 90, 0, 0, 5, 40, 80, 1500, BUZZER_TONE_SOFT}, // 纯五度，快速起音，最长拖音
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                     // 结束标记
};

// 按键释放音符定义（下行大三度，轻快）
const buzzer_note_t g_key_release_notes[] = {
    {BUZZER_NOTE_G6, 30, 90, 1, 0, 5, 40, 80, 1000, BUZZER_TONE_SOFT}, // 纯五度，快速起音，超长拖音
    {BUZZER_NOTE_C7, 40, 90, 0, 0, 5, 40, 80, 1200, BUZZER_TONE_SOFT}, // 高音主音，快速起音，更长拖音
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                    // 结束标记
};

// 门铃音效定义（大三和弦，明亮欢快）
const buzzer_note_t g_doorbell_notes[] = {
    {BUZZER_NOTE_C5, 100, 90, 1, 0, 5, 20, 85, 200, BUZZER_TONE_BRIGHT}, // 低音主音
    {BUZZER_NOTE_E5, 100, 90, 1, 0, 5, 20, 85, 200, BUZZER_TONE_BRIGHT}, // 大三度
    {BUZZER_NOTE_G5, 200, 90, 0, 0, 5, 20, 85, 400, BUZZER_TONE_BRIGHT}, // 纯五度，延长音
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                      // 结束标记
};

// 消息提醒音效定义（上行琶音，清脆）
const buzzer_note_t g_notify_notes[] = {
    {BUZZER_NOTE_C6, 50, 90, 1, 0, 5, 15, 85, 100, BUZZER_TONE_PULSE},  // 主音
    {BUZZER_NOTE_E6, 50, 90, 1, 0, 5, 15, 85, 100, BUZZER_TONE_PULSE},  // 大三度
    {BUZZER_NOTE_G6, 50, 90, 1, 0, 5, 15, 85, 100, BUZZER_TONE_PULSE},  // 纯五度
    {BUZZER_NOTE_C7, 100, 90, 0, 0, 5, 15, 85, 200, BUZZER_TONE_PULSE}, // 高八度，延长音
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                     // 结束标记
};

// 设备启动音效定义（完整大调琶音，华丽）
const buzzer_note_t g_startup_notes[] = {
    {BUZZER_NOTE_C4, 100, 90, 1, 0, 10, 30, 85, 200, BUZZER_TONE_SOFT},   // 低音主音
    {BUZZER_NOTE_E4, 100, 90, 1, 0, 10, 30, 85, 200, BUZZER_TONE_SOFT},   // 大三度
    {BUZZER_NOTE_G4, 100, 90, 1, 0, 10, 30, 85, 200, BUZZER_TONE_SOFT},   // 纯五度
    {BUZZER_NOTE_C5, 100, 90, 1, 0, 10, 30, 85, 200, BUZZER_TONE_SOFT},   // 中音主音
    {BUZZER_NOTE_E5, 100, 90, 1, 0, 10, 30, 85, 200, BUZZER_TONE_SOFT},   // 中音大三度
    {BUZZER_NOTE_G5, 200, 90, 0, 0, 10, 30, 85, 400, BUZZER_TONE_BRIGHT}, // 中音纯五度，延长音
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                       // 结束标记
};

// 设备关闭音效定义（下行小三和弦，柔和）
const buzzer_note_t g_shutdown_notes[] = {
    {BUZZER_NOTE_G5, 100, 90, 1, 0, 10, 40, 85, 200, BUZZER_TONE_SOFT}, // 中音纯五度
    {BUZZER_NOTE_E5, 100, 90, 1, 0, 10, 40, 85, 200, BUZZER_TONE_SOFT}, // 中音小三度
    {BUZZER_NOTE_C5, 200, 90, 0, 0, 10, 40, 85, 400, BUZZER_TONE_SOFT}, // 中音主音，延长音
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                     // 结束标记
};

// 模式切换音效定义（大三度转位，活泼）
const buzzer_note_t g_mode_switch_notes[] = {
    {BUZZER_NOTE_C5, 50, 90, 1, 0, 5, 20, 85, 100, BUZZER_TONE_PULSE},  // 主音
    {BUZZER_NOTE_G5, 50, 90, 1, 0, 5, 20, 85, 100, BUZZER_TONE_PULSE},  // 纯五度
    {BUZZER_NOTE_E5, 100, 90, 0, 0, 5, 20, 85, 200, BUZZER_TONE_PULSE}, // 大三度，延长音
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                     // 结束标记
};

// 场景触发音效定义（完整和弦，丰富）
const buzzer_note_t g_scene_notes[] = {
    {BUZZER_NOTE_C5, 80, 90, 1, 0, 10, 30, 85, 150, BUZZER_TONE_SOFT},    // 主音
    {BUZZER_NOTE_E5, 80, 90, 1, 0, 10, 30, 85, 150, BUZZER_TONE_SOFT},    // 大三度
    {BUZZER_NOTE_G5, 80, 90, 1, 0, 10, 30, 85, 150, BUZZER_TONE_SOFT},    // 纯五度
    {BUZZER_NOTE_C6, 200, 90, 0, 0, 10, 30, 85, 400, BUZZER_TONE_BRIGHT}, // 高八度，延长音
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                       // 结束标记
};

// WiFi搜索网络音效定义（脉冲音，表示搜索中）
const buzzer_note_t g_wifi_scan_notes[] = {
    {BUZZER_NOTE_C5, 30, 90, 1, 0, 5, 10, 80, 50, BUZZER_TONE_PULSE}, // 短促脉冲
    {BUZZER_NOTE_E5, 30, 90, 1, 0, 5, 10, 80, 50, BUZZER_TONE_PULSE}, // 短促脉冲
    {BUZZER_NOTE_G5, 30, 90, 1, 0, 5, 10, 80, 50, BUZZER_TONE_PULSE}, // 短促脉冲
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                   // 结束标记
};

// WiFi连接中音效定义（渐进音，表示连接过程）
const buzzer_note_t g_wifi_connecting_notes[] = {
    {BUZZER_NOTE_C5, 100, 90, 1, 0, 10, 20, 85, 200, BUZZER_TONE_SOFT}, // 主音
    {BUZZER_NOTE_E5, 100, 90, 1, 0, 10, 20, 85, 200, BUZZER_TONE_SOFT}, // 大三度
    {BUZZER_NOTE_G5, 100, 90, 1, 0, 10, 20, 85, 200, BUZZER_TONE_SOFT}, // 纯五度
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                     // 结束标记
};

// WiFi连接成功音效定义（上行琶音，明亮欢快）
const buzzer_note_t g_wifi_connected_notes[] = {
    {BUZZER_NOTE_C5, 80, 90, 1, 0, 5, 15, 85, 150, BUZZER_TONE_BRIGHT},  // 主音
    {BUZZER_NOTE_E5, 80, 90, 1, 0, 5, 15, 85, 150, BUZZER_TONE_BRIGHT},  // 大三度
    {BUZZER_NOTE_G5, 80, 90, 1, 0, 5, 15, 85, 150, BUZZER_TONE_BRIGHT},  // 纯五度
    {BUZZER_NOTE_C6, 200, 90, 0, 0, 5, 15, 85, 400, BUZZER_TONE_BRIGHT}, // 高八度，延长音
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                      // 结束标记
};

// WiFi连接失败音效定义（下行小三度，紧张感）
const buzzer_note_t g_wifi_failed_notes[] = {
    {BUZZER_NOTE_E5, 150, 90, 1, 0, 10, 30, 85, 200, BUZZER_TONE_PULSE}, // 小三度高音
    {BUZZER_NOTE_C5, 200, 90, 0, 0, 10, 30, 85, 400, BUZZER_TONE_PULSE}, // 主音
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                      // 结束标记
};

// 蓝牙配对开始音效定义（清脆短促音）
const buzzer_note_t g_remote_start_notes[] = {
    {BUZZER_NOTE_C6, 30, 90, 1, 0, 5, 10, 80, 50, BUZZER_TONE_BRIGHT}, // 高音主音，清脆短促
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                    // 结束标记
};

// 蓝牙配对中音效定义（柔和渐进和弦）
const buzzer_note_t g_remote_pairing_notes[] = {
    {BUZZER_NOTE_C5, 100, 90, 1, 0, 10, 20, 85, 200, BUZZER_TONE_SOFT},   // 主音，柔和起音
    {BUZZER_NOTE_E5, 100, 90, 1, 0, 10, 20, 85, 200, BUZZER_TONE_SOFT},   // 大三度，柔和起音
    {BUZZER_NOTE_G5, 100, 90, 1, 0, 10, 20, 85, 200, BUZZER_TONE_SOFT},   // 纯五度，柔和起音
    {BUZZER_NOTE_C6, 100, 90, 0, 0, 10, 20, 85, 300, BUZZER_TONE_SOFT},   // 高八度，柔和延长
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                       // 结束标记
};

// 蓝牙配对成功音效定义（明亮上行琶音）
const buzzer_note_t g_remote_success_notes[] = {
    {BUZZER_NOTE_C5, 80, 90, 1, 0, 5, 15, 85, 150, BUZZER_TONE_BRIGHT},   // 主音，明亮起音
    {BUZZER_NOTE_E5, 80, 90, 1, 0, 5, 15, 85, 150, BUZZER_TONE_BRIGHT},   // 大三度，明亮起音
    {BUZZER_NOTE_G5, 80, 90, 1, 0, 5, 15, 85, 150, BUZZER_TONE_BRIGHT},   // 纯五度，明亮起音
    {BUZZER_NOTE_C6, 200, 90, 0, 0, 5, 15, 85, 400, BUZZER_TONE_BRIGHT},  // 高八度，明亮延长
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                       // 结束标记
};

// 蓝牙配对失败音效定义（柔和下行小三度）
const buzzer_note_t g_remote_failed_notes[] = {
    {BUZZER_NOTE_E5, 150, 90, 1, 0, 10, 30, 85, 200, BUZZER_TONE_SOFT},   // 小三度高音，柔和起音
    {BUZZER_NOTE_C5, 200, 90, 0, 0, 10, 30, 85, 400, BUZZER_TONE_SOFT},   // 主音，柔和延长
    {0, 0, 0, 0, 0, 0, 0, 0, 0, BUZZER_TONE_SQUARE}                       // 结束标记
};

// 预定义音乐数组
static const buzzer_music_t g_music_data[] = {
    {"PowerOn",
     "开机音效",
     g_power_on_notes,
     sizeof(g_power_on_notes) / sizeof(g_power_on_notes[0])},
    {"Alert",
     "提示音",
     g_alert_notes,
     sizeof(g_alert_notes) / sizeof(g_alert_notes[0])},
    {"Success",
     "成功音",
     g_success_notes,
     sizeof(g_success_notes) / sizeof(g_success_notes[0])},
    {"Error",
     "错误音",
     g_error_notes,
     sizeof(g_error_notes) / sizeof(g_error_notes[0])},
    {"KeyClick",
     "按键点击音",
     g_key_click_notes,
     sizeof(g_key_click_notes) / sizeof(g_key_click_notes[0])},
    {"KeyLong",
     "按键长按音",
     g_key_long_press_notes,
     sizeof(g_key_long_press_notes) / sizeof(g_key_long_press_notes[0])},
    {"KeyRelease",
     "按键释放音",
     g_key_release_notes,
     sizeof(g_key_release_notes) / sizeof(g_key_release_notes[0])},
    {"Doorbell",
     "门铃音效",
     g_doorbell_notes,
     sizeof(g_doorbell_notes) / sizeof(g_doorbell_notes[0])},
    {"Notify",
     "消息提醒",
     g_notify_notes,
     sizeof(g_notify_notes) / sizeof(g_notify_notes[0])},
    {"Startup",
     "设备启动",
     g_startup_notes,
     sizeof(g_startup_notes) / sizeof(g_startup_notes[0])},
    {"Shutdown",
     "设备关闭",
     g_shutdown_notes,
     sizeof(g_shutdown_notes) / sizeof(g_shutdown_notes[0])},
    {"ModeSwitch",
     "模式切换",
     g_mode_switch_notes,
     sizeof(g_mode_switch_notes) / sizeof(g_mode_switch_notes[0])},
    {"Scene",
     "场景触发",
     g_scene_notes,
     sizeof(g_scene_notes) / sizeof(g_scene_notes[0])},
    {"WifiScan",
     "WiFi搜索网络",
     g_wifi_scan_notes,
     sizeof(g_wifi_scan_notes) / sizeof(g_wifi_scan_notes[0])},
    {"WifiConnecting",
     "WiFi连接中",
     g_wifi_connecting_notes,
     sizeof(g_wifi_connecting_notes) / sizeof(g_wifi_connecting_notes[0])},
    {"WifiConnected",
     "WiFi连接成功",
     g_wifi_connected_notes,
     sizeof(g_wifi_connected_notes) / sizeof(g_wifi_connected_notes[0])},
    {"WifiFailed",
     "WiFi连接失败",
     g_wifi_failed_notes,
     sizeof(g_wifi_failed_notes) / sizeof(g_wifi_failed_notes[0])},
    {"RemoteStart",
     "蓝牙配对开始",
     g_remote_start_notes,
     sizeof(g_remote_start_notes) / sizeof(g_remote_start_notes[0])},
    {"RemotePairing",
     "蓝牙配对中",
     g_remote_pairing_notes,
     sizeof(g_remote_pairing_notes) / sizeof(g_remote_pairing_notes[0])},
    {"RemoteSuccess",
     "蓝牙配对成功",
     g_remote_success_notes,
     sizeof(g_remote_success_notes) / sizeof(g_remote_success_notes[0])},
    {"RemoteFailed",
     "蓝牙配对失败",
     g_remote_failed_notes,
     sizeof(g_remote_failed_notes) / sizeof(g_remote_failed_notes[0])}};

/**
 * @brief 获取预定义音乐数据
 *
 * @param type 音乐类型
 * @return const buzzer_music_t* 音乐数据指针
 */
const buzzer_music_t *Buzzer_GetMusicData(buzzer_music_type_t type)
{
    if (type < BUZZER_MUSIC_COUNT) {
        return &g_music_data[type];
    }
    return NULL;
}