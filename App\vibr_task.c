/**
 * @file vibr_task.c
 * <AUTHOR> (<EMAIL>)
 * @brief 振动电机任务实现
 * @version 0.1
 * @date 2025-05-06
 *
 * @copyright Copyright (c) 2025
 *
 */
#include "vibr_task.h"

#define VIBR_EVENT_QUEUE_LEN 10

// ================== 静态变量 ==================
// 振动电机状态数组
static VibrState s_vibrState[VIBR_ID_MAX];
// 事件队列句柄
static QueueHandle_t s_vibrEventQueue = NULL;
// 任务句柄
static TaskHandle_t s_vibrTaskHandle = NULL;
// 全局定时器句柄
static TimerHandle_t s_vibrGlobalTimer = NULL;

// ================== 内部函数声明 ==================
static void vibr_global_timer_cb(TimerHandle_t xTimer); // 全局定时器回调
static void vibr_task_entry(void *param);               // 任务主循环
static void vibr_handle_event(const VibrEventMsg *msg); // 事件分发处理
static void vibr_reset_motor(uint8_t id);               // 电机状态复位
static void vibr_force_stop(uint8_t id);

/**
 * @brief 启动单个振动电机（带强度档位）
 * @param id 电机ID
 * @param mode 振动模式
 * @param timeout 超时时间(ms)
 * @param level 振动强度档位
 */
void Vibr_StartWithLevel(VibrID id, VibrMode mode, uint32_t timeout, VibrStrengthLevel level)
{
    VibrEventMsg msg       = {0};
    msg.type               = VIBR_EVT_START;
    msg.data.start.idMask  = (1 << id);
    msg.data.start.mode    = mode;
    msg.data.start.timeout = timeout;
    msg.data.start.level   = level;
    xQueueSend(s_vibrEventQueue, &msg, 0);
}

/**
 * @brief 启动多个振动电机（带强度档位）
 * @param ids 电机ID数组
 * @param count 数量
 * @param mode 振动模式
 * @param timeout 超时时间(ms)
 * @param level 振动强度档位
 */
void Vibr_StartMultipleWithLevel(const VibrID *ids, uint8_t count, VibrMode mode, uint32_t timeout, VibrStrengthLevel level)
{
    VibrEventMsg msg       = {0};
    msg.type               = VIBR_EVT_START;
    msg.data.start.idMask  = Vibr_ConvertIdsToBitMask(ids, count);
    msg.data.start.mode    = mode;
    msg.data.start.timeout = timeout;
    msg.data.start.level   = level;
    xQueueSend(s_vibrEventQueue, &msg, 0);
}

/**
 * @brief 启动所有振动电机（带强度档位）
 * @param mode 振动模式
 * @param timeout 超时时间(ms)
 * @param level 振动强度档位
 */
void Vibr_StartAllWithLevel(VibrMode mode, uint32_t timeout, VibrStrengthLevel level)
{
    VibrEventMsg msg       = {0};
    msg.type               = VIBR_EVT_START;
    msg.data.start.idMask  = (1 << VIBR_ID_MAX) - 1;
    msg.data.start.mode    = mode;
    msg.data.start.timeout = timeout;
    msg.data.start.level   = level;
    xQueueSend(s_vibrEventQueue, &msg, 0);
}

/**
 * @brief 启动单个振动电机
 * @param id 电机ID
 * @param mode 振动模式
 * @param timeout 超时时间(ms)
 */
void Vibr_Start(VibrID id, VibrMode mode, uint32_t timeout)
{
    Vibr_StartWithLevel(id, mode, timeout, VIBR_LEVEL_LOW);
}

/**
 * @brief 停止单个振动电机
 * @param id 电机ID
 */
void Vibr_Stop(VibrID id)
{
    VibrEventMsg msg     = {0};
    msg.type             = VIBR_EVT_STOP;
    msg.data.stop.idMask = (1 << id);
    xQueueSend(s_vibrEventQueue, &msg, 0);
}

/**
 * @brief 设置单个电机定时器
 * @param id 电机ID
 * @param period 周期(ms)
 * @param count 重复次数
 */
void Vibr_SetTimer(VibrID id, uint32_t period, uint32_t count)
{
    VibrEventMsg msg         = {0};
    msg.type                 = VIBR_EVT_TIMER_SET;
    msg.data.timerSet.idMask = (1 << id);
    msg.data.timerSet.period = period;
    msg.data.timerSet.count  = count;
    xQueueSend(s_vibrEventQueue, &msg, 0);
}

/**
 * @brief 按时间档位启动单个电机
 * @param id 电机ID
 * @param mode 振动模式
 * @param level 时间档位
 */
void Vibr_StartWithTimeLevel(VibrID id, VibrMode mode, VibrTimeLevel level)
{
    uint32_t timeout = Vibr_GetTimeoutFromLevel(level);
    if (timeout) Vibr_Start(id, mode, timeout);
}

/**
 * @brief 按自定义分钟数启动单个电机
 * @param id 电机ID
 * @param mode 振动模式
 * @param minutes 分钟数
 */
void Vibr_StartWithCustomTime(VibrID id, VibrMode mode, uint32_t minutes)
{
    if (minutes) Vibr_Start(id, mode, minutes * 60 * 1000);
}

/**
 * @brief 启动多个振动电机
 * @param ids 电机ID数组
 * @param count 数量
 * @param mode 振动模式
 * @param timeout 超时时间(ms)
 */
void Vibr_StartMultiple(const VibrID *ids, uint8_t count, VibrMode mode, uint32_t timeout)
{
    Vibr_StartMultipleWithLevel(ids, count, mode, timeout, VIBR_LEVEL_LOW);
}

/**
 * @brief 停止多个振动电机
 * @param ids 电机ID数组
 * @param count 数量
 */
void Vibr_StopMultiple(const VibrID *ids, uint8_t count)
{
    VibrEventMsg msg     = {0};
    msg.type             = VIBR_EVT_STOP;
    msg.data.stop.idMask = Vibr_ConvertIdsToBitMask(ids, count);
    xQueueSend(s_vibrEventQueue, &msg, 0);
}

/**
 * @brief 停止所有振动电机
 */
void Vibr_StopAll(void)
{
    VibrEventMsg msg     = {0};
    msg.type             = VIBR_EVT_STOP;
    msg.data.stop.idMask = (1 << VIBR_ID_MAX) - 1;
    xQueueSend(s_vibrEventQueue, &msg, 0);
}

/**
 * @brief 设置多个电机定时器
 * @param ids 电机ID数组
 * @param count 数量
 * @param period 周期(ms)
 * @param repeatCount 重复次数
 */
void Vibr_SetTimerMultiple(const VibrID *ids, uint8_t count, uint32_t period, uint32_t repeatCount)
{
    VibrEventMsg msg         = {0};
    msg.type                 = VIBR_EVT_TIMER_SET;
    msg.data.timerSet.idMask = Vibr_ConvertIdsToBitMask(ids, count);
    msg.data.timerSet.period = period;
    msg.data.timerSet.count  = repeatCount;
    xQueueSend(s_vibrEventQueue, &msg, 0);
}

/**
 * @brief 按时间档位启动多个电机
 * @param ids 电机ID数组
 * @param count 数量
 * @param mode 振动模式
 * @param level 时间档位
 */
void Vibr_StartMultipleWithTimeLevel(const VibrID *ids, uint8_t count, VibrMode mode, VibrTimeLevel level)
{
    uint32_t timeout = Vibr_GetTimeoutFromLevel(level);
    if (timeout) Vibr_StartMultiple(ids, count, mode, timeout);
}

/**
 * @brief 按自定义分钟数启动多个电机
 * @param ids 电机ID数组
 * @param count 数量
 * @param mode 振动模式
 * @param minutes 分钟数
 */
void Vibr_StartMultipleWithCustomTime(const VibrID *ids, uint8_t count, VibrMode mode, uint32_t minutes)
{
    if (minutes) Vibr_StartMultiple(ids, count, mode, minutes * 60 * 1000);
}

/**
 * @brief 启动所有振动电机
 * @param mode 振动模式
 * @param timeout 超时时间(ms)
 */
void Vibr_StartAll(VibrMode mode, uint32_t timeout)
{
    Vibr_StartAllWithLevel(mode, timeout, VIBR_LEVEL_LOW);
}

/**
 * @brief 电机ID数组转bitmask
 * @param ids 电机ID数组
 * @param count 数量
 * @return bitmask
 */
uint8_t Vibr_ConvertIdsToBitMask(const VibrID *ids, uint8_t count)
{
    uint8_t mask = 0;
    if (!ids || count == 0) return 0;
    for (uint8_t i = 0; i < count; i++) {
        if (ids[i] < VIBR_ID_MAX) mask |= (1 << ids[i]);
    }
    return mask;
}

/**
 * @brief 时间档位转超时时间
 * @param level 档位
 * @return 超时时间(ms)
 */
uint32_t Vibr_GetTimeoutFromLevel(VibrTimeLevel level)
{
    switch (level) {
        case VIBR_TIME_10MIN:
            return VIBR_TIME_10MIN_MS;
        case VIBR_TIME_20MIN:
            return VIBR_TIME_20MIN_MS;
        case VIBR_TIME_30MIN:
            return VIBR_TIME_30MIN_MS;
        default:
            return 0;
    }
}

/**
 * @brief 复位电机状态
 * @param state 状态指针
 */
void Vibr_ResetMotorState(VibrState *state)
{
    if (state) {
        state->duty      = 0;
        state->mode      = (uint8_t)VIBR_MODE_CONTINUOUS;
        state->direction = 0;
        state->tickCount = 0;
        state->period    = 0;
        state->count     = 0;
    }
}

/**
 * @brief 判断所有电机是否停止
 * @return true-全部停止 false-有电机在运行
 */
bool Vibr_IsAllMotorsStopped(void)
{
    for (int i = 0; i < VIBR_ID_MAX; i++) {
        if (s_vibrState[i].duty > 0) return false;
    }
    return true;
}

// ================== 定时器回调 ==================
/**
 * @brief 全局定时器回调
 */
static void vibr_global_timer_cb(TimerHandle_t xTimer)
{
    for (int i = 0; i < VIBR_ID_MAX; ++i) {
        VibrState *state = &s_vibrState[i];
        if (!state->enabled) continue;
        // tick事件
        if (state->tick_ms > 0) {
            if (state->tick_ms > 10) {
                state->tick_ms -= 10;
            } else {
                state->tick_ms   = state->tick_reload;
                VibrEventMsg msg = {0};
                msg.type         = VIBR_EVT_TICK;
                msg.data.tick.id = i;
                xQueueSend(s_vibrEventQueue, &msg, 0);
            }
        }
        // timeout事件
        if (state->timeout_ms > 0) {
            if (state->timeout_ms > 10) {
                state->timeout_ms -= 10;
            } else {
                state->timeout_ms   = 0;
                VibrEventMsg msg    = {0};
                msg.type            = VIBR_EVT_TIMEOUT;
                msg.data.timeout.id = i;
                xQueueSend(s_vibrEventQueue, &msg, 0);
            }
        }
        // period事件
        if (state->period_ms > 0) {
            if (state->period_ms > 10) {
                state->period_ms -= 10;
            } else {
                state->period_ms         = state->period_reload;
                VibrEventMsg msg         = {0};
                msg.type                 = VIBR_EVT_TIMER_SET; // 可自定义事件类型
                msg.data.timerSet.idMask = (1 << i);
                msg.data.timerSet.period = state->period_reload;
                msg.data.timerSet.count  = state->count;
                xQueueSend(s_vibrEventQueue, &msg, 0);
            }
        }
    }
}

/**
 * @brief 电机状态复位
 * @param id 电机ID
 */
static void vibr_reset_motor(uint8_t id)
{
    VIBR_LOG_INFO("reset_motor: id=%d", id);
    Vibr_ResetMotorState(&s_vibrState[id]);
    bsp_SetPwmDuty(GET_VIBR_PWM_PORT(id), 0);
}

/**
 * @brief 强制停止电机
 * @param id 电机ID
 */
static void vibr_force_stop(uint8_t id)
{
    VibrState *state     = &s_vibrState[id];
    state->enabled       = 0;
    state->timeout_ms    = 0;
    state->tick_ms       = 0;
    state->tick_reload   = 0;
    state->period_ms     = 0;
    state->period_reload = 0;
    Vibr_ResetMotorState(state);
    bsp_SetPwmDuty(GET_VIBR_PWM_PORT(id), 0);
    VIBR_LOG_INFO("force_stop: id=%d", id);
}

/**
 * @brief 事件分发处理
 * @param msg 事件消息
 */
static void vibr_handle_event(const VibrEventMsg *msg)
{
    switch (msg->type) {
        case VIBR_EVT_START: {
            VIBR_LOG_INFO("START: idMask=0x%02X, mode=%d, timeout=%lu, level=%d",
                          msg->data.start.idMask, msg->data.start.mode, msg->data.start.timeout, msg->data.start.level);
            for (uint8_t id = 0; id < VIBR_ID_MAX; id++) {
                if (msg->data.start.idMask & (1 << id)) {
                    VibrState *state = &s_vibrState[id];
                    state->mode      = (uint8_t)msg->data.start.mode;
                    state->level     = (uint8_t)msg->data.start.level;
                    state->enabled   = 1;
                    // 初始化软件定时器变量
                    state->timeout_ms    = msg->data.start.timeout;
                    state->tick_ms       = 0;
                    state->tick_reload   = 0;
                    state->period_ms     = 0;
                    state->period_reload = 0;
                    uint8_t duty         = Vibr_LevelToDuty((VibrStrengthLevel)state->level);
                    if (duty > 100) duty = 100;
                    switch (state->mode) {
                        case VIBR_MODE_CONTINUOUS:
                            state->duty = duty;
                            break;
                        case VIBR_MODE_PULSE1:
                            state->duty        = duty;
                            state->tick_ms     = VIBR_PULSE1_PERIOD;
                            state->tick_reload = VIBR_PULSE1_PERIOD;
                            break;
                        case VIBR_MODE_PULSE2:
                            state->duty        = duty;
                            state->tick_ms     = VIBR_PULSE2_PERIOD;
                            state->tick_reload = VIBR_PULSE2_PERIOD;
                            break;
                        case VIBR_MODE_PULSE3:
                            state->duty        = duty;
                            state->tick_ms     = VIBR_PULSE3_PERIOD;
                            state->tick_reload = VIBR_PULSE3_PERIOD;
                            break;
                        case VIBR_MODE_GRADIENT:
                            state->duty        = VIBR_MIN_DUTY;
                            state->direction   = 0;
                            state->tick_ms     = VIBR_GRADIENT_PERIOD;
                            state->tick_reload = VIBR_GRADIENT_PERIOD;
                            break;
                        default:
                            break;
                    }
                    VIBR_LOG_INFO("START set PWM: id=%d, duty=%d", id, state->duty);
                    if (state->duty > 100) state->duty = 100;
                    bsp_SetPwmDuty(GET_VIBR_PWM_PORT(id), state->duty);
                }
            }
            break;
        }
        case VIBR_EVT_STOP: {
            VIBR_LOG_INFO("STOP: idMask=0x%02X", msg->data.stop.idMask);
            for (uint8_t id = 0; id < VIBR_ID_MAX; id++) {
                if (msg->data.stop.idMask & (1 << id)) {
                    vibr_force_stop(id);
                }
            }
            break;
        }
        case VIBR_EVT_TIMER_SET: {
            // 设置周期事件参数
            for (uint8_t id = 0; id < VIBR_ID_MAX; id++) {
                if (msg->data.timerSet.idMask & (1 << id)) {
                    VibrState *state     = &s_vibrState[id];
                    state->period_ms     = msg->data.timerSet.period;
                    state->period_reload = msg->data.timerSet.period;
                    state->count         = msg->data.timerSet.count;
                }
            }
            break;
        }
        case VIBR_EVT_TIMEOUT: {
            VIBR_LOG_INFO("TIMEOUT: id=%d", msg->data.timeout.id);
            vibr_force_stop(msg->data.timeout.id);
            break;
        }
        case VIBR_EVT_TICK: {
            VIBR_LOG_INFO("TICK: id=%d", msg->data.tick.id);
            uint8_t id       = msg->data.tick.id;
            VibrState *state = &s_vibrState[id];
            uint8_t duty     = Vibr_LevelToDuty((VibrStrengthLevel)state->level);
            if (duty > 100) duty = 100;
            switch (state->mode) {
                case VIBR_MODE_PULSE1:
                case VIBR_MODE_PULSE2:
                case VIBR_MODE_PULSE3:
                    state->duty = (state->duty > 0) ? 0 : duty;
                    break;
                case VIBR_MODE_GRADIENT:
                    if (state->direction == 0) {
                        state->duty += VIBR_GRADIENT_STEP;
                        if (state->duty >= duty) {
                            state->duty      = duty;
                            state->direction = 1;
                        }
                    } else {
                        if (state->duty <= VIBR_GRADIENT_STEP) {
                            state->duty      = 0;
                            state->direction = 0;
                        } else {
                            state->duty -= VIBR_GRADIENT_STEP;
                        }
                    }
                    break;
                default:
                    break;
            }
            if (state->duty > 100) state->duty = 100;
            bsp_SetPwmDuty(GET_VIBR_PWM_PORT(id), state->duty);
            break;
        }
        default:
            break;
    }
}

/**
 * @brief 振动电机任务主循环
 *        处理所有事件，实现状态机逻辑
 */
static void vibr_task_entry(void *param)
{
    (void)param;
    // 初始化所有电机状态和PWM
    for (int i = 0; i < VIBR_ID_MAX; i++) {
        Vibr_ResetMotorState(&s_vibrState[i]);
        bsp_SetPwmDuty(GET_VIBR_PWM_PORT(i), 0);
    }

    for (;;) {
        VibrEventMsg msg;
        if (xQueueReceive(s_vibrEventQueue, &msg, portMAX_DELAY) == pdPASS) {
            vibr_handle_event(&msg);
        }
    }
}

/**
 * @brief 振动电机任务初始化
 *
 * 创建事件队列、定时器、任务等资源，需在app_init中调用
 */
void vibr_task_init(void)
{
    memset(s_vibrState, 0, sizeof(s_vibrState));
    s_vibrEventQueue = xQueueCreate(VIBR_EVENT_QUEUE_LEN, sizeof(VibrEventMsg));
    if (s_vibrEventQueue == NULL) {
        VIBR_LOG_ERROR("vibr_task_init: xQueueCreate failed");
    }
    // 创建全局定时器
    s_vibrGlobalTimer = xTimerCreate("VibrGlobal", pdMS_TO_TICKS(10), pdTRUE, NULL, vibr_global_timer_cb);
    if (s_vibrGlobalTimer == NULL) {
        VIBR_LOG_ERROR("vibr_task_init: xTimerCreate failed");
    } else {
        xTimerStart(s_vibrGlobalTimer, 0);
    }
    // 创建振动电机任务
    BaseType_t xReturn = xTaskCreate(vibr_task_entry,
                                     "vibr_task",
                                     APP_VIBR_TASK_STACK_SIZE,
                                     NULL,
                                     APP_VIBR_PRIO,
                                     &s_vibrTaskHandle);
    if (xReturn != pdPASS) {
        VIBR_LOG_ERROR("vibr_task_init: xTaskCreate failed");
    }
}