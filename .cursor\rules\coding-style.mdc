---
description: 
globs: 
alwaysApply: true
---
# FreeRTOS嵌入式项目编程规范

## 1. 命名规范
- 文件命名：
  - 任务相关文件统一为`xxx_task.c`/`xxx_task.h`。
  - 驱动相关文件为`bsp_xxx.c`/`bsp_xxx.h`。
  - 公共组件为`xxx.c`/`xxx.h`。
- 变量命名：
  - 全局变量加`g_`前缀，静态变量加`s_`前缀。
  - 任务句柄：`TaskHandle_t xxx_task_handle`。
  - 队列、信号量、事件组等RTOS对象加类型后缀（如`xxx_queue`、`xxx_sem`、`xxx_evt`）。
- 函数命名：
  - 任务入口函数：`void xxx_task_entry(void *pvParameters)`。
  - 驱动接口：`bsp_xxx_init()`、`bsp_xxx_write()`等。
  - 组件接口：`log_info()`、`ringbuffer_put()`等。

## 2. 注释规范
- 文件头注释：包含文件功能、作者、日期、版本等。
- 函数头注释：简要描述功能、参数、返回值、注意事项。
- 关键逻辑、硬件相关操作、RTOS对象创建与使用处必须有详细注释。
- 推荐中英文结合，硬件寄存器操作建议附寄存器/位定义说明。

## 3. 代码风格
- 缩进统一使用4空格。
- 大括号风格建议K&R（与FreeRTOS源码一致）。
- 每行不超过120字符，适当换行。
- 头文件需有防重复包含宏。
- 禁止魔法数字，常量统一用`#define`或`const`定义。
- 每个文件建议不超过1000行代码，最多不能超过1500行，超出需强制拆分模块。

## 4. FreeRTOS对象管理
- 所有任务、队列、信号量、事件组等对象统一在专用头文件声明（如`app_freertos.h`）。
- 对象创建、删除、使用流程需有详细注释。
- 任务优先级、堆栈大小需结合实际评估，避免资源浪费或溢出。
- ISR中与RTOS交互必须用`FromISR`后缀API。
- 建议任务间通信优先用队列、事件组，避免直接全局变量。

## 5. 其他建议
- 充分利用静态分析工具和编译器告警，保证代码质量。
- 重要操作、边界条件、错误处理必须有日志输出（如log组件）。
- 代码提交前建议自测，重大变更需评审。

---

如有团队特殊约定，可在本规范基础上补充。
