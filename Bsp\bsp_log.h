#ifndef BSP_LOG_H
#define BSP_LOG_H

#include "log.h"

// BSP模块日志开关配置
#define BSP_FLASH_LOG_ENABLE    1 // Flash模块日志使能
#define BSP_SPI_LOG_ENABLE      1 // SPI模块日志使能
#define BSP_W25QXX_LOG_ENABLE   1 // W25QXX Flash模块日志使能
#define BSP_PARAM_LOG_ENABLE    1 // 参数管理模块日志使能
#define BSP_WATCHDOG_LOG_ENABLE 1 // 看门狗模块日志使能
#define BSP_PWM_LOG_ENABLE      1 // PWM模块日志使能
#define BSP_HALL_LOG_ENABLE     1 // Hall传感器模块日志使能
#define BSP_ADC_LOG_ENABLE      1 // ADC模块日志使能
#define BSP_USART_LOG_ENABLE    1 // USART模块日志使能
#define BSP_LED_LOG_ENABLE      1 // LED模块日志使能
#define BSP_CLOCK_LOG_ENABLE    1 // 时钟模块日志使能
#define BSP_TIMER_LOG_ENABLE    1 // 定时器模块日志使能
#define BSP_RADAR_LOG_ENABLE    1 // 雷达模块日志使能
#define BSP_DS1302_LOG_ENABLE   1 // DS1302 RTC模块日志使能

// Flash模块日志
#define BSP_FLASH_LOG_TAG       "BSP_FLASH"
#if BSP_FLASH_LOG_ENABLE
#define BSP_FLASH_LOG_ERROR(...)     LOG_ERROR(BSP_FLASH_LOG_TAG, __VA_ARGS__)
#define BSP_FLASH_LOG_WARN(...)      LOG_WARN(BSP_FLASH_LOG_TAG, __VA_ARGS__)
#define BSP_FLASH_LOG_INFO(...)      LOG_INFO(BSP_FLASH_LOG_TAG, __VA_ARGS__)
#define BSP_FLASH_LOG_DEBUG(...)     LOG_DEBUG(BSP_FLASH_LOG_TAG, __VA_ARGS__)
#define BSP_FLASH_LOG_DEBUG_RAW(...) LOG_DEBUG_RAW(__VA_ARGS__)
#else
#define BSP_FLASH_LOG_ERROR(...)
#define BSP_FLASH_LOG_WARN(...)
#define BSP_FLASH_LOG_INFO(...)
#define BSP_FLASH_LOG_DEBUG(...)
#define BSP_FLASH_LOG_DEBUG_RAW(...)
#endif

// SPI模块日志
#define SPI_LOG_TAG "BSP_SPI"
#if BSP_SPI_LOG_ENABLE
#define SPI_LOG_ERROR(...) LOG_ERROR(SPI_LOG_TAG, __VA_ARGS__)
#define SPI_LOG_WARN(...)  LOG_WARN(SPI_LOG_TAG, __VA_ARGS__)
#define SPI_LOG_INFO(...)  LOG_INFO(SPI_LOG_TAG, __VA_ARGS__)
#define SPI_LOG_DEBUG(...) LOG_DEBUG(SPI_LOG_TAG, __VA_ARGS__)
#else
#define SPI_LOG_ERROR(...)
#define SPI_LOG_WARN(...)
#define SPI_LOG_INFO(...)
#define SPI_LOG_DEBUG(...)
#endif

// W25QXX Flash模块日志
#define W25QXX_LOG_TAG "BSP_W25QXX"
#if BSP_W25QXX_LOG_ENABLE
#define W25QXX_LOG_ERROR(...) LOG_ERROR(W25QXX_LOG_TAG, __VA_ARGS__)
#define W25QXX_LOG_WARN(...)  LOG_WARN(W25QXX_LOG_TAG, __VA_ARGS__)
#define W25QXX_LOG_INFO(...)  LOG_INFO(W25QXX_LOG_TAG, __VA_ARGS__)
#define W25QXX_LOG_DEBUG(...) LOG_DEBUG(W25QXX_LOG_TAG, __VA_ARGS__)
#else
#define W25QXX_LOG_ERROR(...)
#define W25QXX_LOG_WARN(...)
#define W25QXX_LOG_INFO(...)
#define W25QXX_LOG_DEBUG(...)
#endif

// 参数管理模块日志
#define BSP_PARAM_LOG_TAG "BSP_PARAM"
#if BSP_PARAM_LOG_ENABLE
#define BSP_PARAM_LOG_ERROR(...) LOG_ERROR(BSP_PARAM_LOG_TAG, __VA_ARGS__)
#define BSP_PARAM_LOG_WARN(...)  LOG_WARN(BSP_PARAM_LOG_TAG, __VA_ARGS__)
#define BSP_PARAM_LOG_INFO(...)  LOG_INFO(BSP_PARAM_LOG_TAG, __VA_ARGS__)
#define BSP_PARAM_LOG_DEBUG(...) LOG_DEBUG(BSP_PARAM_LOG_TAG, __VA_ARGS__)
#else
#define BSP_PARAM_LOG_ERROR(...)
#define BSP_PARAM_LOG_WARN(...)
#define BSP_PARAM_LOG_INFO(...)
#define BSP_PARAM_LOG_DEBUG(...)
#endif

// 看门狗模块日志
#define WATCHDOG_LOG_TAG "BSP_WDOG"
#if BSP_WATCHDOG_LOG_ENABLE
#define WATCHDOG_LOG_ERROR(...) LOG_ERROR(WATCHDOG_LOG_TAG, __VA_ARGS__)
#define WATCHDOG_LOG_WARN(...)  LOG_WARN(WATCHDOG_LOG_TAG, __VA_ARGS__)
#define WATCHDOG_LOG_INFO(...)  LOG_INFO(WATCHDOG_LOG_TAG, __VA_ARGS__)
#define WATCHDOG_LOG_DEBUG(...) LOG_DEBUG(WATCHDOG_LOG_TAG, __VA_ARGS__)
#else
#define WATCHDOG_LOG_ERROR(...)
#define WATCHDOG_LOG_WARN(...)
#define WATCHDOG_LOG_INFO(...)
#define WATCHDOG_LOG_DEBUG(...)
#endif

// PWM模块日志
#define PWM_LOG_TAG "BSP_PWM"
#if BSP_PWM_LOG_ENABLE
#define PWM_LOG_ERROR(...) LOG_ERROR(PWM_LOG_TAG, __VA_ARGS__)
#define PWM_LOG_WARN(...)  LOG_WARN(PWM_LOG_TAG, __VA_ARGS__)
#define PWM_LOG_INFO(...)  LOG_INFO(PWM_LOG_TAG, __VA_ARGS__)
#define PWM_LOG_DEBUG(...) LOG_DEBUG(PWM_LOG_TAG, __VA_ARGS__)
#else
#define PWM_LOG_ERROR(...)
#define PWM_LOG_WARN(...)
#define PWM_LOG_INFO(...)
#define PWM_LOG_DEBUG(...)
#endif

// Hall传感器模块日志
#define HALL_LOG_TAG "BSP_HALL"
#if BSP_HALL_LOG_ENABLE
#define HALL_LOG_ERROR(...) LOG_ERROR(HALL_LOG_TAG, __VA_ARGS__)
#define HALL_LOG_WARN(...)  LOG_WARN(HALL_LOG_TAG, __VA_ARGS__)
#define HALL_LOG_INFO(...)  LOG_INFO(HALL_LOG_TAG, __VA_ARGS__)
#define HALL_LOG_DEBUG(...) LOG_DEBUG(HALL_LOG_TAG, __VA_ARGS__)
#else
#define HALL_LOG_ERROR(...)
#define HALL_LOG_WARN(...)
#define HALL_LOG_INFO(...)
#define HALL_LOG_DEBUG(...)
#endif

// ADC模块日志
#define ADC_LOG_TAG "BSP_ADC"
#if BSP_ADC_LOG_ENABLE
#define ADC_LOG_ERROR(...) LOG_ERROR(ADC_LOG_TAG, __VA_ARGS__)
#define ADC_LOG_WARN(...)  LOG_WARN(ADC_LOG_TAG, __VA_ARGS__)
#define ADC_LOG_INFO(...)  LOG_INFO(ADC_LOG_TAG, __VA_ARGS__)
#define ADC_LOG_DEBUG(...) LOG_DEBUG(ADC_LOG_TAG, __VA_ARGS__)
#else
#define ADC_LOG_ERROR(...)
#define ADC_LOG_WARN(...)
#define ADC_LOG_INFO(...)
#define ADC_LOG_DEBUG(...)
#endif

// USART模块日志
#define USART_LOG_TAG "BSP_USART"
#if BSP_USART_LOG_ENABLE
#define USART_LOG_ERROR(...) LOG_ERROR(USART_LOG_TAG, __VA_ARGS__)
#define USART_LOG_WARN(...)  LOG_WARN(USART_LOG_TAG, __VA_ARGS__)
#define USART_LOG_INFO(...)  LOG_INFO(USART_LOG_TAG, __VA_ARGS__)
#define USART_LOG_DEBUG(...) LOG_DEBUG(USART_LOG_TAG, __VA_ARGS__)
#else
#define USART_LOG_ERROR(...)
#define USART_LOG_WARN(...)
#define USART_LOG_INFO(...)
#define USART_LOG_DEBUG(...)
#endif

// LED模块日志
#define BSP_LED_LOG_TAG "BSP_LED"
#if BSP_LED_LOG_ENABLE
#define BSP_LED_LOG_ERROR(...) LOG_ERROR(BSP_LED_LOG_TAG, __VA_ARGS__)
#define BSP_LED_LOG_WARN(...)  LOG_WARN(BSP_LED_LOG_TAG, __VA_ARGS__)
#define BSP_LED_LOG_INFO(...)  LOG_INFO(BSP_LED_LOG_TAG, __VA_ARGS__)
#define BSP_LED_LOG_DEBUG(...) LOG_DEBUG(BSP_LED_LOG_TAG, __VA_ARGS__)
#else
#define BSP_LED_LOG_ERROR(...)
#define BSP_LED_LOG_WARN(...)
#define BSP_LED_LOG_INFO(...)
#define BSP_LED_LOG_DEBUG(...)
#endif

// 时钟模块日志
#define CLOCK_LOG_TAG "BSP_CLOCK"
#if BSP_CLOCK_LOG_ENABLE
#define CLOCK_LOG_ERROR(...) LOG_ERROR(CLOCK_LOG_TAG, __VA_ARGS__)
#define CLOCK_LOG_WARN(...)  LOG_WARN(CLOCK_LOG_TAG, __VA_ARGS__)
#define CLOCK_LOG_INFO(...)  LOG_INFO(CLOCK_LOG_TAG, __VA_ARGS__)
#define CLOCK_LOG_DEBUG(...) LOG_DEBUG(CLOCK_LOG_TAG, __VA_ARGS__)
#else
#define CLOCK_LOG_ERROR(...)
#define CLOCK_LOG_WARN(...)
#define CLOCK_LOG_INFO(...)
#define CLOCK_LOG_DEBUG(...)
#endif

// 定时器模块日志
#define TIMER_LOG_TAG "BSP_TIMER"
#if BSP_TIMER_LOG_ENABLE
#define TIMER_LOG_ERROR(...) LOG_ERROR(TIMER_LOG_TAG, __VA_ARGS__)
#define TIMER_LOG_WARN(...)  LOG_WARN(TIMER_LOG_TAG, __VA_ARGS__)
#define TIMER_LOG_INFO(...)  LOG_INFO(TIMER_LOG_TAG, __VA_ARGS__)
#define TIMER_LOG_DEBUG(...) LOG_DEBUG(TIMER_LOG_TAG, __VA_ARGS__)
#else
#define TIMER_LOG_ERROR(...)
#define TIMER_LOG_WARN(...)
#define TIMER_LOG_INFO(...)
#define TIMER_LOG_DEBUG(...)
#endif

// 雷达模块日志
#define BSP_RADAR_LOG_TAG "BSP_RADAR"
#if BSP_RADAR_LOG_ENABLE
#define BSP_RADAR_LOG_ERROR(...) LOG_ERROR(BSP_RADAR_LOG_TAG, __VA_ARGS__)
#define BSP_RADAR_LOG_WARN(...)  LOG_WARN(BSP_RADAR_LOG_TAG, __VA_ARGS__)
#define BSP_RADAR_LOG_INFO(...)  LOG_INFO(BSP_RADAR_LOG_TAG, __VA_ARGS__)
#define BSP_RADAR_LOG_DEBUG(...) LOG_DEBUG(BSP_RADAR_LOG_TAG, __VA_ARGS__)
#else
#define BSP_RADAR_LOG_ERROR(...)
#define BSP_RADAR_LOG_WARN(...)
#define BSP_RADAR_LOG_INFO(...)
#define BSP_RADAR_LOG_DEBUG(...)
#endif

// DS1302 RTC模块日志
#define DS1302_LOG_TAG "BSP_DS1302"
#if BSP_DS1302_LOG_ENABLE
#define DS1302_LOG_ERROR(...) LOG_ERROR(DS1302_LOG_TAG, __VA_ARGS__)
#define DS1302_LOG_WARN(...)  LOG_WARN(DS1302_LOG_TAG, __VA_ARGS__)
#define DS1302_LOG_INFO(...)  LOG_INFO(DS1302_LOG_TAG, __VA_ARGS__)
#define DS1302_LOG_DEBUG(...) LOG_DEBUG(DS1302_LOG_TAG, __VA_ARGS__)
#else
#define DS1302_LOG_ERROR(...)
#define DS1302_LOG_WARN(...)
#define DS1302_LOG_INFO(...)
#define DS1302_LOG_DEBUG(...)
#endif

// BSP通用日志
#define BSP_LOG_TAG        "BSP"
#define BSP_LOG_ERROR(...) LOG_ERROR(BSP_LOG_TAG, __VA_ARGS__)
#define BSP_LOG_WARN(...)  LOG_WARN(BSP_LOG_TAG, __VA_ARGS__)
#define BSP_LOG_INFO(...)  LOG_INFO(BSP_LOG_TAG, __VA_ARGS__)
#define BSP_LOG_DEBUG(...) LOG_DEBUG(BSP_LOG_TAG, __VA_ARGS__)

#endif /* BSP_LOG_H */