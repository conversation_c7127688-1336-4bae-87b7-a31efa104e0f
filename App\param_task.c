/**
 * @file param.c
 * <AUTHOR> (<EMAIL>)
 * @brief 参数管理
 * @version 0.1
 * @date 2025-05-07
 *
 * @copyright Copyright (c) 2025
 *
 */
#include "param_task.h"

#define PARAM_QUEUE_LEN 8

static struct fdb_kvdb kvdb = {0};

static SemaphoreHandle_t g_param_mutex = NULL;

static QueueHandle_t param_queue = NULL;

// ================= 具体处理函数实现 =================

/**
 * @brief 保存电机参数
 * @param data 电机参数
 * @return 错误码
 */
static fdb_err_t save_motor(const void *data) {
    const motor_param_save_ctx_t *motor_ctx = (const motor_param_save_ctx_t *)data;
    struct fdb_blob blob;
    char key[12];
    snprintf(key, sizeof(key), "motor%d", motor_ctx->id);
    return fdb_kv_set_blob(&kvdb, key, fdb_blob_make(&blob, &motor_ctx->param, sizeof(motor_param_store_t)));
}

/**
 * @brief 保存远程ID
 * @param data 远程ID
 * @return 错误码
 */
static fdb_err_t save_remote_id(const void *data) {
    struct fdb_blob blob;
    return fdb_kv_set_blob(&kvdb, "remote_id", fdb_blob_make(&blob, data, 6));
}

/**
 * @brief 保存自定义角度
 * @param data 自定义角度
 * @return 错误码
 */
static fdb_err_t save_custom_angle(const void *data) {
    const custom_angle_save_ctx_t *angle_ctx = (const custom_angle_save_ctx_t *)data;
    struct fdb_blob blob;
    char key[12];
    snprintf(key, sizeof(key), "custom%d", angle_ctx->index);
    return fdb_kv_set_blob(&kvdb, key, fdb_blob_make(&blob, &angle_ctx->angle, sizeof(custom_bed_angle_t)));
}

/**
 * @brief 保存WiFi参数
 * @param data WiFi参数
 * @return 错误码
 */
static fdb_err_t save_wifi(const void *data) {
    struct fdb_blob blob;
    return fdb_kv_set_blob(&kvdb, "wifi", fdb_blob_make(&blob, data, sizeof(wifi_param_t)));
}

/**
 * @brief 保存MQTT参数
 * @param data MQTT参数
 * @return 错误码
 */
static fdb_err_t save_mqtt(const void *data) {
    struct fdb_blob blob;
    return fdb_kv_set_blob(&kvdb, "mqtt", fdb_blob_make(&blob, data, sizeof(mqtt_param_t)));
}

/**
 * @brief 加载电机参数
 * @param arg 电机ID
 * @param result 结果
 * @return 是否成功
 */
static bool load_motor(const void *arg, void *result) {
    struct fdb_blob blob;
    char key[12];
    uint8_t id = *(uint8_t *)arg;
    snprintf(key, sizeof(key), "motor%d", id);
    size_t len = fdb_kv_get_blob(&kvdb, key, fdb_blob_make(&blob, result, sizeof(motor_param_store_t)));
    return len == sizeof(motor_param_store_t);
}

/**
 * @brief 加载远程ID
 * @param arg 远程ID
 * @param result 结果
 * @return 是否成功
 */
static bool load_remote_id(const void *arg, void *result) {
    struct fdb_blob blob;
    size_t len = fdb_kv_get_blob(&kvdb, "remote_id", fdb_blob_make(&blob, result, 6));
    return len == 6;
}

/**
 * @brief 加载自定义角度
 * @param arg 自定义角度索引
 * @param result 结果
 * @return 是否成功
 */
static bool load_custom_angle(const void *arg, void *result) {
    struct fdb_blob blob;
    char key[12];
    uint8_t index = *(uint8_t *)arg;
    snprintf(key, sizeof(key), "custom%d", index);
    size_t len = fdb_kv_get_blob(&kvdb, key, fdb_blob_make(&blob, result, sizeof(custom_bed_angle_t)));
    return len == sizeof(custom_bed_angle_t);
}

/**
 * @brief 加载WiFi参数
 * @param arg 参数
 * @param result 结果
 * @return 是否成功
 */
static bool load_wifi(const void *arg, void *result) {
    struct fdb_blob blob;
    size_t len = fdb_kv_get_blob(&kvdb, "wifi", fdb_blob_make(&blob, result, sizeof(wifi_param_t)));
    return len == sizeof(wifi_param_t);
}

/**
 * @brief 加载MQTT参数
 * @param arg 参数
 * @param result 结果
 * @return 是否成功
 */
static bool load_mqtt(const void *arg, void *result) {
    struct fdb_blob blob;
    size_t len = fdb_kv_get_blob(&kvdb, "mqtt", fdb_blob_make(&blob, result, sizeof(mqtt_param_t)));
    return len == sizeof(mqtt_param_t);
}

// ================= 查找表定义 =================
static const param_handler_entry_t param_handler_table[] = {
    { PARAM_SAVE_MOTOR, save_motor, load_motor },
    { PARAM_SAVE_REMOTE_ID, save_remote_id, load_remote_id },
    { PARAM_SAVE_CUSTOM_ANGLE, save_custom_angle, load_custom_angle },
    { PARAM_SAVE_WIFI, save_wifi, load_wifi },
    { PARAM_SAVE_MQTT, save_mqtt, load_mqtt },
    { PARAM_LOAD_MOTOR, NULL, load_motor },
    { PARAM_LOAD_REMOTE_ID, NULL, load_remote_id },
    { PARAM_LOAD_CUSTOM_ANGLE, NULL, load_custom_angle },
    { PARAM_LOAD_WIFI, NULL, load_wifi },
    { PARAM_LOAD_MQTT, NULL, load_mqtt },
};
#define PARAM_HANDLER_TABLE_SIZE (sizeof(param_handler_table)/sizeof(param_handler_table[0])) // 查找表大小

/**
 * @brief 查找保存处理函数
 * @param type 参数类型
 * @return 处理函数指针
 */
static const param_handler_entry_t* find_save_handler(uint8_t type) {
    for (size_t i = 0; i < PARAM_HANDLER_TABLE_SIZE; ++i) {
        if (param_handler_table[i].type == type && param_handler_table[i].save_handler) {
            return &param_handler_table[i];
        }
    }
    return NULL;
}

/**
 * @brief 查找加载处理函数
 * @param type 参数类型
 * @return 处理函数指针
 */
static const param_handler_entry_t* find_load_handler(uint8_t type) {
    for (size_t i = 0; i < PARAM_HANDLER_TABLE_SIZE; ++i) {
        if (param_handler_table[i].type == type && param_handler_table[i].load_handler) {
            return &param_handler_table[i];
        }
    }
    return NULL;
}

/**
 * @brief 参数管理任务入口
 * @param pvParameters 任务参数
 */
static void param_task_entry(void *pvParameters)
{
    param_msg_t msg;
    for (;;) {
        if (xQueueReceive(param_queue, &msg, portMAX_DELAY) == pdTRUE) {
            if (msg.op_type == PARAM_OP_SAVE) {
                const param_handler_entry_t *entry = find_save_handler(msg.msg.save.type);
                fdb_err_t err = FDB_NO_ERR;
                if (entry && entry->save_handler) {
                    err = entry->save_handler(msg.msg.save.data);
                    if (err != FDB_NO_ERR) {
                        PARAM_LOG_ERROR("Async save type %d failed: %d", msg.msg.save.type, err);
                    } else {
                        PARAM_LOG_INFO("Async save type %d success", msg.msg.save.type);
                    }
                } else {
                    PARAM_LOG_ERROR("Unknown param save type: %d", msg.msg.save.type);
                }
                // 同步保存支持
                if (msg.msg.save.success) *msg.msg.save.success = (err == FDB_NO_ERR);
                if (msg.msg.save.done_sem) xSemaphoreGive(msg.msg.save.done_sem);
            } else if (msg.op_type == PARAM_OP_LOAD) {
                const param_handler_entry_t *entry = find_load_handler(msg.msg.load.type);
                bool result = false;
                if (entry && entry->load_handler) {
                    result = entry->load_handler(msg.msg.load.arg, msg.msg.load.result);
                    if (result) {
                        PARAM_LOG_INFO("Async load type %d success", msg.msg.load.type);
                    } else {
                        PARAM_LOG_ERROR("Async load type %d failed", msg.msg.load.type);
                    }
                } else {
                    PARAM_LOG_ERROR("Unknown param load type: %d", msg.msg.load.type);
                }
                if (msg.msg.load.success) *msg.msg.load.success = result;
                if (msg.msg.load.done_sem) xSemaphoreGive(msg.msg.load.done_sem);
            }
        }
    }
}

/**
 * @brief 异步保存参数
 * @param req 保存请求
 * @return 是否成功
 */
bool param_save_async(const param_save_req_t *req)
{
    param_msg_t msg = {0};
    msg.op_type     = PARAM_OP_SAVE;
    msg.msg.save    = *req;
    return xQueueSend(param_queue, &msg, 0) == pdTRUE;
}

/**
 * @brief 异步读取参数
 * @param req 读取请求
 * @return 是否成功
 */
bool param_load_async(param_load_req_t *req)
{
    param_msg_t msg       = {0};
    msg.op_type           = PARAM_OP_LOAD;
    msg.msg.load          = *req;
    SemaphoreHandle_t sem = xSemaphoreCreateBinary();
    bool success          = false;
    msg.msg.load.done_sem = sem;
    msg.msg.load.success  = &success;
    xQueueSend(param_queue, &msg, portMAX_DELAY);
    xSemaphoreTake(sem, portMAX_DELAY);
    vSemaphoreDelete(sem);
    if (req->success) *req->success = success;
    return success;
}

/**
 * @brief 锁
 *
 * @param db
 */
static void lock(fdb_db_t db)
{
    xSemaphoreTake(g_param_mutex, portMAX_DELAY);
}

/**
 * @brief 解锁
 *
 * @param db
 */
static void unlock(fdb_db_t db)
{
    xSemaphoreGive(g_param_mutex);
}

/**
 * @brief 初始化参数管理任务
 */
void param_task_init(void)
{
    if (g_param_mutex == NULL) {
        g_param_mutex = xSemaphoreCreateMutex();
        assert(g_param_mutex != NULL);
    }

    fdb_kvdb_control(&kvdb, FDB_KVDB_CTRL_SET_LOCK, lock);
    fdb_kvdb_control(&kvdb, FDB_KVDB_CTRL_SET_UNLOCK, unlock);

    /* 初始化FlashDB */
    fdb_kvdb_init(&kvdb, "kvdb", "env", NULL, NULL);

    param_queue = xQueueCreate(PARAM_QUEUE_LEN, sizeof(param_msg_t));
    if (param_queue == NULL) {
        PARAM_LOG_ERROR("参数队列创建失败");
    }
    BaseType_t xReturn = xTaskCreate(param_task_entry,
                                     "param_task",
                                     APP_PARAM_TASK_STACK_SIZE,
                                     NULL,
                                     APP_PARAM_TASK_PRIO,
                                     NULL);
    if (xReturn != pdPASS) {
        PARAM_LOG_ERROR("参数任务创建失败");
    }
}

// ================= 类型安全同步API实现 =================

bool param_load_motor(uint8_t id, motor_param_store_t *out)
{
    param_load_req_t req = {
        .type = PARAM_LOAD_MOTOR,
        .arg = &id,
        .result = out,
    };
    return param_load_async(&req);
}

bool param_save_motor(uint8_t id, const motor_param_store_t *in)
{
    motor_param_save_ctx_t ctx = {
        .id = id,
        .param = *in,
    };
    param_save_req_t req = {
        .type = PARAM_SAVE_MOTOR,
        .data = &ctx,
    };
    return param_save_sync(&req);
}

bool param_load_custom_angle(uint8_t index, custom_bed_angle_t *out)
{
    param_load_req_t req = {
        .type = PARAM_LOAD_CUSTOM_ANGLE,
        .arg = &index,
        .result = out,
    };
    return param_load_async(&req);
}

bool param_save_custom_angle(uint8_t index, const custom_bed_angle_t *in)
{
    custom_angle_save_ctx_t ctx = {
        .index = index,
        .angle = *in,
    };
    param_save_req_t req = {
        .type = PARAM_SAVE_CUSTOM_ANGLE,
        .data = &ctx,
    };
    return param_save_sync(&req);
}

bool param_load_wifi(wifi_param_t *out)
{
    param_load_req_t req = {
        .type = PARAM_LOAD_WIFI,
        .arg = NULL,
        .result = out,
    };
    return param_load_async(&req);
}

bool param_save_wifi(const wifi_param_t *in)
{
    param_save_req_t req = {
        .type = PARAM_SAVE_WIFI,
        .data = in,
    };
    return param_save_sync(&req);
}

bool param_load_mqtt(mqtt_param_t *out)
{
    param_load_req_t req = {
        .type = PARAM_LOAD_MQTT,
        .arg = NULL,
        .result = out,
    };
    return param_load_async(&req);
}

bool param_save_mqtt(const mqtt_param_t *in)
{
    param_save_req_t req = {
        .type = PARAM_SAVE_MQTT,
        .data = in,
    };
    return param_save_sync(&req);
}

// ================= 新增：同步保存API实现 =================
bool param_save_sync(param_save_req_t *req)
{
    param_msg_t msg = {0};
    msg.op_type     = PARAM_OP_SAVE;
    msg.msg.save    = *req;
    SemaphoreHandle_t sem = xSemaphoreCreateBinary();
    bool success = false;
    msg.msg.save.done_sem = sem;
    msg.msg.save.success  = &success;
    xQueueSend(param_queue, &msg, portMAX_DELAY);
    xSemaphoreTake(sem, portMAX_DELAY);
    vSemaphoreDelete(sem);
    if (req->success) *req->success = success;
    return success;
}