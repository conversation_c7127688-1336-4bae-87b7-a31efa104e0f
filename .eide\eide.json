{"name": "EBED_FREERTOS", "type": "ARM", "dependenceList": [], "srcDirs": ["App", "Bsp", "Components", "FreeRTOS", "FWLib", "User"], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": []}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "d7f3f8c4b74e39890cf6cbca3765db71"}, "targets": {"Debug": {"excludeList": ["FreeRTOS/portable/MemMang/heap_1.c", "FreeRTOS/portable/MemMang/heap_2.c", "FreeRTOS/portable/MemMang/heap_3.c", "FreeRTOS/portable/MemMang/heap_5.c", "FreeRTOS/portable/RVDS/ARM7_LPC21xx", "FreeRTOS/portable/RVDS/ARM_CA9", "FreeRTOS/portable/RVDS/ARM_CM0", "FreeRTOS/portable/RVDS/ARM_CM4F", "FreeRTOS/portable/RVDS/ARM_CM4_MPU", "FreeRTOS/portable/RVDS/ARM_CM7", "Components/misc", "Components/easylogger", "FWLib/CMSIS/Core/Include/a-profile", "FWLib/CMSIS/Core/Include/m-profile", "FWLib/CMSIS/Core/Include/r-profile", "FreeRTOS/portable/RVDS", "FreeRTOS/portable/GCC/ARM7_AT91FR40008", "FreeRTOS/portable/GCC/ARM7_AT91SAM7S", "FreeRTOS/portable/GCC/ARM7_LPC2000", "FreeRTOS/portable/GCC/ARM7_LPC23xx", "FreeRTOS/portable/GCC/ARM_AARCH64", "FreeRTOS/portable/GCC/ARM_AARCH64_SRE", "FreeRTOS/portable/GCC/ARM_CA53_64_BIT_SRE", "FreeRTOS/portable/GCC/ARM_CA9", "FreeRTOS/portable/GCC/ARM_CA53_64_BIT", "FreeRTOS/portable/GCC/ARM_CM0", "FreeRTOS/portable/GCC/ARM_CM23", "FreeRTOS/portable/GCC/ARM_CM23_NTZ", "FreeRTOS/portable/GCC/ARM_CM33", "FreeRTOS/portable/GCC/ARM_CM33_NTZ", "FreeRTOS/portable/GCC/ARM_CM35P", "FreeRTOS/portable/GCC/ARM_CM35P_NTZ", "FreeRTOS/portable/GCC/ARM_CM3_MPU", "FreeRTOS/portable/GCC/ARM_CM4F", "FreeRTOS/portable/GCC/ARM_CM4_MPU", "FreeRTOS/portable/GCC/ARM_CM55", "FreeRTOS/portable/GCC/ARM_CM55_NTZ", "FreeRTOS/portable/GCC/ARM_CM7", "FreeRTOS/portable/GCC/ARM_CM85", "FreeRTOS/portable/GCC/ARM_CM85_NTZ", "FreeRTOS/portable/GCC/ARM_CR5", "FreeRTOS/portable/GCC/ARM_CRx_MPU", "FreeRTOS/portable/GCC/ARM_CRx_No_GIC", "FreeRTOS/portable/GCC/ATMega323", "FreeRTOS/portable/GCC/AVR32_UC3", "FreeRTOS/portable/GCC/AVR_AVRDx", "FreeRTOS/portable/GCC/AVR_Mega0", "FreeRTOS/portable/GCC/ColdFire_V2", "FreeRTOS/portable/GCC/CORTUS_APS3", "FreeRTOS/portable/GCC/H8S2329", "FreeRTOS/portable/GCC/HCS12", "FreeRTOS/portable/GCC/IA32_flat", "FreeRTOS/portable/GCC/MCF5235", "FreeRTOS/portable/GCC/MicroBlaze", "FreeRTOS/portable/GCC/MicroBlazeV8", "FreeRTOS/portable/GCC/MicroBlazeV9", "FreeRTOS/portable/GCC/MSP430F449", "FreeRTOS/portable/GCC/NiosII", "FreeRTOS/portable/GCC/PPC405_Xilinx", "FreeRTOS/portable/GCC/PPC440_Xilinx", "FreeRTOS/portable/GCC/RISC-V", "FreeRTOS/portable/GCC/RL78", "FreeRTOS/portable/GCC/RX100", "FreeRTOS/portable/GCC/RX200", "FreeRTOS/portable/GCC/RX600", "FreeRTOS/portable/GCC/RX600v2", "FreeRTOS/portable/GCC/RX700v3_DPFPU", "FreeRTOS/portable/GCC/STR75x", "FreeRTOS/portable/GCC/TriCore_1782", "Components/FlashDB/demos", "Components/FlashDB/docs", "Components/FlashDB/port", "Components/FlashDB/samples", "Components/FlashDB/tests", "Components/FlashDB/zephyr", "Bsp/bsp_hall.c"], "toolchain": "AC6", "compileConfig": {"cpuType": "Cortex-M4", "floatingPointHardware": "none", "useCustomScatterFile": false, "scatterFilePath": "<YOUR_SCATTER_FILE>.sct", "storageLayout": {"RAM": [{"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x6000"}, "isChecked": true, "noInit": false}], "ROM": [{"tag": "IROM", "id": 1, "mem": {"startAddr": "0x08000000", "size": "0x20000"}, "isChecked": true, "isStartup": true}]}, "options": "null"}, "uploader": "pyOCD", "uploadConfig": {"bin": "", "targetName": "cortex_m", "baseAddr": "0x08000000", "speed": "4M", "config": ".eide/debug.pyocd.yaml", "otherCmds": ""}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "OpenOCD": {"bin": "", "target": "stm32f1x", "interface": "stlink", "baseAddr": "0x08000000"}}, "custom_dep": {"name": "default", "incList": [], "libList": [], "defineList": []}, "builderOptions": {"GCC": {"version": 5, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"$float-abi-type": "softfp", "output-debug-info": "enable", "misc-control": "--specs=nosys.specs --specs=nano.specs"}, "c/cpp-compiler": {"language-c": "c11", "language-cpp": "c++11", "optimization": "level-debug", "warnings": "all-warnings", "one-elf-section-per-function": true, "one-elf-section-per-data": true, "C_FLAGS": "", "CXX_FLAGS": ""}, "asm-compiler": {"ASM_FLAGS": ""}, "linker": {"output-format": "elf", "remove-unused-input-sections": true, "LD_FLAGS": "", "LIB_FLAGS": "-lm"}}, "AC6": {"version": 3, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-balanced", "language-c": "gnu99", "language-cpp": "c++11", "one-elf-section-per-function": true, "warnings": "ac5-like-warnings"}, "asm-compiler": {"$use": "asm-auto"}, "linker": {"output-format": "elf", "misc-controls": "--diag_suppress=L6329 --KEEP=*Handler"}}}}}, "version": "3.5"}