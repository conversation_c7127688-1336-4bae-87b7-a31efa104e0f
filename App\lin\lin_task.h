#ifndef LIN_TASK_H
#define LIN_TASK_H

/**
 * @file lin_task.h
 * @brief LIN主机控制任务头文件
 * <AUTHOR>
 * @version V1.0
 * @date 2024-06-09
 * @note 新增LIN主机控制任务
 */

#include "app_config.h"
#include "app_log.h"
#include "bsp.h"
#include "bsp_lin.h"

#define LIN_SCHEDULE_TABLE_SIZE 5                             // 调度表大小
#define LIN_MAX_CALLBACKS       (LIN_SCHEDULE_TABLE_SIZE * 2) // 最大回调函数数量

#define FRAME_BUF_SIZE 200

// ========== 长帧分段发送状态结构体 ===========
typedef struct {
    uint8_t id;
    uint8_t buf[FRAME_BUF_SIZE];
    uint16_t total_len;
    uint16_t offset;
    uint8_t sending; // 1: 正在发送长帧
} lin_long_frame_send_t;

/**
 * @brief LIN主从模式类型
 */
typedef enum {
    LIN_MODE_MASTER = 0, // 主机
    LIN_MODE_SLAVE       // 从机
} lin_mode_t;

/**
 * @brief LIN主机操作类型
 */
typedef enum {
    LIN_OP_READ = 0, // 读取从机
    LIN_OP_WRITE     // 控制/写入从机
} lin_op_type_t;

/**
 * @brief LIN帧类型
 */
typedef enum {
    LIN_FRAME_UNCONDITIONAL = 0, // 无条件帧
    LIN_FRAME_EVENT,             // 事件触发帧
    LIN_FRAME_SPORADIC           // 零星帧
} lin_frame_type_t;

typedef enum {
    LIN_ERR_NONE = 0,
    LIN_ERR_TIMEOUT,
    LIN_ERR_CHECKSUM,
    LIN_ERR_UNKNOWN
} lin_error_type_t;

// LIN消息结构体
typedef struct {
    uint8_t id;       // 消息ID
    uint8_t len;      // 数据长度
    uint8_t checksum; // 校验和（与数据分开存储）
    uint8_t data[8];  // 数据域（1-8字节）
} lin_msg_t;

/**
 * @brief LIN主机调度表项结构体（符合LIN2.0协议栈思想）
 */
typedef struct {
    uint32_t last_tick;           // 上次执行时间戳
    uint16_t period_ms;           // 调度周期
    uint16_t response_timeout_ms; // 响应超时时间
    uint8_t slave_id;             // 从机ID
    uint8_t msg_id;               // 消息ID
    uint8_t write_data[8];        // 写入数据
    uint8_t read_data[8];         // 读取数据
    uint8_t write_len : 4;        // 写入数据长度
    uint8_t read_len : 4;         // 读取数据长度
    uint8_t need_exec : 1;        // 是否需要执行
    uint8_t last_result : 1;      // 上次执行结果
    uint8_t event_flag : 1;       // 事件标志
    uint8_t valid : 1;            // 有效标志
    uint8_t expect_len : 4;       // 期望读取数据长度
    uint8_t error_count;          // 错误计数
    uint8_t op_type : 2;          // 操作类型
    uint8_t frame_type : 2;       // 帧类型
    uint8_t error_type : 2;       // 错误类型
    uint8_t reserved : 2;         // 保留字段
} lin_schedule_entry_t;

// LIN消息回调类型
typedef void (*lin_msg_callback_t)(const lin_schedule_entry_t *entry);

typedef struct {
    uint8_t msg_id;
    lin_msg_callback_t cb;
} lin_callback_entry_t;

/**
 * @brief LIN任务初始化（默认主机模式）
 */
void lin_task_init(void);

/**
 * @brief 切换到从机模式（由回调触发）
 */
void lin_task_switch_to_slave(void);

/**
 * @brief 动态修改调度周期
 * @param msg_id LIN消息ID
 * @param new_period_ms 新的调度周期（ms）
 */
void lin_set_schedule_period(uint8_t msg_id, uint16_t new_period_ms);

/**
 * @brief 事件触发帧调度（立即调度一次指定帧）
 * @param msg_id LIN消息ID
 */
void lin_trigger_event_frame(uint8_t msg_id);

// 动态调度API
int lin_add_schedule_entry(const lin_schedule_entry_t *entry);
int lin_remove_schedule_entry(uint8_t msg_id);
int lin_modify_schedule_entry(uint8_t msg_id, const lin_schedule_entry_t *entry);

/**
 * @brief 注册指定msg_id的回调
 * @param msg_id LIN消息ID
 * @param cb 回调函数指针
 */
void lin_register_msgid_callback(uint8_t msg_id, lin_msg_callback_t cb);

/**
 * @brief 注册默认回调（未注册msg_id时调用）
 * @param cb 回调函数指针
 */
void lin_register_default_callback(lin_msg_callback_t cb);

/**
 * @brief 发送帧
 * 
 * @param id 从机ID
 * @param frame_buf 帧数据
 * @param frame_len 帧长度
 */
void lin_slave_send_frame(const uint8_t *frame_buf, uint16_t frame_len);

#endif // LIN_TASK_H