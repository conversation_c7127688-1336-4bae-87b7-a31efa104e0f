#ifndef BSP_HALL_H
#define BSP_HALL_H

#include "config.h"

#ifdef USE_HALL

// 霍尔传感器端口定义
#define HALL0_PORT   HC_GPIOB
#define HALL1_PORT   HC_GPIOB
#define HALL2_PORT   HC_GPIOB
#define HALL3_PORT   HC_GPIOB

// 霍尔传感器引脚定义
#define HALL0_PIN    GPIO_PIN_15
#define HALL1_PIN    GPIO_PIN_14
#define HALL2_PIN    GPIO_PIN_13
#define HALL3_PIN    GPIO_PIN_12

// 霍尔传感器中断配置
#define HALL_IRQn    PORTB_IRQn

#define ALL_HALL_PIN (HALL1_PIN | HALL2_PIN | HALL3_PIN | HALL4_PIN)

// 方向定义
typedef enum {
    HALL_DIR_NONE = 0, // 未知方向
    HALL_DIR_UP,       // 上升方向
    HALL_DIR_DOWN      // 下降方向
} hall_dir_t;

// 霍尔传感器回调函数类型定义
typedef void (*hall_callback_t)(uint8_t hallId, bool state, hall_dir_t dir);

// 霍尔传感器配置结构体
typedef struct {
    bool reverse_direction;      // 是否反转方向判断逻辑
    uint16_t debounce_time;      // 去抖时间(ms)
    uint16_t stop_timeout;       // 停止超时时间(ms)
    uint16_t min_valid_interval; // 最小有效信号间隔(ms)
} hall_config_t;

// 霍尔传感器状态结构体
typedef struct {
    bool last_state;         // 上次状态
    uint32_t last_trig_time; // 上次触发时间
    hall_dir_t last_dir;     // 上次方向
    uint32_t pulse_count;    // 脉冲计数
    uint32_t error_count;    // 错误计数
} hall_status_t;

// 函数声明
void bsp_HallSetConfig(uint8_t hallId, const hall_config_t *config);
void bsp_HallGetStatus(uint8_t hallId, hall_status_t *status);
void bsp_HallReset(uint8_t hallId);

// 霍尔传感器初始化函数
void bsp_HallInit(uint8_t hallId);

// 读取霍尔传感器状态
bool bsp_ReadHall(uint8_t hallId);

// 注册霍尔传感器中断回调函数
void bsp_HallRegisterCallback(hall_callback_t callback);

// 使能/失能霍尔传感器中断
void bsp_HallInterruptEnable(uint8_t hallId, bool enable);

#endif // USE_HALL

#endif // BSP_HALL_H