/**
 * @file clock.c
 * <AUTHOR> (<EMAIL>)
 * @brief 时钟配置
 * @version 0.1
 * @date 2024-09-11
 *
 * @copyright Copyright (c) 2024
 *
 */
#include "bsp_clock.h"

/**
 * @brief Systick初始化
 *
 */
void Sys_ClkCfg(void)
{
    stc_sysctrl_sysclk_source_init_t stcSysClkSrc = {0};
    stc_sysctrl_clk_init_t stcSysClk              = {0};

    /* 系统时钟源初始化 */
    /* FOUT = FIN * MULM / DIVN / OD = 4 * 48 / 1 / 2^2 = 48MHz */
    stcSysClkSrc.u32SysClkSourceType = SYSCTRL_SYSCLK_SOURCE_TYPE_PLL;

    stcSysClkSrc.u32PLLState = SYSCTRL_PLL_MULM_CONFIG(48) | SYSCTRL_PLL_DIVN_CONFIG(1) | SYSCTRL_PLL_OEN_ON | SYSCTRL_PLL_WAITCYCLE_CONFIG(7) | SYSCTRL_PLL_OD_CONFIG(2) | SYSCTRL_PLL_SRC_RCH;

    while (Ok != SYSCTRL_SysClkSrcInit(&stcSysClkSrc)) /* 时钟源初始化 */
    {
        ;
    }

    /* 系统时钟初始化 */
    stcSysClk.u32ClockType = SYSCTRL_CLOCKTYPE_SYSCLK | SYSCTRL_CLOCKTYPE_HCLK | SYSCTRL_CLOCKTYPE_PCLK0 | SYSCTRL_CLOCKTYPE_PCLK1; /* 时钟初始化需要操作PLL，HCLK和PCLK */

    stcSysClk.u32SysClkSource = SYSCTRL_SYSCLK_SOURCE_PLL; /* PLL为系统时钟 */
    stcSysClk.u32HClkDiv      = SYSCTRL_SYSCLK_HCLK_PRS1;  /* HCLK不分频 */
    stcSysClk.u32PClk0Div     = SYSCTRL_SYSCLK_PCLK0_PRS1; /* PCLK0不分频 */
    stcSysClk.u32PClk1Div     = SYSCTRL_SYSCLK_PCLK1_PRS4; /* PCLK1 4分频 */
    stcSysClk.u32WaitCycle    = SYSCTRL_FLASH_WAIT_CYCLE2; /* 设置flash 读等待为2个周期 */

    SYSCTRL_SysClkInit(&stcSysClk); /* 时钟初始化 */
}

/**
 * @brief 系统时钟初始化
 *
 */
void bsp_ClockInit(void)
{
    Sys_ClkCfg();
    SysTick_Config(SystemCoreClock / 1000); // 内核函数，SysTick配置，定时1ms
}
