/**
 * @file bed_control.c
 * <AUTHOR> (<EMAIL>)
 * @brief 床位控制
 * @version 0.1
 * @date 2025-05-06
 *
 * @copyright Copyright (c) 2025
 *
 */
#include "bed_control.h"
#include "param_task.h"
#include "motor_task.h"
#include "vibr_task.h"
#include "rgb_task.h"
#include "msg_task.h"

// ================== 按摩电机控制状态变量 ==================
static bool g_massage_power_on = false;  // 按摩电机开关
static uint8_t g_massage_strength_level[2] = {1, 1}; // [0]=背部, [1]=腿部
static uint8_t g_massage_mode[2] = {1, 1};           // [0]=背部, [1]=腿部
static uint8_t g_massage_time_level[2] = {0, 0};     // [0]=背部, [1]=腿部
static uint32_t g_massage_time_ms[2] = {0, 0};
static bool g_massage_time_custom[2] = {false, false};

// 档位与vibr_task.h枚举映射
static const VibrStrengthLevel s_strength_map[3] = {VIBR_LEVEL_LOW, VIBR_LEVEL_MEDIUM, VIBR_LEVEL_HIGH};
static const VibrMode s_mode_map[4]              = {VIBR_MODE_CONTINUOUS, VIBR_MODE_PULSE1, VIBR_MODE_PULSE2, VIBR_MODE_PULSE3};
static const VibrTimeLevel s_time_map[3]         = {VIBR_TIME_10MIN, VIBR_TIME_20MIN, VIBR_TIME_30MIN};

/**
 * @brief 多电机多模式控制参数表
 * 可根据实际需求扩展更多模式和参数
 */
const BedMultiMotorParam bed_control_mode_table[BED_MODE_MAX] = {
    [BED_MODE_FLAT]        = {.back1_angle = 0, .back2_angle = 0, .waist_angle = 0, .leg_angle = 0, .speed = 100},     // 平躺
    [BED_MODE_SLEEP]       = {.back1_angle = 0, .back2_angle = 0, .waist_angle = 0, .leg_angle = 0, .speed = 100},     // 睡眠
    [BED_MODE_MOVIE]       = {.back1_angle = 55, .back2_angle = 55, .waist_angle = 10, .leg_angle = 35, .speed = 100}, // 观影
    [BED_MODE_SPACE]       = {.back1_angle = 18, .back2_angle = 18, .waist_angle = 5, .leg_angle = 35, .speed = 100},  // 太空
    [BED_MODE_READ]        = {.back1_angle = 40, .back2_angle = 40, .waist_angle = 0, .leg_angle = 20, .speed = 100},  // 阅读
    [BED_MODE_SNORE]       = {.back1_angle = 15, .back2_angle = 15, .waist_angle = 15, .leg_angle = 0, .speed = 100},  // 止鼾
    [BED_MODE_DRINK]       = {.back1_angle = 15, .back2_angle = 15, .waist_angle = 20, .leg_angle = 0, .speed = 100},  // 酒后
    [BED_MODE_RELAX]       = {.back1_angle = 15, .back2_angle = 15, .waist_angle = 20, .leg_angle = 0, .speed = 100},  // 放松
    [BED_MODE_SOFT_WAKEUP] = {.back1_angle = 30, .back2_angle = 30, .waist_angle = 10, .leg_angle = 10, .speed = 100}, // 柔性唤醒
    [BED_MODE_PILATES]     = {.back1_angle = 0, .back2_angle = 0, .waist_angle = 0, .leg_angle = 60, .speed = 100},    // 普拉提
    [BED_MODE_RECOVERY]    = {.back1_angle = 20, .back2_angle = 20, .waist_angle = 10, .leg_angle = 30, .speed = 100}, // 运动恢复
    [BED_MODE_WAIST_CARE]  = {.back1_angle = 10, .back2_angle = 10, .waist_angle = 30, .leg_angle = 20, .speed = 100}, // 腰部呵护
    [BED_MODE_AUTO_SNORE]  = {.back1_angle = 25, .back2_angle = 25, .waist_angle = 15, .leg_angle = 10, .speed = 100}, // 自动止鼾
    [BED_MODE_FEEDING]     = {.back1_angle = 0, .back2_angle = 0, .waist_angle = 0, .leg_angle = 0, .speed = 100},     // 哺乳
    [BED_MODE_YOGA]        = {.back1_angle = 0, .back2_angle = 0, .waist_angle = 0, .leg_angle = 0, .speed = 100},     // 瑜伽
    [BED_MODE_SHOW]        = {.back1_angle = 0, .back2_angle = 0, .waist_angle = 0, .leg_angle = 0, .speed = 100},     // 展示
    [BED_MODE_MASSAGE]     = {.back1_angle = 0, .back2_angle = 0, .waist_angle = 0, .leg_angle = 0, .speed = 100},     // 按摩
    [BED_MODE_CASUAL]      = {.back1_angle = 0, .back2_angle = 0, .waist_angle = 0, .leg_angle = 0, .speed = 100},     // 休闲
};

/**
 * @brief 电机移动到指定角度
 * @param motor_id 电机编号
 * @param angle 目标角度
 * @param speed 运动速度(1-100)
 */
static void bed_control_move_to_angle(uint8_t motor_id, uint8_t angle, uint8_t speed)
{
    motor_task_send_cmd(&(motor_task_cmd_t){
        .cmd      = MOTOR_TASK_CMD_MOVE_TO_ANGLE,
        .motor_id = motor_id,
        .data     = {
                .angle = angle,
        },
    });
    motor_task_set_speed(motor_id, speed);
}

/**
 * @brief 按模式设置床位
 * @param mode 预设模式
 */
void bed_control_set_mode(uint8_t mode)
{
    if (mode >= BED_MODE_MAX) return;
    const BedMultiMotorParam *param = &bed_control_mode_table[mode];
    // 控制背部电机
    bed_control_move_to_angle(MOTOR_ID_BACK1, param->back1_angle, param->speed);
    // 控制背部电机
    bed_control_move_to_angle(MOTOR_ID_BACK2, param->back2_angle, param->speed);
    // 控制腰部电机
    bed_control_move_to_angle(MOTOR_ID_WAIST, param->waist_angle, param->speed);
    // 控制腿部电机
    bed_control_move_to_angle(MOTOR_ID_LEG, param->leg_angle, param->speed);

    // 打印当前模式
    BED_LOG_INFO("Bed mode: %d", mode);
}

/**
 * @brief 电机向上运动
 * @param motor_id 电机编号
 */
void bed_control_up(uint8_t motor_id)
{
    motor_task_send_cmd(&(motor_task_cmd_t){
        .cmd      = MOTOR_TASK_CMD_UP,
        .motor_id = motor_id,
        .data     = {
                .position = 100,
        },
    });
    BED_LOG_INFO("Bed up: %d", motor_id);
}

/**
 * @brief 电机向下运动
 * @param motor_id 电机编号
 */
void bed_control_down(uint8_t motor_id)
{
    motor_task_send_cmd(&(motor_task_cmd_t){
        .cmd      = MOTOR_TASK_CMD_DOWN,
        .motor_id = motor_id,
        .data     = {
                .position = 0,
        },
    });
    BED_LOG_INFO("Bed down: %d", motor_id);
}

/**
 * @brief 电机停止
 * @param motor_id 电机编号
 */
void bed_control_stop(uint8_t motor_id)
{
    motor_task_send_cmd(&(motor_task_cmd_t){
        .cmd      = MOTOR_TASK_CMD_STOP,
        .motor_id = motor_id,
    });
    BED_LOG_INFO("Bed stop: %d", motor_id);
}

/**
 * @brief 保存当前角度到自定义模式
 * @param index 自定义模式索引
 */
void bed_control_set_custom_mode(uint8_t index)
{
    if (index >= CUSTOM_MODE_NUM) return;
    custom_bed_angle_t angle = {
        .back1_angle = motor_task_get_angle(MOTOR_ID_BACK1),
        .back2_angle = motor_task_get_angle(MOTOR_ID_BACK2),
        .waist_angle = motor_task_get_angle(MOTOR_ID_WAIST),
        .leg_angle   = motor_task_get_angle(MOTOR_ID_LEG),
    };
    param_save_custom_angle(index, &angle);
}

/**
 * @brief 读出自定义模式的角度并调整
 * @param index 自定义模式索引
 * @return bool 是否读取成功
 */
bool bed_control_get_custom_mode(uint8_t index)
{
    if (index >= CUSTOM_MODE_NUM) return false;
    custom_bed_angle_t angle = {0};
    if (param_load_custom_angle(index, &angle)) {
        bed_control_move_to_angle(MOTOR_ID_BACK1, angle.back1_angle, 100);
        bed_control_move_to_angle(MOTOR_ID_BACK2, angle.back2_angle, 100);
        bed_control_move_to_angle(MOTOR_ID_WAIST, angle.waist_angle, 100);
        bed_control_move_to_angle(MOTOR_ID_LEG, angle.leg_angle, 100);
        return true;
    }
    return false;
}

/**
 * @brief 按摩电机参数同步到硬件
 */
static void bed_control_massage_update_motor(void)
{
    if (g_massage_power_on) {
        Vibr_Stop(VIBR_ID_3);
        Vibr_Stop(VIBR_ID_4);
        uint32_t timeout3 = g_massage_time_custom[0] ? g_massage_time_ms[0] : Vibr_GetTimeoutFromLevel(s_time_map[g_massage_time_level[0]]);
        uint32_t timeout4 = g_massage_time_custom[1] ? g_massage_time_ms[1] : Vibr_GetTimeoutFromLevel(s_time_map[g_massage_time_level[1]]);
        Vibr_StartWithLevel(VIBR_ID_3, s_mode_map[g_massage_mode[0] - 1], timeout3, s_strength_map[g_massage_strength_level[0] - 1]);
        Vibr_StartWithLevel(VIBR_ID_4, s_mode_map[g_massage_mode[1] - 1], timeout4, s_strength_map[g_massage_strength_level[1] - 1]);
    } else {
        Vibr_Stop(VIBR_ID_3);
        Vibr_Stop(VIBR_ID_4);
    }
}

/**
 * @brief 灯带打开
 */
void bed_control_rgb_on(void)
{
    RGB_SetResponseMode(RGB_RESPONSE_FAST);
    RGB_TurnOn(RGB_CHANNEL_SINGLE);
    RGB_SetEffect(RGB_CHANNEL_SINGLE, RGB_EFFECT_STATIC, 0);
    RGB_SetBrightness(RGB_CHANNEL_SINGLE, RGB_MAX_BRIGHTNESS);
}

/**
 * @brief 灯带关闭
 */
void bed_control_rgb_off(void)
{
    RGB_SetResponseMode(RGB_RESPONSE_FAST);
    RGB_SetEffect(RGB_CHANNEL_SINGLE, RGB_EFFECT_STATIC, 0);
    RGB_SetBrightness(RGB_CHANNEL_SINGLE, 0);
    RGB_TurnOff(RGB_CHANNEL_SINGLE);
}

/**
 * @brief 按摩电机开关控制
 */
void bed_control_massage_power_ctrl(void)
{
    if (!g_massage_power_on) {
        // 开启：最低强度、连续、10min
        g_massage_power_on          = true;
        g_massage_strength_level[0] = 1;
        g_massage_strength_level[1] = 1;
        g_massage_mode[0]            = 1;
        g_massage_mode[1]            = 1;
        g_massage_time_level[0]      = 0;
        g_massage_time_level[1]      = 0;
        bed_control_massage_update_motor();
        // 输出状态
        BLE_LOG_INFO("massage_power_on: %d", g_massage_power_on);
    } else {
        // 关闭
        g_massage_power_on = false;
        bed_control_massage_update_motor();
        // 输出状态
        BLE_LOG_INFO("massage_power_off: %d", g_massage_power_on);
    }
}

/**
 * @brief 背部按摩电机强度控制
 */
void bed_control_back_massage_strength_ctrl(void)
{
    if (g_massage_power_on) {
        g_massage_strength_level[0]++;
        if (g_massage_strength_level[0] > 3) g_massage_strength_level[0] = 1;
        bed_control_massage_update_motor();
        // 输出状态
        BLE_LOG_INFO("back_strength: %d", g_massage_strength_level[0]);
    }
}

/**
 * @brief 腿部按摩电机强度控制
 */
void bed_control_leg_massage_strength_ctrl(void)
{
    if (g_massage_power_on) {
        g_massage_strength_level[1]++;
        if (g_massage_strength_level[1] > 3) g_massage_strength_level[1] = 1;
        bed_control_massage_update_motor();
        // 输出状态
        BLE_LOG_INFO("leg_strength: %d", g_massage_strength_level[1]);
    }
}

/**
 * @brief 按摩电机模式控制
 */
void bed_control_massage_mode_ctrl(void)
{
    if (g_massage_power_on) {
        g_massage_mode[0]++;
        if (g_massage_mode[0] > sizeof(s_mode_map) / sizeof(s_mode_map[0])) g_massage_mode[0] = 1;
        g_massage_mode[1]++;
        if (g_massage_mode[1] > sizeof(s_mode_map) / sizeof(s_mode_map[0])) g_massage_mode[1] = 1;
        bed_control_massage_update_motor();
        // 输出状态
        BLE_LOG_INFO("mode: %d", g_massage_mode[0]);
    }
}

/**
 * @brief 按摩电机定时控制
 */
void bed_control_massage_time_ctrl(void)
{
    if (g_massage_power_on) {
        g_massage_time_level[0]++;
        if (g_massage_time_level[0] > 2) g_massage_time_level[0] = 0;
        g_massage_time_level[1]++;
        if (g_massage_time_level[1] > 2) g_massage_time_level[1] = 0;
        bed_control_massage_update_motor();
        // 输出状态
        BLE_LOG_INFO("time_level: %d", g_massage_time_level[0]);
    }
}

/**
 * @brief 设置按摩电机强度
 * @param part 电机部分
 * @param strength 强度
 */
void bed_control_set_massage_strength(uint8_t part, uint8_t strength)
{
    if (part == 0)
        g_massage_strength_level[0] = strength;
    else if (part == 1)
        g_massage_strength_level[1] = strength;
    else {
        g_massage_strength_level[0] = strength;
        g_massage_strength_level[1] = strength;
    }
    bed_control_massage_update_motor();
}

/**
 * @brief 设置按摩电机模式
 * @param part 电机部分
 * @param mode 模式
 */
void bed_control_set_massage_mode(uint8_t part, uint8_t mode)
{
    if (part == 0)
        g_massage_mode[0] = mode;
    else if (part == 1)
        g_massage_mode[1] = mode;
    else {
        g_massage_mode[0] = mode;
        g_massage_mode[1] = mode;
    }
    bed_control_massage_update_motor();
}

/**
 * @brief 设置按摩电机定时
 * @param part 电机部分
 * @param time_ms 时间
 * @param is_custom 是否自定义
 */
void bed_control_set_massage_time(uint8_t part, uint32_t time_ms, bool is_custom)
{
    if (part == 0) {
        g_massage_time_custom[0] = is_custom;
        if (is_custom) g_massage_time_ms[0] = time_ms;
        else g_massage_time_level[0] = (uint8_t)time_ms;
    } else if (part == 1) {
        g_massage_time_custom[1] = is_custom;
        if (is_custom) g_massage_time_ms[1] = time_ms;
        else g_massage_time_level[1] = (uint8_t)time_ms;
    } else {
        g_massage_time_custom[0] = g_massage_time_custom[1] = is_custom;
        if (is_custom) g_massage_time_ms[0] = g_massage_time_ms[1] = time_ms;
        else g_massage_time_level[0] = g_massage_time_level[1] = (uint8_t)time_ms;
    }
    bed_control_massage_update_motor();
}

/**
 * @brief 设置按摩电机功率
 * @param on 是否开启
 */
void bed_control_set_massage_power(bool on)
{
    g_massage_power_on = on;
    bed_control_massage_update_motor();
}
