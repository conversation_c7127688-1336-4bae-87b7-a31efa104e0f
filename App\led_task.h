#ifndef LED_TASK_H
#define LED_TASK_H

#include "app_config.h"
#include "app_log.h"
#include "bsp.h"

/**
 * @brief LED类型定义
 */
typedef enum {
    LED_SYS = 0, /**< 系统LED */
    LED_WIFI,    /**< WiFi指示LED */
    LED_BLE,     /**< 蓝牙指示LED */
    LED_MAX      /**< LED数量 */
} Led_TypeDef;

/**
 * @brief LED控制消息类型
 */
typedef enum {
    LED_MSG_ON = 0,  /**< 打开LED */
    LED_MSG_OFF,     /**< 关闭LED */
    LED_MSG_TOGGLE,  /**< 切换LED状态 */
    LED_MSG_BLINK,   /**< 简单闪烁 */
    LED_MSG_BLINK_EX /**< 高级闪烁 */
} LedMsgType;

/**
 * @brief 闪烁配置结构体
 */
typedef struct {
    TimerHandle_t timer; /**< FreeRTOS软件定时器句柄 */
    uint16_t onTime;     /**< 亮灯时间(ms) */
    uint16_t offTime;    /**< 灭灯时间(ms) */
    uint16_t count;      /**< 剩余闪烁次数, 0=持续 */
} blink_t;

/**
 * @brief LED控制上下文结构体
 * 保存所有LED的状态和闪烁配置
 */
typedef struct {
    uint8_t state[LED_MAX]; /**< 每个LED当前状态(0=灭, 1=亮) */
    blink_t blink[LED_MAX]; /**< 每个LED的闪烁配置 */
} LedControlContext;

/**
 * @brief LED控制消息结构体
 */
typedef struct {
    LedMsgType type; /**< 消息类型 */
    uint16_t onTime;  /**< 亮灯时间(ms) */
    uint16_t offTime; /**< 灭灯时间(ms) */
    uint16_t count;   /**< 闪烁次数(0=持续) */
    uint8_t ledId;    /**< LED编号 */
} LedMsg;

/**
 * @brief 打开指定LED
 * @param ledId LED编号
 */
void LED_on(Led_TypeDef ledId);

/**
 * @brief 关闭指定LED
 * @param ledId LED编号
 */
void LED_off(Led_TypeDef ledId);

/**
 * @brief 切换指定LED状态
 * @param ledId LED编号
 */
void LED_toggle(Led_TypeDef ledId);

/**
 * @brief 指定LED等时闪烁(持续)
 * @param ledId LED编号
 * @param period 闪烁周期(ms)
 */
void LED_blink(Led_TypeDef ledId, uint16_t period);

/**
 * @brief 高级闪烁控制
 * @param ledId LED编号
 * @param onTime 亮灯时间(ms)
 * @param offTime 灭灯时间(ms)
 * @param count 闪烁次数(0=持续)
 */
void LED_blinkEx(Led_TypeDef ledId, uint16_t onTime, uint16_t offTime, uint16_t count);

/**
 * @brief 常用闪烁模式宏
 */
#define LED_BLINK_FAST(id)   LED_blinkEx(id, 50, 50, 0)   /**< 快速闪烁(10Hz) */
#define LED_BLINK_NORMAL(id) LED_blinkEx(id, 250, 250, 0) /**< 正常闪烁(2Hz) */
#define LED_BLINK_SLOW(id)   LED_blinkEx(id, 500, 500, 0) /**< 慢速闪烁(1Hz) */
#define LED_BLINK_ONCE(id)   LED_blinkEx(id, 50, 50, 1)   /**< 闪烁一次 */
#define LED_BLINK_TWICE(id)  LED_blinkEx(id, 100, 100, 2) /**< 闪烁两次 */
#define LED_BLINK_ERROR(id)  LED_blinkEx(id, 50, 950, 0)  /**< 错误指示(1Hz, 5%占空比) */

/**
 * @brief LED控制模块初始化(使用前必须调用)
 */
void led_task_init(void);

#endif // LED_TASK_H