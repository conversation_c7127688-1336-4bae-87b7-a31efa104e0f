#ifndef __BSP_ADC_H__
#define __BSP_ADC_H__

#include "config.h"

#ifdef USE_ADC

// ADC通道数量定义
#define ADC_CHANNEL_NUM             6   // adc通道数
#define AD_FILTER_NUM               100 // 滤波点数
#define ADC_MAX_VARIATION           50  // 最大允许波动值，从100降低到50，减少突变

// 堵转保护参数
#define BLOCK_COMMUTATION_MIN_COUNT 2       // 10ms内最小换相次数
#define BLOCK_INTEGRAL_LIMIT        8000    // 电流积分阈值，100ms内触发
#define BLOCK_RECOVERY_THRESHOLD    2000    // 恢复判据的积分阈值
#define BLOCK_RECOVERY_COMM_COUNT   3       // 恢复判据的换相次数
#define BLOCK_CURRENT_LOW_THRESHOLD 50.0f   // 电流低于此值时积分衰减
#define BLOCK_DECAY_FACTOR          0.95f   // 积分衰减系数
#define BLOCK_CURRENT_THRESHOLD     6000.0f // 堵转判据（单位mA）
#define OVERCURRENT_THRESHOLD       9000.0f // 过流判据（单位mA）

// ADC采样参数定义
#define ADC_VREF                    3304  // 参考电压(mV)
#define ADC_RESOLUTION              4096  // ADC分辨率(12位)
#define CURRENT_SENSE_RESISTOR      0.05f // 采样电阻(Ω)
#define CURRENT_AMP_GAIN            6.67f // 放大倍数

// 电机电流检测通道定义
typedef enum {
    MOTOR0_CURR = 0, // 电机0电流检测通道
    MOTOR1_CURR,     // 电机1电流检测通道
    MOTOR2_CURR,     // 电机2电流检测通道
    MOTOR3_CURR,     // 电机3电流检测通道
    MOTOR_VIB1_CURR, // 振动电机1电流检测通道
    MOTOR_VIB2_CURR  // 振动电机2电流检测通道
} motor_current_channel_t;

// ADC数据结构定义
struct adFilter_t {
    float current_integral;     // 电流积分值
    float current_ma;           // 实际电流值(mA)
    uint16_t offset;            // 偏置值
    uint16_t filtered_value;    // 滤波后的ADC值
    uint16_t peak_value;        // ADC峰值
    uint8_t is_blocked : 1;     // 堵转标志位
    uint8_t is_overcurrent : 1; // 过流标志位
    uint8_t reserved : 6;       // 保留字段
};

typedef struct {
    float iir_alpha;        // IIR滤波系数
    uint16_t filter_window; // 滤波窗口长度
} adc_param_t;

// ADC通道映射
#define MOTOR1_CURR_CH     ADC_Channel_Mux13_PC03 // 电机1 - PC03
#define MOTOR2_CURR_CH     ADC_Channel_Mux1_PA01  // 电机2 - PA01
#define MOTOR3_CURR_CH     ADC_Channel_Mux0_PA00  // 电机3 - PA00
#define MOTOR4_CURR_CH     ADC_Channel_Mux12_PC02 // 电机4 - PC02
#define MOTOR_VIB1_CURR_CH ADC_Channel_Mux11_PC01 // 振动电机1 - PC01
#define MOTOR_VIB2_CURR_CH ADC_Channel_Mux10_PC00 // 振动电机2 - PC00

// 对外接口函数
extern void bsp_AdcInit(void);                          // ADC初始化
extern uint16_t bsp_GetFilteredCurrent(uint8_t ch);     // 获取滤波后的电流值(mA)
extern void adc_data_process(const uint8_t *dir_array); // ADC融合算法处理，主循环每10ms调用，传递方向
extern uint16_t get_commutation_count(uint8_t ch);      // 获取换相计数
extern void commutation_count_reset(uint8_t ch);        // 重置换相计数
extern bool get_blocked_flag(uint8_t ch);               // 获取堵转标志位
extern uint16_t get_peak_current(uint8_t ch);           // 获取电流峰值
extern bool get_overcurrent_flag(uint8_t ch);           // 获取过流标志位

typedef struct {
    float x_hat; // 估计值
    float p;     // 估计误差协方差
    float q;     // 过程噪声协方差
    float r;     // 测量噪声协方差
    float k;     // 卡尔曼增益
} kalman_filter_t;

void kalman_init(kalman_filter_t *kf, float q, float r, float init_x);
float kalman_update(kalman_filter_t *kf, float measurement);

#endif

#endif
