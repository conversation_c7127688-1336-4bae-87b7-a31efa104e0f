#include "bsp_lin.h"
#include "FreeRTOS.h"
#include "semphr.h"

#define LIN_DATA_LEN   8
#define LIN_FRAME_SIZE (LIN_DATA_LEN + 1) // SYNC+ID+DATA+CHECKSUM

#define SLAVE_PID      0x60

static lin_role_t g_lin_role = LIN_ROLE_MASTER;

static uint8_t lin_frame_buf[LIN_FRAME_SIZE];
static volatile uint8_t lin_frame_pos      = 0;
static SemaphoreHandle_t lin_master_rx_sem = NULL;

// 从机模式相关变量
static uint8_t lin_slave_rx_id            = 0;
static uint8_t lin_slave_rx_data[8]       = {0};
static uint8_t lin_slave_rx_len           = 0;
static SemaphoreHandle_t lin_slave_rx_sem = NULL;
static uint8_t lin_slave_rx_flag          = 0;

// 主机状态机
static enum {
    LIN_STATE_WAIT_SYNC,
    LIN_STATE_WAIT_ID,
    LIN_STATE_WAIT_DATA,
    LIN_STATE_WAIT_CHECKSUM
} lin_rx_state = LIN_STATE_WAIT_SYNC;

// 从机状态机
static enum {
    SLAVE_WAIT_SYNC,
    SLAVE_WAIT_ID,
    SLAVE_WAIT_DATA,
    SLAVE_WAIT_CHECKSUM
} lin_slave_state                 = SLAVE_WAIT_SYNC;
static uint8_t lin_slave_data_pos = 0;

/**
 * @brief 初始化LIN串口端口
 */
static void lin_usart_port_init(void)
{
    stc_gpio_init_t stcGpioInit = {0};
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpioa);
    DDL_ZERO_STRUCT(stcGpioInit);
    stcGpioInit.u32Mode     = GPIO_MODE_INPUT;
    stcGpioInit.u32Pin      = GPIO_PIN_09;
    stcGpioInit.u32PullUpDn = GPIO_PULL_UP;
    GPIOA_Init(&stcGpioInit);
    DDL_ZERO_STRUCT(stcGpioInit);
    stcGpioInit.u32Mode     = GPIO_MODE_INPUT;
    stcGpioInit.u32Pin      = GPIO_PIN_10;
    stcGpioInit.u32PullUpDn = GPIO_PULL_UP;
    GPIOA_Init(&stcGpioInit);
    GPIO_PA09_AF_USART0_TXD_SET();
    GPIO_PA10_AF_USART0_RXD_SET();
}

/**
 * @brief 配置LIN串口，支持主/从模式
 * @param baudrate 波特率
 * @param role 主机/从机模式
 */
static void lin_usart_config(uint32_t baudrate, lin_role_t role)
{
    stc_usart_lin_init_t stcLINInit = {0U};
    uint32_t u32CalBaudRate         = 0U;
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralUsart0);
    stcLINInit.u32BaudRate   = baudrate;
    stcLINInit.u32SampleMode = USART_OVERSAMPLING_16;
    // TODO: 如有主从差异配置可在此补充
    u32CalBaudRate = USART_LIN_Init(HC_USART0, &stcLINInit);
    if (0U != u32CalBaudRate) {
        // 配置有效
    }
    USART_ClearIrq(HC_USART0, USART_FLAG_ALL);
    EnableNvic(USART0_FLAG_IRQn, IrqLevel4, TRUE);
    EnableNvic(USART0_RX_IRQn, IrqLevel4, TRUE);
    REG_SETBITS(HC_USART0->CR1, USART_CR1_TE);
    USART_EnableIrq(HC_USART0, USART_FLAG_TO);
    REG_SETBITS(HC_USART0->CR1, USART_CR1_RE);
    USART_EnableIrq(HC_USART0, USART_FLAG_RC);
}

/**
 * @brief LIN统一初始化接口，支持主/从一体
 * @param baudrate 波特率
 * @param role 主机/从机模式
 */
void bsp_lin_init(uint32_t baudrate, lin_role_t role)
{
    g_lin_role = role;
    lin_usart_port_init();
    lin_usart_config(baudrate, role);
    if (role == LIN_ROLE_MASTER) {
        if (lin_master_rx_sem == NULL) lin_master_rx_sem = xSemaphoreCreateBinary();
        if (lin_slave_rx_sem != NULL) vSemaphoreDelete(lin_slave_rx_sem);
        lin_frame_pos = 0;
        lin_rx_state  = LIN_STATE_WAIT_SYNC;
    } else {
        if (lin_slave_rx_sem == NULL) lin_slave_rx_sem = xSemaphoreCreateBinary();
        if (lin_master_rx_sem != NULL) vSemaphoreDelete(lin_master_rx_sem);
        lin_slave_rx_id    = 0;
        lin_slave_rx_len   = 0;
        lin_slave_data_pos = 0;
        memset(lin_slave_rx_data, 0, sizeof(lin_slave_rx_data));
        lin_slave_state = SLAVE_WAIT_SYNC;
    }
}

/**
 * @brief UART0 Rx(RC)中断服务函数，兼容主机和从机
 */
void Usart0_Rx_IRQHandler(void)
{
    unsigned char data;
    if (USART_GetFlag(HC_USART0, USART_FLAG_LBD)) {
        if (g_lin_role == LIN_ROLE_SLAVE) {
            if (lin_slave_rx_flag == 0) lin_rx_state = LIN_STATE_WAIT_SYNC;
        } else {
            lin_rx_state = LIN_STATE_WAIT_SYNC;
        }
        USART_ClearIrq(HC_USART0, USART_FLAG_LBD);
    }
    if (USART_GetFlag(HC_USART0, USART_FLAG_RC)) {
        USART_UART_ReceiveINT(HC_USART0, &data);
        if (g_lin_role == LIN_ROLE_MASTER) {
            switch (lin_rx_state) {
                case LIN_STATE_WAIT_SYNC:
                    if (data == 0x55) {
                        lin_frame_pos = 0;
                        lin_rx_state  = LIN_STATE_WAIT_ID;
                    }
                    break;
                case LIN_STATE_WAIT_ID:
                    if (data == SLAVE_PID) {
                        lin_rx_state = LIN_STATE_WAIT_DATA;
                    } else {
                        lin_rx_state = LIN_STATE_WAIT_SYNC;
                    }
                    break;
                case LIN_STATE_WAIT_DATA:
                    lin_frame_buf[lin_frame_pos++] = data;
                    if (lin_frame_pos == LIN_DATA_LEN) {
                        lin_rx_state = LIN_STATE_WAIT_CHECKSUM;
                    }
                    break;
                case LIN_STATE_WAIT_CHECKSUM:
                    lin_frame_buf[lin_frame_pos++]      = data;
                    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
                    xSemaphoreGiveFromISR(lin_master_rx_sem, &xHigherPriorityTaskWoken);
                    portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
                    lin_rx_state = LIN_STATE_WAIT_SYNC;
                    break;
                default:
                    lin_rx_state = LIN_STATE_WAIT_SYNC;
                    break;
            }
        } else {
            switch (lin_slave_state) {
                case SLAVE_WAIT_SYNC:
                    if (data == 0x55) lin_slave_state = SLAVE_WAIT_ID;
                    break;
                case SLAVE_WAIT_ID:
                    lin_slave_rx_id = data;
                    if (data == LIN_READ_PID) {
                        lin_slave_data_pos                  = 0;
                        BaseType_t xHigherPriorityTaskWoken = pdFALSE;
                        xSemaphoreGiveFromISR(lin_slave_rx_sem, &xHigherPriorityTaskWoken);
                        portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
                        lin_slave_state   = SLAVE_WAIT_SYNC;
                        lin_slave_rx_flag = 1;
                    } else if (data == LIN_WRITE_PID) {
                        lin_slave_data_pos = 0;
                        lin_slave_state    = SLAVE_WAIT_DATA;
                    } else {
                        lin_slave_state = SLAVE_WAIT_SYNC;
                    }
                    break;
                case SLAVE_WAIT_DATA:
                    lin_slave_rx_data[lin_slave_data_pos++] = data;
                    if (lin_slave_data_pos >= 8) {
                        lin_slave_rx_len = lin_slave_data_pos;
                        lin_slave_state = SLAVE_WAIT_CHECKSUM;
                    }
                    break;
                case SLAVE_WAIT_CHECKSUM:
                    if (data == lin_calculate_checksum(lin_slave_rx_id, lin_slave_rx_data, lin_slave_rx_len)) {
                        BaseType_t xHigherPriorityTaskWoken = pdFALSE;
                        xSemaphoreGiveFromISR(lin_slave_rx_sem, &xHigherPriorityTaskWoken);
                        portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
                        lin_slave_state = SLAVE_WAIT_SYNC;
                    }
                    break;
                default:
                    lin_slave_state = SLAVE_WAIT_SYNC;
                    break;
            }
        }
        USART_ClearIrq(HC_USART0, USART_FLAG_RC);
    }
}

void Usart0_Flag_IRQHandler(void)
{
    if (USART_GetFlag(HC_USART0, USART_FLAG_TO)) {
        USART_ClearFlag(HC_USART0, USART_FLAG_TO);
    }
}

/**
 * @brief 主机发送LIN帧
 * @param id 帧ID
 * @param data 数据指针
 * @param len 数据长度
 * @return 0成功，<0失败
 */
int bsp_lin_master_send_frame(uint8_t id, const uint8_t *data, uint8_t len)
{
    uint8_t id_with_parity = LIN_IDParity(id);
    en_result_t ret;
    ret = USART_LIN_Break_Sent(LIN_MASTER_USART, LIN_BREAK_LEN);
    if (ret != Ok) return -1;
    ret = USART_LIN_Sync_Sent(LIN_MASTER_USART);
    if (ret != Ok) return -2;
    USART_LIN_ID_Sent(LIN_MASTER_USART, id_with_parity);
    if (len > 0 && data != NULL) {
        ret = USART_LIN_Transmit(LIN_MASTER_USART, (uint8_t *)data, len, LIN_TIMEOUT_VALUE);
        if (ret != Ok) return -3;
        uint8_t checksum = lin_calculate_checksum(id_with_parity, data, len);
        USART_LIN_Transmit(LIN_MASTER_USART, &checksum, 1, LIN_TIMEOUT_VALUE);
        ret = USART_LIN_Break_Sent(LIN_MASTER_USART, LIN_BREAK_LEN);
        if (ret != Ok) return -4;
    }
    return 0;
}

/**
 * @brief 主机读取LIN帧（阻塞/超时）
 * @param buf 数据缓冲区
 * @param len 期望读取长度
 * @param timeout_ms 超时时间
 * @return 实际读取长度
 */
int bsp_lin_master_read(uint8_t *buf, uint8_t len, uint32_t timeout_ms)
{
    if (len != LIN_FRAME_SIZE) return 0;
    if (xSemaphoreTake(lin_master_rx_sem, pdMS_TO_TICKS(timeout_ms)) == pdTRUE) {
        for (uint8_t i = 0; i < LIN_FRAME_SIZE; i++) {
            buf[i] = lin_frame_buf[i];
        }
        return LIN_FRAME_SIZE;
    } else {
        return 0;
    }
}

/**
 * @brief 从机接收主机请求（阻塞/超时）
 * @param id 指向接收ID的指针
 * @param data 数据缓冲区
 * @param len 实际接收长度
 * @param timeout_ms 超时时间
 * @return 1成功，0超时
 */
int bsp_lin_slave_receive(uint8_t *id, uint8_t *data, uint8_t *len, uint32_t timeout_ms)
{
    if (xSemaphoreTake(lin_slave_rx_sem, pdMS_TO_TICKS(timeout_ms)) == pdTRUE) {
        if (lin_slave_rx_id == LIN_READ_PID) {
            if (id) *id = lin_slave_rx_id;
        } else if (lin_slave_rx_id == LIN_WRITE_PID) {
            if (id) *id = lin_slave_rx_id;
            if (data && lin_slave_rx_len > 0) memcpy(data, lin_slave_rx_data, lin_slave_rx_len);
            if (len) *len = lin_slave_rx_len;
        }
        return 1;
    }
    return 0;
}

/**
 * @brief 从机响应主机
 * @param id 帧ID
 * @param data 数据指针
 * @param len 数据长度
 * @return 0成功，<0失败
 */
int bsp_lin_slave_respond(uint8_t id, const uint8_t *data, uint8_t len)
{
    uint8_t checksum = lin_calculate_checksum(id, data, len);
    if (len > 0 && data != NULL) {
        USART_LIN_Transmit(LIN_MASTER_USART, (uint8_t *)data, len, LIN_TIMEOUT_VALUE);
    }
    USART_LIN_Transmit(LIN_MASTER_USART, &checksum, 1, LIN_TIMEOUT_VALUE);

    lin_slave_rx_flag = 0;

    return 0;
}

/**
 * @brief 计算校验和（根据LIN协议，可以是经典校验和或增强校验和）
 *
 * @param id 消息ID（增强校验和需要）
 * @param data 数据数组
 * @param len 数据长度
 * @return 计算出的校验和
 */
uint8_t lin_calculate_checksum(uint8_t id, const uint8_t *data, uint8_t len)
{
    uint16_t sum = 0;

    uint8_t id_parity = LIN_IDParity(id);

    // 根据LIN 2.0规范，ID为0x3C和0x3D使用经典校验和
    // 其他ID使用增强校验和（包含ID）
    bool use_classic = false;

    // 0x3C 0x3D 使用标准型校验
    if (id_parity == 0x3c || id_parity == 0x3d) {
        use_classic = true;
    }

    // 增强型校验和需要包含ID
    if (!use_classic) {
        sum = id_parity;
    }

    // 累加所有数据字节
    for (uint8_t i = 0; i < len; i++) {
        sum += *(data++);

        if (sum > 0xFF) // 进位
        {
            sum -= 0xFF;
        }
    }

    // 取反得到校验和
    return (uint8_t)(~sum);
}