#ifndef APP_LOG_H
#define APP_LOG_H

#include "log.h"

// module log开关
#define MOTOR_LOG_ENABLE   1 // Motor日志宏使能
#define LED_LOG_ENABLE     0 // LED日志宏使能
#define BLE_LOG_ENABLE     0 // BLE日志宏使能
#define BED_LOG_ENABLE     0 // 床控日志宏使能
#define RADAR_LOG_ENABLE   0 // 雷达日志宏使能
#define VIBR_LOG_ENABLE    0 // 振动日志宏使能
#define RGB_LOG_ENABLE     0 // RGB日志宏使能
#define WIFI_LOG_ENABLE    0 // WIFI日志宏使能
#define PARAM_LOG_ENABLE   0 // PARAM日志宏使能
#define BUZZER_LOG_ENABLE  0 // BUZZER日志宏使能
#define LIN_LOG_ENABLE     0 // LIN日志宏使能
#define MONITOR_LOG_ENABLE 0 // 监控日志宏使能
#define MSG_LOG_ENABLE     0 // 消息处理日志宏使能

// ========== Motor日志宏 ==========
#define MOTOR_LOG_TAG      "MOTOR"
#if MOTOR_LOG_ENABLE
    #if USE_ASYNC_LOG
        #define MOTOR_LOG_ERROR(...) LOG_ASYNC_ERROR(MOTOR_LOG_TAG, __VA_ARGS__)
        #define MOTOR_LOG_WARN(...)  LOG_ASYNC_WARN(MOTOR_LOG_TAG, __VA_ARGS__)
        #define MOTOR_LOG_INFO(...)  LOG_ASYNC_INFO(MOTOR_LOG_TAG, __VA_ARGS__)
        #define MOTOR_LOG_DEBUG(...) LOG_ASYNC_DEBUG(MOTOR_LOG_TAG, __VA_ARGS__)
    #else
        #define MOTOR_LOG_ERROR(...) LOG_ERROR(MOTOR_LOG_TAG, __VA_ARGS__)
        #define MOTOR_LOG_WARN(...)  LOG_WARN(MOTOR_LOG_TAG, __VA_ARGS__)
        #define MOTOR_LOG_INFO(...)  LOG_INFO(MOTOR_LOG_TAG, __VA_ARGS__)
        #define MOTOR_LOG_DEBUG(...) LOG_DEBUG(MOTOR_LOG_TAG, __VA_ARGS__)
    #endif
#else
    #define MOTOR_LOG_ERROR(...)
    #define MOTOR_LOG_WARN(...)
    #define MOTOR_LOG_INFO(...)
    #define MOTOR_LOG_DEBUG(...)
#endif

// ========== LED日志宏 ==========
#define LED_LOG_TAG "LED"
#if LED_LOG_ENABLE
    #if USE_ASYNC_LOG
        #define LED_LOG_ERROR(...) LOG_ASYNC_ERROR(LED_LOG_TAG, __VA_ARGS__)
        #define LED_LOG_WARN(...)  LOG_ASYNC_WARN(LED_LOG_TAG, __VA_ARGS__)
        #define LED_LOG_INFO(...)  LOG_ASYNC_INFO(LED_LOG_TAG, __VA_ARGS__)
        #define LED_LOG_DEBUG(...) LOG_ASYNC_DEBUG(LED_LOG_TAG, __VA_ARGS__)
    #else
        #define LED_LOG_ERROR(...) LOG_ERROR(LED_LOG_TAG, __VA_ARGS__)
        #define LED_LOG_WARN(...)  LOG_WARN(LED_LOG_TAG, __VA_ARGS__)
        #define LED_LOG_INFO(...)  LOG_INFO(LED_LOG_TAG, __VA_ARGS__)
        #define LED_LOG_DEBUG(...) LOG_DEBUG(LED_LOG_TAG, __VA_ARGS__)
    #endif
#else
    #define LED_LOG_ERROR(...)
    #define LED_LOG_WARN(...)
    #define LED_LOG_INFO(...)
    #define LED_LOG_DEBUG(...)
#endif

// ========== BLE日志宏 ==========
#define BLE_LOG_TAG "BLE"
#if BLE_LOG_ENABLE
    #if USE_ASYNC_LOG
        #define BLE_LOG_ERROR(...) LOG_ASYNC_ERROR(BLE_LOG_TAG, __VA_ARGS__)
        #define BLE_LOG_WARN(...)  LOG_ASYNC_WARN(BLE_LOG_TAG, __VA_ARGS__)
        #define BLE_LOG_INFO(...)  LOG_ASYNC_INFO(BLE_LOG_TAG, __VA_ARGS__)
        #define BLE_LOG_DEBUG(...) LOG_ASYNC_DEBUG(BLE_LOG_TAG, __VA_ARGS__)
    #else
        #define BLE_LOG_ERROR(...) LOG_ERROR(BLE_LOG_TAG, __VA_ARGS__)
        #define BLE_LOG_WARN(...)  LOG_WARN(BLE_LOG_TAG, __VA_ARGS__)
        #define BLE_LOG_INFO(...)  LOG_INFO(BLE_LOG_TAG, __VA_ARGS__)
        #define BLE_LOG_DEBUG(...) LOG_DEBUG(BLE_LOG_TAG, __VA_ARGS__)
    #endif
#else
    #define BLE_LOG_ERROR(...)
    #define BLE_LOG_WARN(...)
    #define BLE_LOG_INFO(...)
    #define BLE_LOG_DEBUG(...)
#endif

// ========== 床控日志宏 ==========
#define BED_LOG_TAG "BED"
#if BED_LOG_ENABLE
    #if USE_ASYNC_LOG
        #define BED_LOG_ERROR(...) LOG_ASYNC_ERROR(BED_LOG_TAG, __VA_ARGS__)
        #define BED_LOG_WARN(...)  LOG_ASYNC_WARN(BED_LOG_TAG, __VA_ARGS__)
        #define BED_LOG_INFO(...)  LOG_ASYNC_INFO(BED_LOG_TAG, __VA_ARGS__)
        #define BED_LOG_DEBUG(...) LOG_ASYNC_DEBUG(BED_LOG_TAG, __VA_ARGS__)
    #else
        #define BED_LOG_ERROR(...) LOG_ERROR(BED_LOG_TAG, __VA_ARGS__)
        #define BED_LOG_WARN(...)  LOG_WARN(BED_LOG_TAG, __VA_ARGS__)
        #define BED_LOG_INFO(...)  LOG_INFO(BED_LOG_TAG, __VA_ARGS__)
        #define BED_LOG_DEBUG(...) LOG_DEBUG(BED_LOG_TAG, __VA_ARGS__)
    #endif
#else
    #define BED_LOG_ERROR(...)
    #define BED_LOG_WARN(...)
    #define BED_LOG_INFO(...)
    #define BED_LOG_DEBUG(...)
#endif

// ========== 雷达日志宏 ==========
#define RADAR_LOG_TAG "RADAR"
#if RADAR_LOG_ENABLE
    #if USE_ASYNC_LOG
        #define RADAR_LOG_ERROR(...) LOG_ASYNC_ERROR(RADAR_LOG_TAG, __VA_ARGS__)
        #define RADAR_LOG_WARN(...)  LOG_ASYNC_WARN(RADAR_LOG_TAG, __VA_ARGS__)
        #define RADAR_LOG_INFO(...)  LOG_ASYNC_INFO(RADAR_LOG_TAG, __VA_ARGS__)
        #define RADAR_LOG_DEBUG(...) LOG_ASYNC_DEBUG(RADAR_LOG_TAG, __VA_ARGS__)
    #else
        #define RADAR_LOG_ERROR(...) LOG_ERROR(RADAR_LOG_TAG, __VA_ARGS__)
        #define RADAR_LOG_WARN(...)  LOG_WARN(RADAR_LOG_TAG, __VA_ARGS__)
        #define RADAR_LOG_INFO(...)  LOG_INFO(RADAR_LOG_TAG, __VA_ARGS__)
        #define RADAR_LOG_DEBUG(...) LOG_DEBUG(RADAR_LOG_TAG, __VA_ARGS__)
    #endif
#else
    #define RADAR_LOG_ERROR(...)
    #define RADAR_LOG_WARN(...)
    #define RADAR_LOG_INFO(...)
    #define RADAR_LOG_DEBUG(...)
#endif

// ========== 振动日志宏 ==========
#define VIBR_LOG_TAG "VIBR"
#if VIBR_LOG_ENABLE
    #if USE_ASYNC_LOG
        #define VIBR_LOG_ERROR(...) LOG_ASYNC_ERROR(VIBR_LOG_TAG, __VA_ARGS__)
        #define VIBR_LOG_WARN(...)  LOG_ASYNC_WARN(VIBR_LOG_TAG, __VA_ARGS__)
        #define VIBR_LOG_INFO(...)  LOG_ASYNC_INFO(VIBR_LOG_TAG, __VA_ARGS__)
        #define VIBR_LOG_DEBUG(...) LOG_ASYNC_DEBUG(VIBR_LOG_TAG, __VA_ARGS__)
    #else
        #define VIBR_LOG_ERROR(...) LOG_ERROR(VIBR_LOG_TAG, __VA_ARGS__)
        #define VIBR_LOG_WARN(...)  LOG_WARN(VIBR_LOG_TAG, __VA_ARGS__)
        #define VIBR_LOG_INFO(...)  LOG_INFO(VIBR_LOG_TAG, __VA_ARGS__)
        #define VIBR_LOG_DEBUG(...) LOG_DEBUG(VIBR_LOG_TAG, __VA_ARGS__)
    #endif
#else
    #define VIBR_LOG_ERROR(...)
    #define VIBR_LOG_WARN(...)
    #define VIBR_LOG_INFO(...)
    #define VIBR_LOG_DEBUG(...)
#endif

// ========== RGB日志宏 ==========
#define RGB_LOG_TAG "RGB"
#if RGB_LOG_ENABLE
    #if USE_ASYNC_LOG
        #define RGB_LOG_ERROR(...) LOG_ASYNC_ERROR(RGB_LOG_TAG, __VA_ARGS__)
        #define RGB_LOG_WARN(...)  LOG_ASYNC_WARN(RGB_LOG_TAG, __VA_ARGS__)
        #define RGB_LOG_INFO(...)  LOG_ASYNC_INFO(RGB_LOG_TAG, __VA_ARGS__)
        #define RGB_LOG_DEBUG(...) LOG_ASYNC_DEBUG(RGB_LOG_TAG, __VA_ARGS__)
    #else
        #define RGB_LOG_ERROR(...) LOG_ERROR(RGB_LOG_TAG, __VA_ARGS__)
        #define RGB_LOG_WARN(...)  LOG_WARN(RGB_LOG_TAG, __VA_ARGS__)
        #define RGB_LOG_INFO(...)  LOG_INFO(RGB_LOG_TAG, __VA_ARGS__)
        #define RGB_LOG_DEBUG(...) LOG_DEBUG(RGB_LOG_TAG, __VA_ARGS__)
    #endif
#else
    #define RGB_LOG_ERROR(...)
    #define RGB_LOG_WARN(...)
    #define RGB_LOG_INFO(...)
    #define RGB_LOG_DEBUG(...)
#endif

// ========== WIFI日志宏 ==========
#define WIFI_LOG_TAG "WIFI"
#if WIFI_LOG_ENABLE
    #if USE_ASYNC_LOG
        #define WIFI_LOG_ERROR(...) LOG_ASYNC_ERROR(WIFI_LOG_TAG, __VA_ARGS__)
        #define WIFI_LOG_WARN(...)  LOG_ASYNC_WARN(WIFI_LOG_TAG, __VA_ARGS__)
        #define WIFI_LOG_INFO(...)  LOG_ASYNC_INFO(WIFI_LOG_TAG, __VA_ARGS__)
        #define WIFI_LOG_DEBUG(...) LOG_ASYNC_DEBUG(WIFI_LOG_TAG, __VA_ARGS__)
    #else
        #define WIFI_LOG_ERROR(...) LOG_ERROR(WIFI_LOG_TAG, __VA_ARGS__)
        #define WIFI_LOG_WARN(...)  LOG_WARN(WIFI_LOG_TAG, __VA_ARGS__)
        #define WIFI_LOG_INFO(...)  LOG_INFO(WIFI_LOG_TAG, __VA_ARGS__)
        #define WIFI_LOG_DEBUG(...) LOG_DEBUG(WIFI_LOG_TAG, __VA_ARGS__)
    #endif
#else
    #define WIFI_LOG_ERROR(...)
    #define WIFI_LOG_WARN(...)
    #define WIFI_LOG_INFO(...)
    #define WIFI_LOG_DEBUG(...)
#endif

// ========== Flash日志宏 ==========
#define PARAM_TAG "PARAM"
#if PARAM_LOG_ENABLE
    #if USE_ASYNC_LOG
        #define PARAM_LOG_DEBUG(fmt, ...) LOG_ASYNC_DEBUG(PARAM_TAG, fmt, ##__VA_ARGS__)
        #define PARAM_LOG_INFO(fmt, ...)  LOG_ASYNC_INFO(PARAM_TAG, fmt, ##__VA_ARGS__)
        #define PARAM_LOG_WARN(fmt, ...)  LOG_ASYNC_WARN(PARAM_TAG, fmt, ##__VA_ARGS__)
        #define PARAM_LOG_ERROR(fmt, ...) LOG_ASYNC_ERROR(PARAM_TAG, fmt, ##__VA_ARGS__)
    #else
        #define PARAM_LOG_DEBUG(fmt, ...) LOG_DEBUG(PARAM_TAG, fmt, ##__VA_ARGS__)
        #define PARAM_LOG_INFO(fmt, ...)  LOG_INFO(PARAM_TAG, fmt, ##__VA_ARGS__)
        #define PARAM_LOG_WARN(fmt, ...)  LOG_WARN(PARAM_TAG, fmt, ##__VA_ARGS__)
        #define PARAM_LOG_ERROR(fmt, ...) LOG_ERROR(PARAM_TAG, fmt, ##__VA_ARGS__)
    #endif
#else
    #define PARAM_LOG_DEBUG(...)
    #define PARAM_LOG_INFO(...)
    #define PARAM_LOG_WARN(...)
    #define PARAM_LOG_ERROR(...)
#endif

// ========== Buzzer日志宏 ==========
#define BUZZER_TAG "BUZZER"
#if BUZZER_LOG_ENABLE
    #if USE_ASYNC_LOG
        #define BUZZER_LOG_DEBUG(fmt, ...) LOG_ASYNC_DEBUG(BUZZER_TAG, fmt, ##__VA_ARGS__)
        #define BUZZER_LOG_INFO(fmt, ...)  LOG_ASYNC_INFO(BUZZER_TAG, fmt, ##__VA_ARGS__)
        #define BUZZER_LOG_WARN(fmt, ...)  LOG_ASYNC_WARN(BUZZER_TAG, fmt, ##__VA_ARGS__)
        #define BUZZER_LOG_ERROR(fmt, ...) LOG_ASYNC_ERROR(BUZZER_TAG, fmt, ##__VA_ARGS__)
    #else
        #define BUZZER_LOG_DEBUG(fmt, ...) LOG_DEBUG(BUZZER_TAG, fmt, ##__VA_ARGS__)
        #define BUZZER_LOG_INFO(fmt, ...)  LOG_INFO(BUZZER_TAG, fmt, ##__VA_ARGS__)
        #define BUZZER_LOG_WARN(fmt, ...)  LOG_WARN(BUZZER_TAG, fmt, ##__VA_ARGS__)
        #define BUZZER_LOG_ERROR(fmt, ...) LOG_ERROR(BUZZER_TAG, fmt, ##__VA_ARGS__)
    #endif
#else
    #define BUZZER_LOG_DEBUG(...)
    #define BUZZER_LOG_INFO(...)
    #define BUZZER_LOG_WARN(...)
    #define BUZZER_LOG_ERROR(...)
#endif

// ========== LIN日志宏 ==========
#define LIN_LOG_TAG "LIN"
#if LIN_LOG_ENABLE
    #if USE_ASYNC_LOG
        #define LIN_LOG_DEBUG(...) LOG_ASYNC_DEBUG(LIN_LOG_TAG, __VA_ARGS__)
        #define LIN_LOG_INFO(...)  LOG_ASYNC_INFO(LIN_LOG_TAG, __VA_ARGS__)
        #define LIN_LOG_WARN(...)  LOG_ASYNC_WARN(LIN_LOG_TAG, __VA_ARGS__)
        #define LIN_LOG_ERROR(...) LOG_ASYNC_ERROR(LIN_LOG_TAG, __VA_ARGS__)
    #else
        #define LIN_LOG_DEBUG(...) LOG_DEBUG(LIN_LOG_TAG, __VA_ARGS__)
        #define LIN_LOG_INFO(...)  LOG_INFO(LIN_LOG_TAG, __VA_ARGS__)
        #define LIN_LOG_WARN(...)  LOG_WARN(LIN_LOG_TAG, __VA_ARGS__)
        #define LIN_LOG_ERROR(...) LOG_ERROR(LIN_LOG_TAG, __VA_ARGS__)
    #endif
#else
    #define LIN_LOG_DEBUG(...)
    #define LIN_LOG_INFO(...)
    #define LIN_LOG_WARN(...)
    #define LIN_LOG_ERROR(...)
#endif

// ========== 监控日志宏 ==========
#define MONITOR_LOG_TAG "MONITOR"
#if MONITOR_LOG_ENABLE
    #if USE_ASYNC_LOG
        #define MONITOR_LOG_DEBUG(...) LOG_ASYNC_DEBUG(MONITOR_LOG_TAG, __VA_ARGS__)
        #define MONITOR_LOG_INFO(...)  LOG_ASYNC_INFO(MONITOR_LOG_TAG, __VA_ARGS__)
        #define MONITOR_LOG_WARN(...)  LOG_ASYNC_WARN(MONITOR_LOG_TAG, __VA_ARGS__)
        #define MONITOR_LOG_ERROR(...) LOG_ASYNC_ERROR(MONITOR_LOG_TAG, __VA_ARGS__)
    #else
        #define MONITOR_LOG_DEBUG(...) LOG_DEBUG(MONITOR_LOG_TAG, __VA_ARGS__)
        #define MONITOR_LOG_INFO(...)  LOG_INFO(MONITOR_LOG_TAG, __VA_ARGS__)
        #define MONITOR_LOG_WARN(...)  LOG_WARN(MONITOR_LOG_TAG, __VA_ARGS__)
        #define MONITOR_LOG_ERROR(...) LOG_ERROR(MONITOR_LOG_TAG, __VA_ARGS__)
    #endif
#else
    #define MONITOR_LOG_DEBUG(...)
    #define MONITOR_LOG_INFO(...)
    #define MONITOR_LOG_WARN(...)
    #define MONITOR_LOG_ERROR(...)
#endif

// ========== 消息处理日志宏 ==========
#define MSG_LOG_TAG "MSG"
#if MSG_LOG_ENABLE
    #if USE_ASYNC_LOG
        #define MSG_LOG_DEBUG(...) LOG_ASYNC_DEBUG(MSG_LOG_TAG, __VA_ARGS__)        
        #define MSG_LOG_INFO(...)  LOG_ASYNC_INFO(MSG_LOG_TAG, __VA_ARGS__)
        #define MSG_LOG_WARN(...)  LOG_ASYNC_WARN(MSG_LOG_TAG, __VA_ARGS__)
        #define MSG_LOG_ERROR(...) LOG_ASYNC_ERROR(MSG_LOG_TAG, __VA_ARGS__)
    #else
        #define MSG_LOG_DEBUG(...) LOG_DEBUG(MSG_LOG_TAG, __VA_ARGS__)
        #define MSG_LOG_INFO(...)  LOG_INFO(MSG_LOG_TAG, __VA_ARGS__)
        #define MSG_LOG_WARN(...)  LOG_WARN(MSG_LOG_TAG, __VA_ARGS__)
        #define MSG_LOG_ERROR(...) LOG_ERROR(MSG_LOG_TAG, __VA_ARGS__)
    #endif
#else
    #define MSG_LOG_DEBUG(...)
    #define MSG_LOG_INFO(...)
    #define MSG_LOG_WARN(...)
    #define MSG_LOG_ERROR(...)
#endif

#endif /* APP_LOG_H */