#ifndef MSG_TASK_H
#define MSG_TASK_H

#include "app_config.h"
#include "app_log.h"
#include "bsp.h"

// 消息来源类型
typedef enum {
    MSG_SRC_BLE = 0, // 蓝牙
    MSG_SRC_NET,     // 网络
    MSG_SRC_VOICE,   // 语音
} msg_src_t;

typedef enum {
    BED_MOVE_UP = 0,
    BED_MOVE_DOWN,
    BED_MOVE_STOP,
} bed_move_action_t;

typedef enum {
    MASSAGE_PART_NONE = 0,
    MASSAGE_PART_BACK,
    MASSAGE_PART_LEG,
    MASSAGE_PART_ALL,
} massage_part_t;

typedef enum {
    MASSAGE_STRENGTH_0 = 0,
    MASSAGE_STRENGTH_1,
    MASSAGE_STRENGTH_2,
    MASSAGE_STRENGTH_3,
} massage_strength_t;

typedef enum {
    MASSAGE_MODE_NONE = 0,
    MASSAGE_MODE_CONSTANT,
    MASSAGE_MODE_PULSE,
    MASSAGE_MODE_WAVE,
    MASSAGE_MODE_PULSE1,
    MASSAGE_MODE_PULSE2,
    MASSAGE_MODE_PULSE3,
} massage_mode_t;

typedef enum {
    MASSAGE_POWER_NONE = 0,
    MASSAGE_POWER_ON,
    MASSAGE_POWER_OFF,
} massage_power_t;

typedef enum {
    RGB_NONE = 0,
    RGB_ON,
    RGB_OFF,
} rgb_action_t;

typedef enum {
    GET_MEMORY1 = 0,
    GET_MEMORY2,
    GET_MEMORY3,
    GET_MEMORY4,
    GET_MEMORY5,

    SET_MEMORY1,
    SET_MEMORY2,
    SET_MEMORY3,
    SET_MEMORY4,
    SET_MEMORY5,

    GET_MEMORY,
    SET_MEMORY,
} bed_memory_action_t;

// 模式类型
typedef enum {
    BED_MODE_FLAT = 0,    // 平躺
    BED_MODE_MOVIE,       // 观影
    BED_MODE_READ,        // 阅读
    BED_MODE_SLEEP,       // 睡眠
    BED_MODE_SNORE,       // 止鼾
    BED_MODE_DRINK,       // 酒后
    BED_MODE_RELAX,       // 放松
    BED_MODE_SPACE,       // 太空
    BED_MODE_SOFT_WAKEUP, // 柔性唤醒
    BED_MODE_PILATES,     // 普拉提
    BED_MODE_RECOVERY,    // 运动恢复
    BED_MODE_WAIST_CARE,  // 腰部呵护
    BED_MODE_AUTO_SNORE,  // 自动止鼾
    BED_MODE_FEEDING,     // 哺乳
    BED_MODE_YOGA,        // 瑜伽
    BED_MODE_SHOW,        // 展示
    BED_MODE_MASSAGE,     // 按摩
    BED_MODE_CASUAL,      // 休闲
    BED_MODE_MAX
} bed_mode_t;

// 控制命令类型
typedef enum {
    // 运动控制
    CTRL_CMD_HEAD,  // 头部
    CTRL_CMD_BACK,  // 背部
    CTRL_CMD_WAIST, // 腰部
    CTRL_CMD_LEG,   // 腿部
    CTRL_CMD_STOP,  // 停止

    // 按摩相关
    CTRL_CMD_MASSAGE,               // 按摩
    CTRL_CMD_MASSAGE_POWER,         // 按摩功率
    CTRL_CMD_MASSAGE_MODE,          // 按摩模式
    CTRL_CMD_MASSAGE_STRENGTH,      // 按摩强度
    CTRL_CMD_MASSAGE_BACK_STRENGTH, // 背部按摩强度
    CTRL_CMD_MASSAGE_LEG_STRENGTH,  // 腿部按摩强度
    CTRL_CMD_MASSAGE_TIME,          // 按摩时间
    CTRL_CMD_MASSAGE_STATUS,        // 按摩状态

    // 灯光
    CTRL_CMD_LIGHT, // 灯光

    // 模式
    CTRL_CMD_WORK_MODE, // 工作模式
    CTRL_CMD_FULL,      // 全床控制
    CTRL_CMD_MEMORY,    // 记忆

    // 智能模式
    CTRL_CMD_SMART_MODE, // 智能模式

    CTRL_CMD_MAX
} ctrl_cmd_t;

typedef struct {
    massage_part_t part;      // 部位
    bed_move_action_t action; // 动作
} bed_move_param_t;

typedef struct {
    massage_part_t part;         // 部位
    massage_mode_t mode;         // 按摩模式
    massage_strength_t strength; // 按摩强度
    uint8_t time;                // 按摩时间
} massage_param_t;

typedef union {
    uint32_t value;            // 值
    bed_move_param_t bed_move; // 床移动参数
    massage_param_t massage;   // 按摩参数
} control_param_t;

// 统一控制消息结构体
typedef struct {
    msg_src_t src;         // 来源
    ctrl_cmd_t cmd;        // 指令类型
    control_param_t param; // 参数
} control_msg_t;

// 控制消息任务初始化
void msg_task_init(void);

// 投递消息到控制消息队列
void msg_send(control_msg_t *msg);

#endif // !MSG_TASK_H
