#
# Automatically generated file. DO NOT EDIT.
# Espressif IoT Development Framework (ESP-IDF) Project Configuration
#
CONFIG_IDF_TARGET_ESP8266=y
CONFIG_IDF_TARGET="esp8266"

#
# SDK tool configuration
#
CONFIG_SDK_TOOLPREFIX="xtensa-lx106-elf-"
CONFIG_SDK_PYTHON="python"
# CONFIG_SDK_MAKE_WARN_UNDEFINED_VARIABLES is not set
CONFIG_BOOTLOADER_INIT_SPI_FLASH=y
# CONFIG_LOG_BOOTLOADER_LEVEL_NONE is not set
# CONFIG_LOG_BOOTLOADER_LEVEL_ERROR is not set
# CONFIG_LOG_BOOTLOADER_LEVEL_WARN is not set
CONFIG_LOG_BOOTLOADER_LEVEL_INFO=y
# CONFIG_LOG_BOOTLOADER_LEVEL_DEBUG is not set
# CONFIG_LOG_BOOTLOADER_LEVEL_VERBOSE is not set
CONFIG_LOG_BOOTLOADER_LEVEL=3
# CONFIG_BOOTLOADER_APP_TEST is not set
CONFIG_ESPTOOLPY_PORT="/dev/ttyUSB0"
CONFIG_ESPTOOLPY_BAUD_115200B=y
# CONFIG_ESPTOOLPY_BAUD_230400B is not set
# CONFIG_ESPTOOLPY_BAUD_921600B is not set
# CONFIG_ESPTOOLPY_BAUD_2MB is not set
# CONFIG_ESPTOOLPY_BAUD_OTHER is not set
CONFIG_ESPTOOLPY_BAUD_OTHER_VAL=115200
CONFIG_ESPTOOLPY_BAUD=115200
CONFIG_ESPTOOLPY_COMPRESSED=y
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
# CONFIG_ESPTOOLPY_FLASHMODE_QOUT is not set
# CONFIG_ESPTOOLPY_FLASHMODE_DIO is not set
# CONFIG_ESPTOOLPY_FLASHMODE_DOUT is not set
CONFIG_ESPTOOLPY_FLASHMODE="dio"
CONFIG_SPI_FLASH_MODE=0x0
# CONFIG_ESPTOOLPY_FLASHFREQ_80M is not set
CONFIG_ESPTOOLPY_FLASHFREQ_40M=y
# CONFIG_ESPTOOLPY_FLASHFREQ_26M is not set
# CONFIG_ESPTOOLPY_FLASHFREQ_20M is not set
CONFIG_ESPTOOLPY_FLASHFREQ="40m"
CONFIG_SPI_FLASH_FREQ=0x0
# CONFIG_ESPTOOLPY_FLASHSIZE_1MB is not set
CONFIG_ESPTOOLPY_FLASHSIZE_2MB=y
# CONFIG_ESPTOOLPY_FLASHSIZE_4MB is not set
# CONFIG_ESPTOOLPY_FLASHSIZE_8MB is not set
# CONFIG_ESPTOOLPY_FLASHSIZE_16MB is not set
CONFIG_ESPTOOLPY_FLASHSIZE="2MB"
CONFIG_SPI_FLASH_SIZE=0x200000
CONFIG_ESPTOOLPY_BEFORE_RESET=y
# CONFIG_ESPTOOLPY_BEFORE_NORESET is not set
CONFIG_ESPTOOLPY_BEFORE="default_reset"
CONFIG_ESPTOOLPY_AFTER_HARD_RESET=y
# CONFIG_ESPTOOLPY_AFTER_SOFT_RESET is not set
# CONFIG_ESPTOOLPY_AFTER_NORESET is not set
CONFIG_ESPTOOLPY_AFTER="hard_reset"
# CONFIG_ESPTOOLPY_MONITOR_BAUD_9600B is not set
# CONFIG_ESPTOOLPY_MONITOR_BAUD_57600B is not set
CONFIG_ESPTOOLPY_MONITOR_BAUD_74880B=y
# CONFIG_ESPTOOLPY_MONITOR_BAUD_115200B is not set
# CONFIG_ESPTOOLPY_MONITOR_BAUD_230400B is not set
# CONFIG_ESPTOOLPY_MONITOR_BAUD_921600B is not set
# CONFIG_ESPTOOLPY_MONITOR_BAUD_2MB is not set
# CONFIG_ESPTOOLPY_MONITOR_BAUD_OTHER is not set
CONFIG_ESPTOOLPY_MONITOR_BAUD_OTHER_VAL=74880
CONFIG_ESPTOOLPY_MONITOR_BAUD=74880
CONFIG_PARTITION_TABLE_SINGLE_APP=y
# CONFIG_PARTITION_TABLE_TWO_OTA is not set
# CONFIG_PARTITION_TABLE_CUSTOM is not set
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_OFFSET=0x8000
CONFIG_PARTITION_TABLE_FILENAME="partitions_singleapp.csv"
CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG=y
# CONFIG_COMPILER_OPTIMIZATION_LEVEL_RELEASE is not set
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE=y
# CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT is not set
# CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE is not set
# CONFIG_COMPILER_CXX_EXCEPTIONS is not set
CONFIG_COMPILER_STACK_CHECK_MODE_NONE=y
# CONFIG_COMPILER_STACK_CHECK_MODE_NORM is not set
# CONFIG_COMPILER_STACK_CHECK_MODE_STRONG is not set
# CONFIG_COMPILER_STACK_CHECK_MODE_ALL is not set
# CONFIG_COMPILER_STACK_CHECK is not set
# CONFIG_COMPILER_WARN_WRITE_STRINGS is not set
CONFIG_APP_UPDATE_CHECK_APP_SUM=y
# CONFIG_APP_UPDATE_CHECK_APP_HASH is not set
# CONFIG_AWS_IOT_SDK is not set
# CONFIG_USING_ESP_CONSOLE is not set
CONFIG_ESP8266_DEFAULT_CPU_FREQ_80=y
# CONFIG_ESP8266_DEFAULT_CPU_FREQ_160 is not set
CONFIG_ESP8266_DEFAULT_CPU_FREQ_MHZ=80
CONFIG_NEWLIB_STDOUT_LINE_ENDING_CRLF=y
# CONFIG_NEWLIB_STDOUT_LINE_ENDING_LF is not set
# CONFIG_NEWLIB_STDOUT_LINE_ENDING_CR is not set
CONFIG_ESP_FILENAME_MACRO_NO_PATH=y
# CONFIG_ESP_FILENAME_MACRO_RAW is not set
# CONFIG_ESP_FILENAME_MACRO_NULL is not set
CONFIG_USING_NEW_ETS_VPRINTF=y
# CONFIG_LINK_ETS_PRINTF_TO_IRAM is not set
# CONFIG_SOC_FULL_ICACHE is not set
CONFIG_SOC_IRAM_SIZE=0xC000
CONFIG_CONSOLE_UART_DEFAULT=y
# CONFIG_CONSOLE_UART_CUSTOM is not set
# CONFIG_CONSOLE_UART_NONE is not set
CONFIG_CONSOLE_UART_NUM=0
CONFIG_CONSOLE_UART_BAUDRATE=74880
# CONFIG_UART0_SWAP_IO is not set
# CONFIG_DISABLE_ROM_UART_PRINT is not set
# CONFIG_PANIC_FULL_STACK is not set
# CONFIG_ESP_PANIC_PRINT_HALT is not set
CONFIG_ESP_PANIC_PRINT_REBOOT=y
# CONFIG_ESP_PANIC_SILENT_REBOOT is not set
CONFIG_MAIN_TASK_STACK_SIZE=3584
CONFIG_TASK_WDT=y
CONFIG_TASK_WDT_PANIC=y
# CONFIG_TASK_WDT_TIMEOUT_13N is not set
# CONFIG_TASK_WDT_TIMEOUT_14N is not set
CONFIG_TASK_WDT_TIMEOUT_15N=y
CONFIG_TASK_WDT_TIMEOUT_S=15
CONFIG_RESET_REASON=y
CONFIG_WIFI_PPT_TASKSTACK_SIZE=2048
CONFIG_EVENT_LOOP_STACK_SIZE=2048
CONFIG_ESP8266_CORE_GLOBAL_DATA_LINK_IRAM=y
# CONFIG_ESP8266_OTA_FROM_OLD is not set
# CONFIG_ESP8266_BOOT_COPY_APP is not set
CONFIG_ESP_ERR_TO_NAME_LOOKUP=y
CONFIG_ESP8266_TIME_SYSCALL_USE_FRC1=y
# CONFIG_ESP8266_TIME_SYSCALL_USE_NONE is not set
CONFIG_SCAN_AP_MAX=32
CONFIG_WIFI_TX_RATE_SEQUENCE_FROM_HIGH=y
# CONFIG_ESP8266_WIFI_QOS_ENABLED is not set
# CONFIG_ESP8266_WIFI_AMPDU_RX_ENABLED is not set
# CONFIG_ESP8266_WIFI_AMSDU_ENABLED is not set
CONFIG_ESP8266_WIFI_RX_BUFFER_NUM=16
CONFIG_ESP8266_WIFI_LEFT_CONTINUOUS_RX_BUFFER_NUM=4
CONFIG_ESP8266_WIFI_RX_PKT_NUM=7
CONFIG_ESP8266_WIFI_TX_PKT_NUM=6
CONFIG_ESP8266_WIFI_NVS_ENABLED=y
CONFIG_ESP8266_WIFI_CONNECT_OPEN_ROUTER_WHEN_PWD_IS_SET=y
# CONFIG_ESP8266_WIFI_DEBUG_LOG_ENABLE is not set
CONFIG_ESP_PHY_CALIBRATION_AND_DATA_STORAGE=y
# CONFIG_ESP_PHY_INIT_DATA_IN_PARTITION is not set
CONFIG_ESP_PHY_INIT_DATA_VDD33_CONST=33
CONFIG_ESP8266_PHY_MAX_WIFI_TX_POWER=20
# CONFIG_ESP8266_HSPI_HIGH_THROUGHPUT is not set
CONFIG_ESP_HTTP_CLIENT_ENABLE_HTTPS=y
CONFIG_HTTP_BUF_SIZE=512
CONFIG_HTTPD_MAX_REQ_HDR_LEN=512
CONFIG_HTTPD_MAX_URI_LEN=512
CONFIG_OTA_BUF_SIZE=256
# CONFIG_OTA_ALLOW_HTTP is not set
# CONFIG_DISABLE_FREERTOS is not set
# CONFIG_FREERTOS_ENABLE_REENT is not set
CONFIG_FREERTOS_HZ=100
CONFIG_FREERTOS_MAX_HOOK=2
CONFIG_FREERTOS_IDLE_TASK_STACKSIZE=1024
CONFIG_FREERTOS_ISR_STACKSIZE=512
# CONFIG_FREERTOS_EXTENED_HOOKS is not set
CONFIG_FREERTOS_GLOBAL_DATA_LINK_IRAM=y
# CONFIG_FREERTOS_CODE_LINK_TO_IRAM is not set
CONFIG_FREERTOS_TIMER_STACKSIZE=2048
CONFIG_TASK_SWITCH_FASTER=y
# CONFIG_USE_QUEUE_SETS is not set
# CONFIG_ENABLE_FREERTOS_SLEEP is not set
# CONFIG_FREERTOS_USE_TRACE_FACILITY is not set
# CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS is not set
# CONFIG_HEAP_DISABLE_IRAM is not set
# CONFIG_LOG_DEFAULT_LEVEL_NONE is not set
# CONFIG_LOG_DEFAULT_LEVEL_ERROR is not set
# CONFIG_LOG_DEFAULT_LEVEL_WARN is not set
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
# CONFIG_LOG_DEFAULT_LEVEL_DEBUG is not set
# CONFIG_LOG_DEFAULT_LEVEL_VERBOSE is not set
CONFIG_LOG_DEFAULT_LEVEL=3
CONFIG_LOG_COLORS=y
# CONFIG_LOG_SET_LEVEL is not set
# CONFIG_LWIP_USE_IRAM is not set
# CONFIG_LWIP_HIGH_THROUGHPUT is not set
CONFIG_LWIP_GLOBAL_DATA_LINK_IRAM=y
CONFIG_TCPIP_RECVMBOX_SIZE=32
CONFIG_LWIP_ARP_TABLE_SIZE=10
CONFIG_LWIP_ARP_MAXAGE=300
# CONFIG_LWIP_ESP_GRATUITOUS_ARP is not set
CONFIG_LWIP_SOCKET_MULTITHREAD=y
# CONFIG_ENABLE_NONBLOCK_SPEEDUP is not set
CONFIG_SET_SOLINGER_DEFAULT=y
CONFIG_ESP_UDP_SYNC_SEND=y
CONFIG_ESP_UDP_SYNC_RETRY_MAX=5
CONFIG_LWIP_MAX_SOCKETS=10
CONFIG_LWIP_SO_REUSE=y
CONFIG_LWIP_SO_REUSE_RXTOALL=y
# CONFIG_LWIP_SO_RCVBUF is not set
CONFIG_LWIP_RECV_BUFSIZE_DEFAULT=11680
CONFIG_LWIP_TCP_CLOSE_TIMEOUT_MS_DEFAULT=10000
# CONFIG_LWIP_IP_FRAG is not set
# CONFIG_LWIP_IP_REASSEMBLY is not set
CONFIG_LWIP_IP_REASS_MAX_PBUFS=10
# CONFIG_LWIP_IP_SOF_BROADCAST is not set
# CONFIG_LWIP_IP_SOF_BROADCAST_RECV is not set
CONFIG_LWIP_ICMP=y
# CONFIG_LWIP_MULTICAST_PING is not set
# CONFIG_LWIP_BROADCAST_PING is not set
# CONFIG_LWIP_RAW is not set
CONFIG_LWIP_DHCP_DOES_ARP_CHECK=y
CONFIG_LWIP_DHCP_MAX_NTP_SERVERS=1
CONFIG_LWIP_DHCPS_LEASE_UNIT=60
CONFIG_LWIP_DHCPS_MAX_STATION_NUM=8
CONFIG_LWIP_DHCP_DISCOVER_RETRANSMISSION_INTERVAL=250
# CONFIG_LWIP_AUTOIP is not set
CONFIG_LWIP_IGMP=y
CONFIG_ESP_DNS=y
CONFIG_DNS_MAX_SERVERS=3
# CONFIG_LWIP_NETIF_LOOPBACK is not set
# CONFIG_TCP_HIGH_SPEED_RETRANSMISSION is not set
CONFIG_LWIP_MAX_ACTIVE_TCP=5
CONFIG_LWIP_MAX_LISTENING_TCP=8
CONFIG_TCP_MAXRTX=12
CONFIG_TCP_SYNMAXRTX=6
CONFIG_TCP_MSS=1460
CONFIG_TCP_SND_BUF_DEFAULT=2920
CONFIG_TCP_WND_DEFAULT=5840
CONFIG_TCP_RECVMBOX_SIZE=6
CONFIG_TCP_QUEUE_OOSEQ=y
CONFIG_TCP_OVERSIZE_MSS=y
# CONFIG_TCP_OVERSIZE_QUARTER_MSS is not set
# CONFIG_TCP_OVERSIZE_DISABLE is not set
# CONFIG_LWIP_TCP_TIMESTAMPS is not set
CONFIG_LWIP_MAX_UDP_PCBS=4
CONFIG_UDP_RECVMBOX_SIZE=6
CONFIG_TCPIP_TASK_STACK_SIZE=2048
CONFIG_LWIP_MAX_RAW_PCBS=4
# CONFIG_LWIP_IPV6 is not set
# CONFIG_LWIP_STATS is not set
# CONFIG_ESP_LWIP_MEM_DBG is not set
# CONFIG_LWIP_DEBUG is not set
CONFIG_MBEDTLS_INTERNAL_MEM_ALLOC=y
# CONFIG_MBEDTLS_DEFAULT_MEM_ALLOC is not set
# CONFIG_MBEDTLS_CUSTOM_MEM_ALLOC is not set
CONFIG_MBEDTLS_ASYMMETRIC_CONTENT_LEN=y
CONFIG_MBEDTLS_SSL_IN_CONTENT_LEN=16384
CONFIG_MBEDTLS_SSL_OUT_CONTENT_LEN=4096
# CONFIG_MBEDTLS_DEBUG is not set
CONFIG_MBEDTLS_HARDWARE_AES=y
# CONFIG_MBEDTLS_HARDWARE_MPI is not set
CONFIG_MBEDTLS_HARDWARE_SHA=y
CONFIG_MBEDTLS_HAVE_TIME=y
# CONFIG_MBEDTLS_HAVE_TIME_DATE is not set
CONFIG_MBEDTLS_TLS_SERVER_AND_CLIENT=y
# CONFIG_MBEDTLS_TLS_SERVER_ONLY is not set
# CONFIG_MBEDTLS_TLS_CLIENT_ONLY is not set
# CONFIG_MBEDTLS_TLS_DISABLED is not set
CONFIG_MBEDTLS_TLS_SERVER=y
CONFIG_MBEDTLS_TLS_CLIENT=y
CONFIG_MBEDTLS_TLS_ENABLED=y
# CONFIG_MBEDTLS_PSK_MODES is not set
CONFIG_MBEDTLS_KEY_EXCHANGE_RSA=y
CONFIG_MBEDTLS_KEY_EXCHANGE_DHE_RSA=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_RSA=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_RSA=y
CONFIG_MBEDTLS_SSL_RENEGOTIATION=y
# CONFIG_MBEDTLS_SSL_PROTO_SSL3 is not set
CONFIG_MBEDTLS_SSL_PROTO_TLS1=y
CONFIG_MBEDTLS_SSL_PROTO_TLS1_1=y
CONFIG_MBEDTLS_SSL_PROTO_TLS1_2=y
# CONFIG_MBEDTLS_SSL_PROTO_DTLS is not set
CONFIG_MBEDTLS_SSL_ALPN=y
CONFIG_MBEDTLS_CLIENT_SSL_SESSION_TICKETS=y
CONFIG_MBEDTLS_SERVER_SSL_SESSION_TICKETS=y
CONFIG_MBEDTLS_AES_C=y
# CONFIG_MBEDTLS_CAMELLIA_C is not set
# CONFIG_MBEDTLS_DES_C is not set
CONFIG_MBEDTLS_RC4_DISABLED=y
# CONFIG_MBEDTLS_RC4_ENABLED_NO_DEFAULT is not set
# CONFIG_MBEDTLS_RC4_ENABLED is not set
# CONFIG_MBEDTLS_BLOWFISH_C is not set
# CONFIG_MBEDTLS_XTEA_C is not set
CONFIG_MBEDTLS_CCM_C=y
CONFIG_MBEDTLS_GCM_C=y
# CONFIG_MBEDTLS_RIPEMD160_C is not set
CONFIG_MBEDTLS_PEM_PARSE_C=y
CONFIG_MBEDTLS_PEM_WRITE_C=y
CONFIG_MBEDTLS_X509_CRL_PARSE_C=y
CONFIG_MBEDTLS_X509_CSR_PARSE_C=y
CONFIG_MBEDTLS_ECP_C=y
CONFIG_MBEDTLS_ECDH_C=y
CONFIG_MBEDTLS_ECDSA_C=y
CONFIG_MBEDTLS_ECP_DP_SECP192R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP224R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP256R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP384R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP521R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP192K1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP224K1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_SECP256K1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_BP256R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_BP384R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_BP512R1_ENABLED=y
CONFIG_MBEDTLS_ECP_DP_CURVE25519_ENABLED=y
CONFIG_MBEDTLS_ECP_NIST_OPTIM=y
# CONFIG_ENABLE_MDNS is not set
# CONFIG_MQTT_USING_ESP is not set
CONFIG_MQTT_USING_IBM=y
CONFIG_V3_1=y
# CONFIG_V3_1_1 is not set
CONFIG_DEFAULT_MQTT_VERSION=3
CONFIG_MQTT_CLIENT_ID="espressif_sample"
CONFIG_MQTT_KEEP_ALIVE=30
CONFIG_MQTT_USERNAME="espressif"
CONFIG_MQTT_PASSWORD="admin"
CONFIG_CLEAN_SESSION=y
# CONFIG_KEEP_SESSION is not set
CONFIG_DEFAULT_MQTT_SESSION=1
CONFIG_NO_TLS=y
# CONFIG_TLS_VERIFY_NONE is not set
# CONFIG_TLS_VERIFY_PEER is not set
# CONFIG_TLS_VERIFY_CLIENT is not set
CONFIG_DEFAULT_MQTT_SECURITY=0
CONFIG_MQTT_SEND_BUFFER=2048
CONFIG_MQTT_RECV_BUFFER=2048
CONFIG_MQTT_SEND_CYCLE=30000
CONFIG_MQTT_RECV_CYCLE=0
CONFIG_MQTT_PING_TIMEOUT=3000
# CONFIG_NEWLIB_LIBRARY_LEVEL_NORMAL is not set
# CONFIG_NEWLIB_LIBRARY_LEVEL_NANO is not set
CONFIG_NEWLIB_LIBRARY_LEVEL_FLOAT_NANO=y
# CONFIG_NEWLIB_LIBRARY_CUSTOMER is not set
# CONFIG_OPENSSL_DEBUG is not set
CONFIG_OPENSSL_ASSERT_DO_NOTHING=y
# CONFIG_OPENSSL_ASSERT_EXIT is not set
# CONFIG_ENABLE_PTHREAD is not set
# CONFIG_USING_SPIFFS is not set
CONFIG_IP_LOST_TIMER_INTERVAL=120
CONFIG_TCPIP_ADAPTER_GLOBAL_DATA_LINK_IRAM=y
# CONFIG_util_assert is not set
CONFIG_ESP_SHA=y
CONFIG_ESP_AES=y
CONFIG_ESP_MD5=y
CONFIG_ESP_ARC4=y
# CONFIG_USING_ESP_VFS is not set
# CONFIG_ENABLE_UNIFIED_PROVISIONING is not set
CONFIG_LTM_FAST=y

# Deprecated options for backward compatibility
CONFIG_TARGET_PLATFORM="esp8266"
CONFIG_TOOLPREFIX="xtensa-lx106-elf-"
# CONFIG_MAKE_WARN_UNDEFINED_VARIABLES is not set
CONFIG_FLASHMODE_QIO=y
# CONFIG_FLASHMODE_QOUT is not set
# CONFIG_FLASHMODE_DIO is not set
# CONFIG_FLASHMODE_DOUT is not set
# CONFIG_MONITOR_BAUD_9600B is not set
# CONFIG_MONITOR_BAUD_57600B is not set
CONFIG_MONITOR_BAUD_74880B=y
# CONFIG_MONITOR_BAUD_115200B is not set
# CONFIG_MONITOR_BAUD_230400B is not set
# CONFIG_MONITOR_BAUD_921600B is not set
# CONFIG_MONITOR_BAUD_2MB is not set
# CONFIG_MONITOR_BAUD_OTHER is not set
CONFIG_MONITOR_BAUD_OTHER_VAL=74880
CONFIG_MONITOR_BAUD=74880
CONFIG_OPTIMIZATION_LEVEL_DEBUG=y
# CONFIG_OPTIMIZATION_LEVEL_RELEASE is not set
CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED=y
# CONFIG_OPTIMIZATION_ASSERTIONS_SILENT is not set
# CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED is not set
# CONFIG_CXX_EXCEPTIONS is not set
CONFIG_STACK_CHECK_NONE=y
# CONFIG_STACK_CHECK_NORM is not set
# CONFIG_STACK_CHECK_STRONG is not set
# CONFIG_STACK_CHECK_ALL is not set
# CONFIG_STACK_CHECK is not set
# CONFIG_WARN_WRITE_STRINGS is not set
# End of deprecated options
