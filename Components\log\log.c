#include "log.h"
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include "FreeRTOS.h"
#include "queue.h"
#include "task.h"

#if LOG_GLOBAL_ENABLE

/* 静态变量 */
static log_level_t s_log_level     = LOG_LEVEL_INFO;
static log_output_fn s_output_func = NULL; // 输出函数指针
static const char *level_str[]     = {
    "NONE",
    "ERROR",
    "WARN",
    "INFO",
    "DEBUG"};

/* 日志缓冲区 */
static char log_buffer[256];

#if USE_ASYNC_LOG
/* ================== 异步日志相关 ================== */
#define LOG_TASK_PRIORITY   tskIDLE_PRIORITY + 1
#define LOG_TASK_STACK_SIZE 128
#define LOG_QUEUE_LEN       16
#define LOG_MSG_MAX_LEN     128

typedef struct {
    char tag[16];
    log_level_t level;
    char msg[LOG_MSG_MAX_LEN];
} log_msg_t;

static QueueHandle_t log_queue      = NULL;
static TaskHandle_t log_task_handle = NULL;

/**
 * @brief 异步日志任务入口
 *
 * @param param 任务参数
 */
static void log_async_task_entry(void *param)
{
    (void)param;
    log_msg_t logmsg;
    for (;;) {
        if (xQueueReceive(log_queue, &logmsg, portMAX_DELAY) == pdPASS) {
            // 格式化输出
            if (s_output_func) {
                int len = snprintf(log_buffer, sizeof(log_buffer) - 1, "[%s][%s] %s\r\n",
                                   level_str[logmsg.level], logmsg.tag, logmsg.msg);
                if (len > 0 && len < sizeof(log_buffer)) {
                    s_output_func(log_buffer, len);
                }
            } else {
                // 默认输出到串口
                printf("[%s][%s] %s\r\n", level_str[logmsg.level], logmsg.tag, logmsg.msg);
            }
        }
    }
}

/**
 * @brief 异步日志任务初始化
 */
void log_async_task_init(void)
{
    if (log_queue == NULL) {
        log_queue = xQueueCreate(LOG_QUEUE_LEN, sizeof(log_msg_t));
    }
    if (log_task_handle == NULL) {
        xTaskCreate(log_async_task_entry,
                    "log_task",
                    LOG_TASK_STACK_SIZE,
                    NULL,
                    LOG_TASK_PRIORITY,
                    &log_task_handle);
    }
}

/**
 * @brief 异步日志接口
 *
 * @param tag 日志标签
 * @param level 日志级别
 * @param format 日志格式
 */
void log_async(const char *tag, log_level_t level, const char *format, ...)
{
    if (log_queue == NULL) return;
    log_msg_t logmsg = {0};
    strncpy(logmsg.tag, tag, sizeof(logmsg.tag) - 1);
    logmsg.level = level;
    va_list args;
    va_start(args, format);
    vsnprintf(logmsg.msg, sizeof(logmsg.msg), format, args);
    va_end(args);
    xQueueSend(log_queue, &logmsg, 0); // 非阻塞
}
#endif // USE_ASYNC_LOG

/* ================== 原有同步日志实现 ================== */

/* 初始化日志模块 */
void log_init(log_level_t level)
{
    s_log_level = level;
}

/* 设置日志级别 */
void log_set_level(log_level_t level)
{
    s_log_level = level;
}

/* 设置日志输出函数 */
void log_set_output(log_output_fn output_func)
{
    s_output_func = output_func;
}

/* 内部通用日志打印函数 */
static void log_write(log_level_t level, const char *color, const char *tag, const char *format, va_list args)
{
    /* 检查日志级别 */
    if (level > s_log_level || s_output_func == NULL) {
        return;
    }

    /* 格式化日志头部 */
    int len = snprintf(log_buffer, sizeof(log_buffer) - 1, "%s[%s][%s] ",
                       color, level_str[level], tag);

    /* 检查头部格式化是否成功 */
    if (len < 0 || len >= sizeof(log_buffer) - 1) {
        return; // 头部格式化失败，直接返回
    }

    /* 格式化日志内容 */
    vsnprintf(log_buffer + len, sizeof(log_buffer) - len - 1, format, args);

    /* 添加换行和颜色复位 */
    len = strlen(log_buffer);
    if (len < sizeof(log_buffer) - 3) {
        snprintf(log_buffer + len, sizeof(log_buffer) - len - 1, "%s\r\n", LOG_COLOR_RESET);
    }

    /* 确保字符串以null结尾 */
    log_buffer[sizeof(log_buffer) - 1] = '\0';

    /* 通过回调函数输出日志 */
    s_output_func(log_buffer, strlen(log_buffer));
}

/* 错误级别日志 */
void log_error(const char *tag, const char *format, ...)
{
    va_list args;
    va_start(args, format);
    log_write(LOG_LEVEL_ERROR, LOG_COLOR_RED, tag, format, args);
    va_end(args);
}

/* 警告级别日志 */
void log_warn(const char *tag, const char *format, ...)
{
    va_list args;
    va_start(args, format);
    log_write(LOG_LEVEL_WARN, LOG_COLOR_YELLOW, tag, format, args);
    va_end(args);
}

/* 信息级别日志 */
void log_info(const char *tag, const char *format, ...)
{
    va_list args;
    va_start(args, format);
    log_write(LOG_LEVEL_INFO, LOG_COLOR_GREEN, tag, format, args);
    va_end(args);
}

/* 调试级别日志 */
void log_debug(const char *tag, const char *format, ...)
{
    va_list args;
    va_start(args, format);
    log_write(LOG_LEVEL_DEBUG, LOG_COLOR_BLUE, tag, format, args);
    va_end(args);
}

#endif /* LOG_GLOBAL_ENABLE */