{\rtf1\ansi\ansicpg936\deff0\deflang1033\deflangfe2052{\fonttbl{\f0\fmodern Consolas;}{\f1\fnil\fcharset129 Courier New;}}
{\colortbl ;\red20\green20\blue20;\red142\green142\blue142;\red9\green118\blue72;}
\viewkind4\uc1\pard\cf1\highlight2\lang2052\f0\fs20 
\par \cf0\highlight0  ets Jan  8 2013,rst cause:2, boot mode:(3,7)\cf1\highlight2 
\par 
\par \cf0\highlight0 load 0x40100000, len 7196, room 16\cf1\highlight2 
\par \cf0\highlight0 tail 12\cf1\highlight2 
\par \cf0\highlight0 chksum 0xc1\cf1\highlight2 
\par \cf0\highlight0 ho 0 tail 12 room 4\cf1\highlight2 
\par \cf0\highlight0 load 0x3ffe8408, len 24, room 12\cf1\highlight2 
\par \cf0\highlight0 tail 12\cf1\highlight2 
\par \cf0\highlight0 chksum 0x48\cf1\highlight2 
\par \cf0\highlight0 ho 0 tail 12 room 4\cf1\highlight2 
\par \cf0\highlight0 load 0x3ffe8420, len 3548, room 12\cf1\highlight2 
\par \cf0\highlight0 tail 0\cf1\highlight2 
\par \cf0\highlight0 chksum 0x18\cf1\highlight2 
\par \cf3\highlight0 I (49) boot: ESP-IDF v3.2-252-g73bf28b-dirty 2nd stage bootloader\cf1\highlight2 
\par \cf3\highlight0 I (49) boot: compile time 10:40:57\cf1\highlight2 
\par \cf3\highlight0 I (50) qio_mode: Enabling default flash chip QIO\cf1\highlight2 
\par \cf3\highlight0 I (58) boot: SPI Speed      : 40MHz\cf1\highlight2 
\par \cf3\highlight0 I (64) boot: SPI Mode       : QIO\cf1\highlight2 
\par \cf3\highlight0 I (70) boot: SPI Flash Size : 2MB\cf1\highlight2 
\par \cf3\highlight0 I (76) boot: Partition Table:\cf1\highlight2 
\par \cf3\highlight0 I (82) boot: ## Label            Usage          Type ST Offset   Length\cf1\highlight2 
\par \cf3\highlight0 I (93) boot:  0 nvs              WiFi data        01 02 00009000 00006000\cf1\highlight2 
\par \cf3\highlight0 I (105) boot:  1 phy_init         RF data          01 01 0000f000 00001000\cf1\highlight2 
\par \cf3\highlight0 I (116) boot:  2 factory          factory app      00 00 00010000 000f0000\cf1\highlight2 
\par \cf3\highlight0 I (128) boot: End of partition table\cf1\highlight2 
\par \cf3\highlight0 I (135) esp_image: segment 0: paddr=0x00010010 vaddr=0x40210010 size=0x26ee0 (159456) map\cf1\highlight2 
\par \cf3\highlight0 I (201) esp_image: segment 1: paddr=0x00036ef8 vaddr=0x40236ef0 size=0x05e00 ( 24064) map\cf1\highlight2 
\par \cf3\highlight0 I (210) esp_image: segment 2: paddr=0x0003cd00 vaddr=0x3ffe8000 size=0x005f4 (  1524) load\cf1\highlight2 
\par \cf3\highlight0 I (212) esp_image: segment 3: paddr=0x0003d2fc vaddr=0x40100000 size=0x00a30 (  2608) load\cf1\highlight2 
\par \cf3\highlight0 I (226) esp_image: segment 4: paddr=0x0003dd34 vaddr=0x40100a30 size=0x05328 ( 21288) load\cf1\highlight2 
\par \cf3\highlight0 I (246) boot: Loaded app from partition at offset 0x10000\cf1\highlight2 
\par \cf3\highlight0 I (268) system_api: Base MAC address is not set, read default base MAC address from EFUSE\cf1\highlight2 
\par \cf3\highlight0 I (273) system_api: Base MAC address is not set, read default base MAC address from EFUSE\cf1\highlight2 
\par \cf0\highlight0 phy_version: 1155.0, 6cb3053, Nov 11 2019, 17:31:08, RTOS new\cf1\highlight2 
\par \cf3\highlight0 I (328) phy_init: phy ver: 1155_0\cf1\highlight2 
\par \cf3\highlight0 I (331) reset_reason: RTC reset 2 wakeup 0 store 0, reason is 2\cf1\highlight2 
\par \cf0\highlight0 Hello world!\cf1\highlight2 
\par \cf0\highlight0 This is ESP8266 chip with 1 CPU cores, WiFi, silicon revision 1, 2MB external flash\cf1\highlight2 
\par \cf3\highlight0 [I/FAL] Flash Abstraction Layer (V0.5.0) initialize success.\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][kv][env] (/home/<USER>/esp/esp8266_spi_flash/components/FlashDB/src/fdb_kvdb.c:1599) KVDB in partition fdb_kvdb1, size is zu bytes.\cf1\highlight2 
\par \cf0\highlight0 [FlashDB] FlashDB V1.0.0 beta is initialize success.\cf1\highlight2 
\par \cf0\highlight0 [FlashDB] You can get the latest version on https://github.com/armink/FlashDB .\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][kvdb][basic] get the 'boot_count' value is 22\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][kvdb][basic] set the 'boot_count' value to 23\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][kvdb][string] create the 'temp' string KV, value is: 36C\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][kvdb][string] get the 'temp' value is: 36C\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][kvdb][string] set 'temp' value to 38C\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][kvdb][string] delete the 'temp' finish\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][kvdb][blob] create the 'temp' blob KV, value is: 36\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][kvdb][blob] get the 'temp' value is: 36\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][kvdb][blob] set 'temp' value to 38\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][kvdb][blob] delete the 'temp' finish\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][tsl][log] (/home/<USER>/esp/esp8266_spi_flash/components/FlashDB/src/fdb_tsdb.c:759) TSDB (log) oldest sectors is 0x00000000, current using sector is 0x00000000.\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] append the new status.temp (36) and status.humi (85)\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] append the new status.temp (38) and status.humi (90)\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 1, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 2, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 3, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 4, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 5, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 6, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 7, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 8, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 9, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 10, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 11, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 12, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 13, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 14, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 15, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 16, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 17, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 18, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 19, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 20, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 21, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 22, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 23, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 24, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 25, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 26, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 27, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 28, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 29, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 30, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 31, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 32, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 33, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 34, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 35, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 36, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 37, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 38, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 39, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 40, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 41, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 42, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 43, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 44, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 45, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 46, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 1, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 2, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 3, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 4, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 5, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 6, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 7, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 8, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 9, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 10, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 11, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 12, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 13, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 14, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 15, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 16, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 17, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 18, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 19, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 20, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 21, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 22, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 23, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 24, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 25, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 26, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 27, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 28, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 29, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 30, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 31, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 32, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 33, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 34, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 35, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 36, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 37, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 38, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 39, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 40, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 41, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 42, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 43, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 44, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 45, temp: 36, humi: 85\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] queried a TSL: time: 46, temp: 38, humi: 90\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] query count is: 2\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 1) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 2) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 3) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 4) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 5) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 6) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 7) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 8) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 9) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 10) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 11) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 12) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 13) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 14) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 15) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 16) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 17) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 18) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 19) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 20) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 21) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 22) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 23) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 24) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 25) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 26) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 27) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 28) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 29) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 30) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 31) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 32) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 33) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 34) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 35) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 36) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 37) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 38) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 39) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 40) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 41) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 42) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 43) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 44) status from 3 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 45) status from 2 to 3\cf1\highlight2 
\par \cf0\highlight0 [FlashDB][sample][tsdb] set the TSL (time 46) status from 2 to 3\cf1\highlight2 
\par \cf0\highlight0 Restarting in 1000 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 999 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 998 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 997 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 996 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 995 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 994 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 993 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 992 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 991 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 990 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 989 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 988 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 987 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 986 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 985 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 984 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 983 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 982 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 981 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 980 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 979 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 978 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 977 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 976 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 975 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 974 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 973 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 972 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 971 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 970 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 969 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 968 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 967 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 966 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 965 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 964 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 963 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 962 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 961 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 960 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 959 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 958 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 957 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 956 seconds...\cf1\highlight2 
\par \cf0\highlight0 Restarting in 955 seconds...\cf1\highlight2 
\par \pard\cf0\highlight0\f1 
\par }
 