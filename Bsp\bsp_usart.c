/**
 * @file bsp_usart.c
 * <AUTHOR> (<EMAIL>)
 * @brief 串口驱动
 * @version 0.1
 * @date 2024-09-11
 *
 * @copyright Copyright (c) 2024
 *
 */
#include "bsp_usart.h"

#ifdef USE_USART

#ifdef USE_USART0
static unsigned char rx0buf[USART0_RXBUF_SIZE]; /*接收缓冲区 */
static unsigned char tx0buf[USART0_TXBUF_SIZE]; /*发送缓冲区 */
static ring_buf_t rb0send, rb0recv;             /*收发缓冲区管理*/
#endif

#ifdef USE_USART1
static unsigned char rx1buf[USART1_RXBUF_SIZE]; /*接收缓冲区 */
static unsigned char tx1buf[USART1_TXBUF_SIZE]; /*发送缓冲区 */
static ring_buf_t rb1send, rb1recv;             /*收发缓冲区管理*/
#endif

#ifdef USE_USART2
static unsigned char rx2buf[USART2_RXBUF_SIZE]; /*接收缓冲区 */
static unsigned char tx2buf[USART2_TXBUF_SIZE]; /*发送缓冲区 */
static ring_buf_t rb2send, rb2recv;             /*收发缓冲区管理*/
#endif

#ifdef USE_USART3
static unsigned char rx3buf[USART3_RXBUF_SIZE]; /*接收缓冲区 */
static unsigned char tx3buf[USART3_TXBUF_SIZE]; /*发送缓冲区 */
static ring_buf_t rb3send, rb3recv;             /*收发缓冲区管理*/
#endif

#ifdef USE_USART0
/**
 * @brief 串口0端口初始化
 */
static void usart0_PortInit(void)
{
    stc_gpio_init_t stcGpioInit = {0};

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpioa); /* GPIOA外设时钟使能 */

    /* USART0 PA09 Tx端口初始化 */
    DDL_ZERO_STRUCT(stcGpioInit);
    stcGpioInit.u32Mode     = GPIO_MODE_INPUT;
    stcGpioInit.u32Pin      = GPIO_PIN_09;
    stcGpioInit.u32PullUpDn = GPIO_PULL_UP;
    GPIOA_Init(&stcGpioInit);

    /* USART0 PA10 Rx端口初始化 */
    DDL_ZERO_STRUCT(stcGpioInit);
    stcGpioInit.u32Mode     = GPIO_MODE_INPUT;
    stcGpioInit.u32Pin      = GPIO_PIN_10;
    stcGpioInit.u32PullUpDn = GPIO_PULL_UP;
    GPIOA_Init(&stcGpioInit);

    GPIO_PA09_AF_USART0_TXD_SET(); /* USART0 PA09 Tx 复用功能设置 */
    GPIO_PA10_AF_USART0_RXD_SET(); /* USART0 PA10 Rx 复用功能设置 */
}

/**
 * @brief 串口0参数配置
 */
static void usart0_Config(uint32_t baudrate)
{
    stc_usart_uart_init_t stcUARTInit = {0};
    uint32_t u32CalBaudRate           = 0U;

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralUsart0); /* USART0外设时钟使能 */

    /* 结构体初始化 */
    stcUARTInit.u32BaudRate    = baudrate;              /* 设置波特率 */
    stcUARTInit.u32SampleMode  = USART_OVERSAMPLING_16; /* 采样模式设定16倍/8倍采样: 16位 */
    stcUARTInit.u32FrameLength = USART_FRAME_LEN_8BITS; /* 数据帧长度8/9bits：8bits */
    stcUARTInit.u32Parity      = USART_PARITY_NONE;     /* 奇偶校验选择：无校验 */
    stcUARTInit.u32StopBits    = USART_STOP_1BIT;       /* 停止位长度:1位 */
    stcUARTInit.u32TransMode   = USART_MODE_TX_RX;      /* 数据传输方式:Tx Rx */

    u32CalBaudRate = USART_UART_Init(HC_USART0, &stcUARTInit);

    if (0U == u32CalBaudRate) {
        // 初始化失败，可以在这里添加错误处理
        while(1);
    }

    USART_ClearIrq(HC_USART1, USART_FLAG_ALL);
    /* Tx中断设置 */
    EnableNvic(USART1_TX_IRQn, IrqLevel4, TRUE);
    USART_EnableIrq(HC_USART1, USART_FLAG_TXE);

    /* Rx中断设置 */
    EnableNvic(USART1_RX_IRQn, IrqLevel4, TRUE);
    USART_EnableIrq(HC_USART1, USART_FLAG_RC);

    /* 接收奇偶校验错误、帧错误、上溢错误中断设置 */
    USART_EnableIrq(HC_USART1, USART_FLAG_PE);
    USART_EnableIrq(HC_USART1, USART_FLAG_FE);
    USART_EnableIrq(HC_USART1, USART_FLAG_OR);
    EnableNvic(USART1_FLAG_IRQn, IrqLevel4, TRUE);
}

/**
 * @brief UART0 Tx(TXE或TC)中断
 *
 */
void Usart0_Tx_IRQHandler(void)
{
    unsigned char data;

    USART_ClearIrq(HC_USART0, USART_FLAG_TC);

    if (ring_buf_get(&rb1send, &data, 1)) {
        /* 发送数据 */
        USART_UART_TransmitINT(HC_USART0, &data);
        USART_EnableIrq(HC_USART0, USART_FLAG_TC);
    } else {
        /* 最后一字节数据发送完成，关闭TXE中断，关闭TC中断 */
        USART_DisableIrq(HC_USART0, USART_FLAG_TC);
        USART_DisableIrq(HC_USART0, USART_FLAG_TXE);
    }
}

/**
 * @brief UART1 Rx(RC)中断
 *
 */
void Usart0_Rx_IRQHandler(void)
{
    unsigned char data;

    /* 接收数据 */
    USART_UART_ReceiveINT(HC_USART0, &data);
    if (ring_buf_free_space(&rb1recv) > 0) {
        ring_buf_put(&rb1recv, &data, 1); /*将数据放入接收缓冲区*/
    }
    USART_ClearIrq(HC_USART0, USART_FLAG_RC);
}

/**
 * @brief UART1 Flag(对应IER[14:3])中断
 *
 */
void Usart0_Flag_IRQHandler(void)
{
    if (0U != USART_GetFlag(HC_USART0, USART_FLAG_PE | USART_FLAG_FE | USART_FLAG_OR)) {
        USART_ClearFlag(HC_USART0, USART_FLAG_PE | USART_FLAG_FE | USART_FLAG_OR);
        /* 有奇偶校验错误、帧错误、上溢错误其中一种或多种错误 */
    }
}

/**
 * @brief 串口0初始化
 */
void bsp_Usart0Init(unsigned int baud)
{
    /*初始化环形缓冲区 */
    ring_buf_init(&rb0send, tx0buf, sizeof(tx0buf));
    ring_buf_init(&rb0recv, rx0buf, sizeof(rx0buf));

    usart0_PortInit();
    usart0_Config(baud);
}
#endif

#ifdef USE_USART1
/**
 * @brief 串口1端口初始化
 *
 */
static void usart1_PortInit(void)
{
    stc_gpio_init_t stcGpioInit = {0};

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpioa); /* GPIOA外设时钟使能 */

    /* USART1 PA13 Rx端口初始化 */
    DDL_ZERO_STRUCT(stcGpioInit);
    stcGpioInit.u32Mode     = GPIO_MODE_INPUT;
    stcGpioInit.u32Pin      = GPIO_PIN_03;
    stcGpioInit.u32PullUpDn = GPIO_PULL_UP;
    GPIOA_Init(&stcGpioInit);

    /* USART1 PA14 Tx端口初始化 */
    DDL_ZERO_STRUCT(stcGpioInit);
    stcGpioInit.u32Mode     = GPIO_MODE_INPUT;
    stcGpioInit.u32Pin      = GPIO_PIN_02;
    stcGpioInit.u32PullUpDn = GPIO_PULL_UP;
    GPIOA_Init(&stcGpioInit);

    GPIO_PA02_AF_USART1_TXD_SET(); /* USART1 PA02 Tx 复用功能设置 */
    GPIO_PA03_AF_USART1_RXD_SET(); /* USART1 PA03 Rx 复用功能设置 */
}

/**
 * @brief 串口1参数配置
 *
 * @param baudrate
 */
static void usart1_Config(uint32_t baudrate)
{
    stc_usart_uart_init_t stcUARTInit = {0};
    uint32_t u32CalBaudRate           = 0U;

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralUsart1); /* USART1外设时钟使能 */

    /* 结构体初始化 */
    stcUARTInit.u32BaudRate    = baudrate;              /* 设置波特率 */
    stcUARTInit.u32SampleMode  = USART_OVERSAMPLING_16; /* 采样模式设定16倍/8倍采样: 16位 */
    stcUARTInit.u32FrameLength = USART_FRAME_LEN_8BITS; /* 数据帧长度8/9bits：8bits */
    stcUARTInit.u32Parity      = USART_PARITY_NONE;     /* 奇偶校验选择：无校验 */
    stcUARTInit.u32StopBits    = USART_STOP_1BIT;       /* 停止位长度:1位 */
    stcUARTInit.u32TransMode   = USART_MODE_TX_RX;      /* 数据传输方式:Tx Rx */

    u32CalBaudRate = USART_UART_Init(HC_USART1, &stcUARTInit);

    if (0U != u32CalBaudRate) {
        /* 有效设置，可通过查看u32CalBaudRate的值确认当前计算的波特率 */
    }

    USART_ClearIrq(HC_USART1, USART_FLAG_ALL);
    /* Tx中断设置 */
    EnableNvic(USART1_TX_IRQn, IrqLevel4, TRUE);
    USART_EnableIrq(HC_USART1, USART_FLAG_TXE);

    /* Rx中断设置 */
    EnableNvic(USART1_RX_IRQn, IrqLevel4, TRUE);
    USART_EnableIrq(HC_USART1, USART_FLAG_RC);

    /* 接收奇偶校验错误、帧错误、上溢错误中断设置 */
    USART_EnableIrq(HC_USART1, USART_FLAG_PE);
    USART_EnableIrq(HC_USART1, USART_FLAG_FE);
    USART_EnableIrq(HC_USART1, USART_FLAG_OR);
    EnableNvic(USART1_FLAG_IRQn, IrqLevel4, TRUE);
}

/**
 * @brief UART0 Tx(TXE或TC)中断
 *
 */
void Usart1_Tx_IRQHandler(void)
{
    unsigned char data;

    USART_ClearIrq(HC_USART1, USART_FLAG_TC);

    if (ring_buf_get(&rb1send, &data, 1)) {
        /* 发送数据 */
        USART_UART_TransmitINT(HC_USART1, &data);
        USART_EnableIrq(HC_USART1, USART_FLAG_TC);
    } else {
        /* 最后一字节数据发送完成，关闭TXE中断，关闭TC中断 */
        USART_DisableIrq(HC_USART1, USART_FLAG_TC);
        USART_DisableIrq(HC_USART1, USART_FLAG_TXE);
    }
}

/**
 * @brief UART1 Rx(RC)中断
 *
 */
void Usart1_Rx_IRQHandler(void)
{
    unsigned char data;

    /* 接收数据 */
    USART_UART_ReceiveINT(HC_USART1, &data);
    if (ring_buf_free_space(&rb1recv) > 0) {
        ring_buf_put(&rb1recv, &data, 1); /*将数据放入接收缓冲区*/
    }
    USART_ClearIrq(HC_USART1, USART_FLAG_RC);
}

/**
 * @brief UART1 Flag(对应IER[14:3])中断
 *
 */
void Usart1_Flag_IRQHandler(void)
{
    if (0U != USART_GetFlag(HC_USART1, USART_FLAG_PE | USART_FLAG_FE | USART_FLAG_OR)) {
        USART_ClearFlag(HC_USART1, USART_FLAG_PE | USART_FLAG_FE | USART_FLAG_OR);
        /* 有奇偶校验错误、帧错误、上溢错误其中一种或多种错误 */
    }
}

/**
 * @brief 串口1初始化
 *
 */
void bsp_Usart1Init(unsigned int baud)
{
    /*初始化环形缓冲区 */
    ring_buf_init(&rb1send, tx1buf, sizeof(tx1buf));
    ring_buf_init(&rb1recv, rx1buf, sizeof(rx1buf));

    usart1_PortInit();
    usart1_Config(baud);
}
#endif

#ifdef USE_USART2
/**
 * @brief 串口2端口初始化
 *
 */
static void usart2_PortInit(void)
{
    stc_gpio_init_t stcGpioInit = {0};

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpiob); /* GPIOB外设时钟使能 */

    /* USART2 PB02 Rx端口初始化 */
    DDL_ZERO_STRUCT(stcGpioInit);
    stcGpioInit.u32Mode     = GPIO_MODE_INPUT;
    stcGpioInit.u32Pin      = GPIO_PIN_02;
    stcGpioInit.u32PullUpDn = GPIO_PULL_UP;
    GPIOB_Init(&stcGpioInit);

    /* USART2 PB10 Tx端口初始化 */
    DDL_ZERO_STRUCT(stcGpioInit);
    stcGpioInit.u32Mode     = GPIO_MODE_INPUT;
    stcGpioInit.u32Pin      = GPIO_PIN_10;
    stcGpioInit.u32PullUpDn = GPIO_PULL_UP;
    GPIOB_Init(&stcGpioInit);

    GPIO_PB02_AF_USART2_RXD_SET(); /* USART2 PB02 Rx 复用功能设置 */
    GPIO_PB10_AF_USART2_TXD_SET(); /* USART2 PB10 Tx 复用功能设置 */
}

/**
 * @brief 串口2参数配置
 *
 * @param baudrate
 */
static void usart2_Config(uint32_t baudrate)
{
    stc_usart_uart_init_t stcUARTInit = {0};
    uint32_t u32CalBaudRate           = 0U;

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralUsart2); /* USART2外设时钟使能 */

    /* 结构体初始化 */
    stcUARTInit.u32BaudRate    = baudrate;              /* 设置波特率 */
    stcUARTInit.u32SampleMode  = USART_OVERSAMPLING_16; /* 采样模式设定16倍/8倍采样: 16位 */
    stcUARTInit.u32FrameLength = USART_FRAME_LEN_8BITS; /* 数据帧长度8/9bits：8bits */
    stcUARTInit.u32Parity      = USART_PARITY_NONE;     /* 奇偶校验选择：无校验 */
    stcUARTInit.u32StopBits    = USART_STOP_1BIT;       /* 停止位长度:1位 */
    stcUARTInit.u32TransMode   = USART_MODE_TX_RX;      /* 数据传输方式:Tx Rx */

    u32CalBaudRate = USART_UART_Init(HC_USART2, &stcUARTInit);

    if (0U != u32CalBaudRate) {
        /* 有效设置，可通过查看u32CalBaudRate的值确认当前计算的波特率 */
    }

    USART_ClearIrq(HC_USART2, USART_FLAG_ALL);
    /* Tx中断设置 */
    EnableNvic(USART2_TX_IRQn, IrqLevel4, TRUE);
    USART_EnableIrq(HC_USART2, USART_FLAG_TXE);

    /* Rx中断设置 */
    EnableNvic(USART2_RX_IRQn, IrqLevel4, TRUE);
    USART_EnableIrq(HC_USART2, USART_FLAG_RC);

    /* 接收奇偶校验错误、帧错误、上溢错误中断设置 */
    USART_EnableIrq(HC_USART2, USART_FLAG_PE);
    USART_EnableIrq(HC_USART2, USART_FLAG_FE);
    USART_EnableIrq(HC_USART2, USART_FLAG_OR);
    EnableNvic(USART2_FLAG_IRQn, IrqLevel4, TRUE);
}

/**
 * @brief UART2 Tx(TXE或TC)中断
 *
 */
void Usart2_Tx_IRQHandler(void)
{
    unsigned char data;

    USART_ClearIrq(HC_USART2, USART_FLAG_TC);

    if (ring_buf_get(&rb2send, &data, 1)) {
        /* 发送数据 */
        USART_UART_TransmitINT(HC_USART2, &data);
        USART_EnableIrq(HC_USART2, USART_FLAG_TC);
    } else {
        /* 最后一字节数据发送完成，关闭TXE中断，关闭TC中断 */
        USART_DisableIrq(HC_USART2, USART_FLAG_TC);
        USART_DisableIrq(HC_USART2, USART_FLAG_TXE);
    }
}

/**
 * @brief UART2 Rx(RC)中断
 *
 */
void Usart2_Rx_IRQHandler(void)
{
    unsigned char data;

    /* 接收数据 */
    USART_UART_ReceiveINT(HC_USART2, &data);
    if (ring_buf_free_space(&rb2recv) > 0) {
        ring_buf_put(&rb2recv, &data, 1); /*将数据放入接收缓冲区*/
    }
    USART_ClearIrq(HC_USART2, USART_FLAG_RC);
}

/**
 * @brief UART2 Flag(对应IER[14:3])中断
 *
 */
void Usart2_Flag_IRQHandler(void)
{
    if (0U != USART_GetFlag(HC_USART2, USART_FLAG_PE | USART_FLAG_FE | USART_FLAG_OR)) {
        USART_ClearFlag(HC_USART2, USART_FLAG_PE | USART_FLAG_FE | USART_FLAG_OR);
        /* 有奇偶校验错误、帧错误、上溢错误其中一种或多种错误 */
    }
}

/**
 * @brief 串口2初始化
 *
 */
void bsp_Usart2Init(unsigned int baud)
{
    /*初始化环形缓冲区 */
    ring_buf_init(&rb2send, tx2buf, sizeof(tx2buf));
    ring_buf_init(&rb2recv, rx2buf, sizeof(rx2buf));

    usart2_PortInit();
    usart2_Config(baud);
}
#endif

#ifdef USE_USART3
/**
 * @brief 串口3端口初始化
 *
 */
static void usart3_PortInit(void)
{
    stc_gpio_init_t stcGpioInit = {0};

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpioa); /* GPIOA外设时钟使能 */

    /* USART3 PA04 Rx端口初始化 */
    DDL_ZERO_STRUCT(stcGpioInit);
    stcGpioInit.u32Mode     = GPIO_MODE_INPUT;
    stcGpioInit.u32Pin      = GPIO_PIN_04;
    stcGpioInit.u32PullUpDn = GPIO_PULL_UP;
    GPIOA_Init(&stcGpioInit);

    /* USART3 PA07 Tx端口初始化 */
    DDL_ZERO_STRUCT(stcGpioInit);
    stcGpioInit.u32Mode     = GPIO_MODE_INPUT;
    stcGpioInit.u32Pin      = GPIO_PIN_07;
    stcGpioInit.u32PullUpDn = GPIO_PULL_UP;
    GPIOA_Init(&stcGpioInit);

    GPIO_PA04_AF_USART3_RXD_SET(); /* USART3 PA04 Rx 复用功能设置 */
    GPIO_PA07_AF_USART3_TXD_SET(); /* USART3 PA07 Tx 复用功能设置 */
}

/**
 * @brief 串口3参数配置
 *
 * @param baudrate
 */
static void usart3_Config(uint32_t baudrate)
{
    stc_usart_uart_init_t stcUARTInit = {0};
    uint32_t u32CalBaudRate           = 0U;

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralUsart3); /* USART3外设时钟使能 */

    /* 结构体初始化 */
    stcUARTInit.u32BaudRate    = baudrate;              /* 设置波特率 */
    stcUARTInit.u32SampleMode  = USART_OVERSAMPLING_16; /* 采样模式设定16倍/8倍采样: 16位 */
    stcUARTInit.u32FrameLength = USART_FRAME_LEN_8BITS; /* 数据帧长度8/9bits：8bits */
    stcUARTInit.u32Parity      = USART_PARITY_NONE;     /* 奇偶校验选择：无校验 */
    stcUARTInit.u32StopBits    = USART_STOP_1BIT;       /* 停止位长度:1位 */
    stcUARTInit.u32TransMode   = USART_MODE_TX_RX;      /* 数据传输方式:Tx Rx */

    u32CalBaudRate = USART_UART_Init(HC_USART3, &stcUARTInit);

    if (0U != u32CalBaudRate) {
        /* 有效设置，可通过查看u32CalBaudRate的值确认当前计算的波特率 */
    }

    USART_ClearIrq(HC_USART3, USART_FLAG_ALL);
    /* Tx中断设置 */
    EnableNvic(USART3_TX_IRQn, IrqLevel4, TRUE);
    USART_EnableIrq(HC_USART3, USART_FLAG_TXE);

    /* Rx中断设置 */
    EnableNvic(USART3_RX_IRQn, IrqLevel4, TRUE);
    USART_EnableIrq(HC_USART3, USART_FLAG_RC);

    /* 接收奇偶校验错误、帧错误、上溢错误中断设置 */
    USART_EnableIrq(HC_USART3, USART_FLAG_PE);
    USART_EnableIrq(HC_USART3, USART_FLAG_FE);
    USART_EnableIrq(HC_USART3, USART_FLAG_OR);
    EnableNvic(USART3_FLAG_IRQn, IrqLevel4, TRUE);
}

/**
 * @brief UART3 Tx(TXE或TC)中断
 *
 */
void Usart3_Tx_IRQHandler(void)
{
    unsigned char data;

    USART_ClearIrq(HC_USART3, USART_FLAG_TC);

    if (ring_buf_get(&rb3send, &data, 1)) {
        /* 发送数据 */
        USART_UART_TransmitINT(HC_USART3, &data);
        USART_EnableIrq(HC_USART3, USART_FLAG_TC);
    } else {
        /* 最后一字节数据发送完成，关闭TXE中断，关闭TC中断 */
        USART_DisableIrq(HC_USART3, USART_FLAG_TC);
        USART_DisableIrq(HC_USART3, USART_FLAG_TXE);
    }
}

/**
 * @brief UART3 Rx(RC)中断
 *
 */
void Usart3_Rx_IRQHandler(void)
{
    unsigned char data;

    /* 接收数据 */
    USART_UART_ReceiveINT(HC_USART3, &data);
    if (ring_buf_free_space(&rb3recv) > 0) {
        ring_buf_put(&rb3recv, &data, 1); /*将数据放入接收缓冲区*/
    }
    USART_ClearIrq(HC_USART3, USART_FLAG_RC);
}

/**
 * @brief UART3 Flag(对应IER[14:3])中断
 *
 */
void Usart3_Flag_IRQHandler(void)
{
    if (0U != USART_GetFlag(HC_USART3, USART_FLAG_PE | USART_FLAG_FE | USART_FLAG_OR)) {
        USART_ClearFlag(HC_USART3, USART_FLAG_PE | USART_FLAG_FE | USART_FLAG_OR);
        /* 有奇偶校验错误、帧错误、上溢错误其中一种或多种错误 */
    }
}

/**
 * @brief 串口3初始化
 *
 */
void bsp_Usart3Init(unsigned int baud)
{
    /*初始化环形缓冲区 */
    ring_buf_init(&rb3send, tx3buf, sizeof(tx3buf));
    ring_buf_init(&rb3recv, rx3buf, sizeof(rx3buf));

    usart3_PortInit();
    usart3_Config(baud);
}
#endif

/**
 * @brief 将数据推进队列进行发送
 *
 * @param usart 串口号
 * @param buf 待发送数据推入缓冲区
 * @param size 待发送数据大小
 * @return unsigned int 实际发送的数据大小
 */
unsigned int usart_send_buf(unsigned char usart, unsigned char *buf, unsigned int size)
{
    unsigned int ret  = 0;
    ring_buf_t *tx_rb = NULL;

    /* 参数检查 */
    if (buf == NULL || size == 0) {
        return 0;
    }

    /* 获取发送缓冲区 */
    tx_rb = usart_get_txrb(usart);
    if (tx_rb == NULL) {
        return 0;
    }

    /* 检查缓冲区空间 */
    if (ring_buf_free_space(tx_rb) < size) {
        return 0;
    }

    /* 将数据放入发送缓冲区 */
    ret = ring_buf_put(tx_rb, buf, size);

    /* 使能发送中断 */
    switch (usart) {
#ifdef USE_USART0
        case USART0:
            USART_EnableIrq(HC_USART0, USART_FLAG_TXE);
            break;
#endif
#ifdef USE_USART1
        case USART1:
            USART_EnableIrq(HC_USART1, USART_FLAG_TXE);
            break;
#endif
#ifdef USE_USART2
        case USART2:
            USART_EnableIrq(HC_USART2, USART_FLAG_TXE);
            break;
#endif
#ifdef USE_USART3
        case USART3:
            USART_EnableIrq(HC_USART3, USART_FLAG_TXE);
            break;
#endif
        default:
            break;
    }

    return ret;
}

/**
 * @brief 从队列中读取接收到的串口数据
 *
 * @param usart 串口号
 * @param buf 数据接收缓冲区
 * @param size 需要读取的数据个数
 * @return unsigned int 实际读取的数据大小
 */
unsigned int usart_recv_buf(unsigned char usart, unsigned char *buf, unsigned int size)
{
    switch (usart) {
#ifdef USE_USART0
        case USART0:
            return ring_buf_get(&rb0recv, (unsigned char *)buf, size);
            break;
#endif

#ifdef USE_USART1
        case USART1:
            return ring_buf_get(&rb1recv, (unsigned char *)buf, size);
            break;
#endif

#ifdef USE_USART2
        case USART2:
            return ring_buf_get(&rb2recv, (unsigned char *)buf, size);
            break;
#endif

#ifdef USE_USART3
        case USART3:
            return ring_buf_get(&rb3recv, (unsigned char *)buf, size);
            break;
#endif

        default:
            break;
    }

    return 0;
}

/**
 * @brief 获取串口接收缓冲区
 *
 * @param usart 串口号
 * @return ring_buf_t* 接收缓冲区指针，失败返回NULL
 */
ring_buf_t *usart_get_rxrb(unsigned char usart)
{
    ring_buf_t *rb_buf = NULL;

    /* 参数检查 */
    if (usart >= USART_MAX_NUM) {
        return NULL;
    }

    /* 获取接收缓冲区 */
    switch (usart) {
#ifdef USE_USART0
        case USART0:
            rb_buf = &rb0recv;
            break;
#endif
#ifdef USE_USART1
        case USART1:
            rb_buf = &rb1recv;
            break;
#endif
#ifdef USE_USART2
        case USART2:
            rb_buf = &rb2recv;
            break;
#endif
#ifdef USE_USART3
        case USART3:
            rb_buf = &rb3recv;
            break;
#endif
        default:
            rb_buf = NULL;
            break;
    }

    return rb_buf;
}

/**
 * @brief 获取串口发送缓冲区
 *
 * @param usart 串口号
 * @return ring_buf_t* 发送缓冲区指针，失败返回NULL
 */
ring_buf_t *usart_get_txrb(unsigned char usart)
{
    ring_buf_t *rb_buf = NULL;

    /* 参数检查 */
    if (usart >= USART_MAX_NUM) {
        return NULL;
    }

    /* 获取发送缓冲区 */
    switch (usart) {
#ifdef USE_USART0
        case USART0:
            rb_buf = &rb0send;
            break;
#endif
#ifdef USE_USART1
        case USART1:
            rb_buf = &rb1send;
            break;
#endif
#ifdef USE_USART2
        case USART2:
            rb_buf = &rb2send;
            break;
#endif
#ifdef USE_USART3
        case USART3:
            rb_buf = &rb3send;
            break;
#endif
        default:
            rb_buf = NULL;
            break;
    }

    return rb_buf;
}

#ifdef USE_PRINTF
int fputc(int ch, FILE *f)
{
    unsigned char c = (unsigned char)ch;
    // 使用带超时的发送函数，超时时间设为1000
    // if (Ok != USART_UART_Transmit(HC_USART2, &c, 1, 1000)) {
    //     // 发送失败处理
    //     return -1;
    // }

    return usart_send_buf(USART2, (unsigned char *)&ch, 1);
    // return ch;
}

int fgetc(FILE *f)
{
    unsigned char data;
    // // 使用带超时的接收函数，超时时间设为1000
    // if (USART_UART_NoError != USART_UART_Receive(HC_USART2, &data, 1, 1000)) {
    //     // 接收失败处理
    //     return -1;
    // }

    usart_recv_buf(USART2, &data, 1);
    return data;
}
#endif

#endif