#ifndef COMPONENTS_LOG_H
#define COMPONENTS_LOG_H

#include <stdint.h>
#include <stdarg.h>

/* 日志总开关 */
#define LOG_GLOBAL_ENABLE 1 /* 设置为0可以完全禁用日志 */

// 选择是否全局使用异步日志
#define USE_ASYNC_LOG     0

/* 日志输出函数类型定义 */
typedef void (*log_output_fn)(const char *buffer, uint32_t size);

#if LOG_GLOBAL_ENABLE
#define LOG_INIT(level)           log_init(level)
#define LOG_SET_LEVEL(level)      log_set_level(level)
#define LOG_SET_OUTPUT(func)      log_set_output(func)
#define LOG_ERROR(tag, ...)       log_error(tag, __VA_ARGS__)
#define LOG_WARN(tag, ...)        log_warn(tag, __VA_ARGS__)
#define LOG_INFO(tag, ...)        log_info(tag, __VA_ARGS__)
#define LOG_DEBUG(tag, ...)       log_debug(tag, __VA_ARGS__)
#if USE_ASYNC_LOG
#define LOG_ASYNC_ERROR(tag, ...) log_async(tag, LOG_LEVEL_ERROR, __VA_ARGS__)
#define LOG_ASYNC_WARN(tag, ...)  log_async(tag, LOG_LEVEL_WARN, __VA_ARGS__)
#define LOG_ASYNC_INFO(tag, ...)  log_async(tag, LOG_LEVEL_INFO, __VA_ARGS__)
#define LOG_ASYNC_DEBUG(tag, ...) log_async(tag, LOG_LEVEL_DEBUG, __VA_ARGS__)
#endif
#else
#define LOG_INIT(level)      ((void)0)
#define LOG_SET_LEVEL(level) ((void)0)
#define LOG_SET_OUTPUT(func) ((void)0)
#define LOG_ERROR(tag, ...)  ((void)0)
#define LOG_WARN(tag, ...)   ((void)0)
#define LOG_INFO(tag, ...)   ((void)0)
#define LOG_DEBUG(tag, ...)  ((void)0)
#endif

/* 日志级别定义 */
typedef enum {
    LOG_LEVEL_NONE = 0,
    LOG_LEVEL_ERROR,
    LOG_LEVEL_WARN,
    LOG_LEVEL_INFO,
    LOG_LEVEL_DEBUG
} log_level_t;

/* 日志颜色定义 */
#define LOG_COLOR_RED    "\033[31m"
#define LOG_COLOR_GREEN  "\033[32m"
#define LOG_COLOR_YELLOW "\033[33m"
#define LOG_COLOR_BLUE   "\033[34m"
#define LOG_COLOR_RESET  "\033[0m"

#if LOG_GLOBAL_ENABLE
/* 日志模块初始化 */
void log_init(log_level_t level);

/* 设置日志级别 */
void log_set_level(log_level_t level);

/* 设置日志输出函数 */
void log_set_output(log_output_fn output_func);

/* 日志输出接口 */
void log_error(const char *tag, const char *format, ...);
void log_warn(const char *tag, const char *format, ...);
void log_info(const char *tag, const char *format, ...);
void log_debug(const char *tag, const char *format, ...);

#if USE_ASYNC_LOG
/* 异步日志接口 */
void log_async(const char *tag, log_level_t level, const char *format, ...);
void log_async_task_init(void);
#endif
#endif

#endif /* COMPONENTS_LOG_H */