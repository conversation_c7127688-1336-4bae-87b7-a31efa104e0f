#ifndef MOTOR_TASK_H
#define MOTOR_TASK_H

#include "app_config.h"
#include "app_log.h"
#include "bsp.h"

/**
 * @file motor_task.h
 * @brief 推杆电机任务模块头文件
 * <AUTHOR>
 * @version 1.0
 * @date 2025-05-06
 *
 * 功能说明：
 * 1. 实现推杆电机上升、下降、停止等基本控制
 * 2. 支持电流检测限位，上下限位点自动识别
 * 3. 支持过流/堵转/超时保护，异常自动停止
 * 4. 支持角度控制，角度与行程时间线性换算，行程时间自学习
 * 5. 状态-命令-事件三维表驱动状态机
 */

// =========================
// 参数宏定义
// =========================
#define MOTOR_TASK_NUM                               4
#define MOTOR_TASK_TIMER_PERIOD_MS                   5   // 处理周期5ms
#define MOTOR_LIMIT_CHECK_WINDOW_MS                  1000 // 限位检测窗口，单位ms
#define NO_COMMUTATION_LIMIT_MS                      500  // 换相数长时间未变，判定为限位，单位ms

#define MOTOR_TRAVEL_SAVE_THRESHOLD                  5 // 换相数误差阈值，可根据实际调整

/** 限位检测电流阈值(mA) */
#define MOTOR_LIMIT_CURRENT_THRESHOLD                5 // mA
/** 默认总行程 */
#define MOTOR_BACK1_DEFAULT_TRAVEL_UP_COMMUTATIONS   4849 // 背部电机1上行全行程换相数
#define MOTOR_BACK2_DEFAULT_TRAVEL_UP_COMMUTATIONS   4849 // 背部电机2上行全行程换相数
#define MOTOR_LEG_DEFAULT_TRAVEL_UP_COMMUTATIONS     4182 // 腿部电机上行全行程换相数
#define MOTOR_WAIST_DEFAULT_TRAVEL_UP_COMMUTATIONS   2000 // 腰部电机上行全行程换相数
#define MOTOR_BACK1_DEFAULT_TRAVEL_DOWN_COMMUTATIONS 4726 // 背部电机1下行全行程换相数
#define MOTOR_BACK2_DEFAULT_TRAVEL_DOWN_COMMUTATIONS 4726 // 背部电机2下行全行程换相数
#define MOTOR_LEG_DEFAULT_TRAVEL_DOWN_COMMUTATIONS   4167 // 腿部电机下行全行程换相数
#define MOTOR_WAIST_DEFAULT_TRAVEL_DOWN_COMMUTATIONS 2000 // 腰部电机下行全行程换相数

/** 各电机最大角度宏定义 */
#define MOTOR_BACK1_MAX_ANGLE                        60 // 背部电机1最大角度
#define MOTOR_BACK2_MAX_ANGLE                        60 // 背部电机2最大角度
#define MOTOR_WAIST_MAX_ANGLE                        15 // 腰部电机最大角度
#define MOTOR_LEG_MAX_ANGLE                          45 // 腿部电机最大角度
/** 电机ID与功能映射宏定义 */
#define MOTOR_ID_BACK1                               0 // 背部电机1
#define MOTOR_ID_BACK2                               1 // 背部电机2
#define MOTOR_ID_LEG                                 2 // 腿部电机
#define MOTOR_ID_WAIST                               3 // 腰部电机

// =========================
// 类型定义
// =========================

/** 电机运动方向 */
typedef enum {
    MOTOR_TASK_DIR_STOP = 0,
    MOTOR_TASK_DIR_UP,
    MOTOR_TASK_DIR_DOWN
} motor_task_dir_t;

/** 电机错误类型 */
typedef enum {
    MOTOR_TASK_ERR_NONE = 0,
    MOTOR_TASK_ERR_OVERCURRENT,
    MOTOR_TASK_ERR_STALL,
    MOTOR_TASK_ERR_TIMEOUT,
    MOTOR_TASK_ERR_MAX
} motor_task_err_t;

/** 电机命令类型 */
typedef enum {
    MOTOR_TASK_CMD_UP = 0,
    MOTOR_TASK_CMD_DOWN,
    MOTOR_TASK_CMD_STOP,
    MOTOR_TASK_CMD_MOVE_TO_ANGLE,
    MOTOR_TASK_CMD_MAX // 用于计数，必须放最后
} motor_task_cmd_type_t;

/** 电机事件类型 */
typedef enum {
    EVT_NONE = 0,
    EVT_LIMIT,
    EVT_OVERCURRENT,
    EVT_STALL,
    EVT_TIMEOUT,
    EVT_MAX
} motor_event_t;

/** 电机状态类型 */
typedef enum {
    MOTOR_STATE_IDLE = 0,
    MOTOR_STATE_HOMING,
    MOTOR_STATE_MOVING,
    MOTOR_STATE_STOPPED,
    MOTOR_STATE_ERROR,
    MOTOR_STATE_MANUAL,
    MOTOR_STATE_MAX // 用于计数，必须放最后
} motor_state_t;

/** 电机命令结构体 */
typedef struct {
    motor_task_cmd_type_t cmd;
    uint8_t motor_id;
    union {
        uint32_t position; // for set pos
        float angle;       // for set angle
    } data;
} motor_task_cmd_t;

/** 电机参数结构体 */
typedef struct {
    float angle;                       // 当前角度
    float target_angle;                // 目标角度
    uint16_t up_travel_commutations;   // 上行全行程换相数
    uint16_t down_travel_commutations; // 下行全行程换相数
    uint16_t commutation_count;        // 换相数
    uint8_t position;                  // 当前位置
    uint8_t id;                        // 电机ID
    uint8_t speed;                     // 速度
    uint8_t max_angle;                 // 最大角度
    motor_task_dir_t dir;              // 当前方向
    motor_task_dir_t last_dir;         // 上次方向
    motor_task_dir_t limit_dir;        // 当前限位方向
    motor_task_err_t error;            // 错误类型
    motor_state_t state;               // 当前状态
    motor_task_cmd_type_t last_cmd;    // 上次命令
    uint8_t is_at_limit : 1;           // 是否到达限位
    uint8_t is_homing : 1;             // 是否复位
    uint8_t is_moving_to_angle : 1;    // 是否正在移动到角度
    uint8_t is_timeout : 1;            // 是否超时
    uint8_t is_calibrated : 1;         // 是否校准
    uint8_t force_limit : 1;           // 是否强制到限位
    uint8_t reserved : 2;              // 保留位
    // 分段累计机制
    uint16_t segment_commutations;  // 当前分段累计换相数
    uint16_t total_commutations;    // 本次完整行程累计换相数
    motor_task_dir_t last_move_dir; // 上一次运动方向
} motor_task_param_t;

// =========================
// 状态-命令-事件三维表结构与分发
// =========================

typedef void (*motor_state_action_t)(uint8_t id, const motor_task_cmd_t *cmd);

typedef struct {
    motor_state_action_t action;
    motor_state_t next_state;
} motor_state_table_entry_t;

// =========================
// API声明
// =========================

/**
 * @brief 初始化电机任务模块
 */
void motor_task_init(void);

/**
 * @brief 发送电机控制命令（上升、下降、停止、角度、参数等）
 * @param cmd 命令结构体指针
 */
void motor_task_send_cmd(const motor_task_cmd_t *cmd);

/**
 * @brief 设置指定电机速度（仅对当前运动有效）
 * @param motor_id 电机ID
 * @param speed 速度百分比
 */
void motor_task_set_speed(uint8_t motor_id, uint8_t speed);

/**
 * @brief 获取指定电机当前运动方向
 * @param motor_id 电机ID
 * @return 当前方向
 */
motor_task_dir_t motor_task_get_dir(uint8_t motor_id);

/**
 * @brief 获取指定电机当前位置（0~100）
 * @param motor_id 电机ID
 * @return 当前位置百分比
 */
uint32_t motor_task_get_position(uint8_t motor_id);

/**
 * @brief 获取指定电机当前角度
 * @param motor_id 电机ID
 * @return 当前角度
 */
float motor_task_get_angle(uint8_t motor_id);

/**
 * @brief 获取指定电机当前错误状态
 * @param motor_id 电机ID
 * @return 错误类型
 */
motor_task_err_t motor_task_get_error(uint8_t motor_id);

/**
 * @brief 清除指定电机错误状态
 * @param motor_id 电机ID
 */
void motor_task_clear_error(uint8_t motor_id);

#endif // MOTOR_TASK_H