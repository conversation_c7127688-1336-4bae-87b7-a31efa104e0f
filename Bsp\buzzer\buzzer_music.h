/**
 * @file buzzer_music.h
 * @brief 蜂鸣器音乐数据定义
 */
#ifndef __BUZZER_MUSIC_H__
#define __BUZZER_MUSIC_H__

#include "bsp_buzzer.h"

// 音乐描述结构体
typedef struct {
    const char *name;           // 音乐名称
    const char *description;    // 音乐描述
    const buzzer_note_t *notes; // 音符序列
    uint8_t note_count;         // 音符数量
} buzzer_music_t;

// 预定义音乐类型
typedef enum {
    BUZZER_MUSIC_POWER_ON,        // 开机音效
    BUZZER_MUSIC_ALERT,           // 提示音
    BUZZER_MUSIC_SUCCESS,         // 成功音
    BUZZER_MUSIC_ERROR,           // 错误音
    BUZZER_MUSIC_KEY_CLICK,       // 按键点击音
    BUZZER_MUSIC_KEY_LONG,        // 按键长按音
    BUZZER_MUSIC_KEY_RELEASE,     // 按键释放音
    BUZZER_MUSIC_DOORBELL,        // 门铃音效
    BUZZER_MUSIC_NOTIFY,          // 消息提醒
    BUZZER_MUSIC_STARTUP,         // 设备启动
    BUZZER_MUSIC_SHUTDOWN,        // 设备关闭
    BUZZER_MUSIC_MODE_SWITCH,     // 模式切换
    BUZZER_MUSIC_SCENE,           // 场景触发
    BUZZER_MUSIC_WIFI_START,      // WiFi开始配网
    BUZZER_MUSIC_WIFI_END,        // WiFi结束配网
    BUZZER_MUSIC_WIFI_SCAN,       // WiFi搜索网络
    BUZZER_MUSIC_WIFI_CONNECTING, // WiFi连接中
    BUZZER_MUSIC_WIFI_CONNECTED,  // WiFi连接成功
    BUZZER_MUSIC_WIFI_FAILED,     // WiFi连接失败
    BUZZER_MUSIC_REMOTE_START,    // 遥控器配对开始
    BUZZER_MUSIC_REMOTE_PAIRING,  // 遥控器配对中
    BUZZER_MUSIC_REMOTE_SUCCESS,  // 遥控器配对成功
    BUZZER_MUSIC_REMOTE_FAILED,   // 遥控器配对失败
    BUZZER_MUSIC_COUNT            // 音乐总数
} buzzer_music_type_t;

// 获取预定义音乐数据
const buzzer_music_t *Buzzer_GetMusicData(buzzer_music_type_t type);

#endif /* __BUZZER_MUSIC_H__ */