#include "bsp_timer.h"
#include "bsp_adc.h"
#include "bsp_pwm.h"

/**
 * @brief 初始化定时器
 *
 */
void bsp_timer_init(void)
{
    stc_gtim_init_t stcInitCfg = {0};

    SYSCTRL_Func_Ctrl(Sysctrl_Fun_Gtim0_0_Btim012_1, FALSE); /* 配置BTIM0/1/2有效，GTIM0无效 */

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralCTim0);

    stcInitCfg.u32TaskMode      = GTIM_TASK_MODE_CONTINUOUS_COUNTER;
    stcInitCfg.u32WorkMode      = GTIM_WORK_MODE_PCLK;
    stcInitCfg.u32Prescaler     = GTIM_COUNTER_CLK_DIV32; // 32分频
    stcInitCfg.u32ToggleEn      = GTIM_TOGGLE_DISABLE;
    stcInitCfg.u32AutoReloadVal = 74; // 50us周期（20kHz）

    Gtim_Init(HC_GTIM0, &stcInitCfg);

    Gtim_DisableCompareCaptureAll(HC_GTIM0);

    Gtim_ClearFlag(HC_GTIM0, GTIM_IT_CLR_UI);
    Gtim_EnableIT(HC_GTIM0, GTIM_IT_UI);
    Gtim_EnableIT(HC_GTIM0, GTIM_OV_TRIG_ADC);
    EnableNvic(GTIM0_UEV_ETR_BTIMA_IRQn, IrqLevel4, TRUE);

    Gtim_Enable(HC_GTIM0);
}

/**
 * @brief GTIMER中断服务函数
 *
 */
void GTim0_Uev_Etr_BTimA_IRQHandler(void)
{
    if (TRUE == Gtim_IsActiveFlag(HC_GTIM0, GTIM_IT_FLAG_UI)) {
        Gtim_ClearFlag(HC_GTIM0, GTIM_IT_CLR_UI);

        soft_pwm_update();
    }
}
