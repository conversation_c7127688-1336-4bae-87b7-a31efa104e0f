#ifndef VIBR_TASK_H
#define VIBR_TASK_H

#include "app_config.h"
#include "app_log.h"
#include "bsp.h"

// ================== 振动电机模式定义 ==================
/**
 * @brief 振动电机工作模式
 */
typedef enum {
    VIBR_MODE_NONE = 0,   // 无振动
    VIBR_MODE_CONTINUOUS, // 连续模式
    VIBR_MODE_PULSE1,     // 脉冲1（100ms）
    VIBR_MODE_PULSE2,     // 脉冲2（300ms）
    VIBR_MODE_PULSE3,     // 脉冲3（500ms）
    VIBR_MODE_GRADIENT,   // 渐变模式
    VIBR_MODE_MAX
} VibrMode;

// ================== 振动电机ID定义 ==================
/**
 * @brief 振动电机编号
 */
typedef enum {
    VIBR_ID_1 = 0, // 电机1
    VIBR_ID_2,     // 电机2
    VIBR_ID_3,     // 电机3
    VIBR_ID_4,     // 电机4
    VIBR_ID_MAX
} VibrID;

// ================== 振动电机定时档位定义 ==================
/**
 * @brief 振动电机定时档位
 */
typedef enum {
    VIBR_TIME_10MIN = 0, // 10分钟
    VIBR_TIME_20MIN,     // 20分钟
    VIBR_TIME_30MIN,     // 30分钟
    VIBR_TIME_CUSTOM,    // 自定义
    VIBR_TIME_MAX
} VibrTimeLevel;

// ================== 振动电机强度档位定义 ==================
typedef enum {
    VIBR_LEVEL_LOW = 0, // 低档
    VIBR_LEVEL_MEDIUM,  // 中档
    VIBR_LEVEL_HIGH,    // 高档
    VIBR_LEVEL_MAX
} VibrStrengthLevel;

#define VIBR_DUTY_LOW    50
#define VIBR_DUTY_MEDIUM 70
#define VIBR_DUTY_HIGH   100

static inline uint8_t Vibr_LevelToDuty(VibrStrengthLevel level)
{
    switch (level) {
        case VIBR_LEVEL_LOW:
            return VIBR_DUTY_LOW;
        case VIBR_LEVEL_MEDIUM:
            return VIBR_DUTY_MEDIUM;
        case VIBR_LEVEL_HIGH:
            return VIBR_DUTY_HIGH;
        default:
            return VIBR_DUTY_LOW;
    }
}

// ================== PWM参数配置 ==================
#define VIBR_MAX_DUTY        100 // 最大占空比
#define VIBR_MIN_DUTY        0   // 最小占空比
#define VIBR_DEFAULT_DUTY    80  // 默认占空比

// ================== 时间参数配置 ==================
#define VIBR_DEFAULT_TIMEOUT 60000            // 默认超时时间(ms)
#define VIBR_TIME_10MIN_MS   (10 * 60 * 1000) // 10分钟(ms)
#define VIBR_TIME_20MIN_MS   (20 * 60 * 1000) // 20分钟(ms)
#define VIBR_TIME_30MIN_MS   (30 * 60 * 1000) // 30分钟(ms)

// ================== 模式参数配置 ==================
#define VIBR_PULSE1_PERIOD   100 // 脉冲1周期(ms)
#define VIBR_PULSE2_PERIOD   300 // 脉冲2周期(ms)
#define VIBR_PULSE3_PERIOD   500 // 脉冲3周期(ms)
#define VIBR_PULSE_DUTY      80  // 跳动模式占空比
#define VIBR_GRADIENT_PERIOD 100 // 渐变模式周期(ms)
#define VIBR_GRADIENT_STEP   5   // 渐变模式步进值

// ================== 事件类型定义 ==================
/**
 * @brief 振动电机事件类型
 */
typedef enum {
    VIBR_EVT_START,     // 启动事件
    VIBR_EVT_STOP,      // 停止事件
    VIBR_EVT_TIMER_SET, // 设置定时器事件
    VIBR_EVT_TIMEOUT,   // 超时事件
    VIBR_EVT_TICK,      // tick事件
    // ...可扩展
} VibrEventType;

// ================== 事件消息体结构体 ==================
/**
 * @brief 振动电机事件消息体
 */
typedef struct {
    VibrEventType type; ///< 事件类型
    union {
        struct {
            uint32_t timeout;        ///< 超时时间(ms)
            uint8_t idMask;          ///< 电机ID掩码
            VibrMode mode;           ///< 振动模式
            VibrStrengthLevel level; ///< 振动强度档位
        } start;
        struct {
            uint8_t idMask; ///< 电机ID掩码
        } stop;
        struct {
            uint32_t period; ///< 周期(ms)
            uint32_t count;  ///< 重复次数
            uint8_t idMask;  ///< 电机ID掩码
        } timerSet;
        struct {
            uint8_t id; ///< 电机ID
        } timeout;
        struct {
            uint8_t id; ///< 电机ID
        } tick;
    } data;
} VibrEventMsg;

// ================== 电机状态结构体 ==================
/**
 * @brief 振动电机状态结构体
 *        使用位域优化内存
 */
typedef struct {
    uint32_t tickCount;     ///< tick计数
    uint32_t period;        ///< 定时器周期(ms)
    uint32_t count;         ///< 重复次数
    uint32_t tick_ms;       ///< 距离下次tick的剩余时间(ms)
    uint32_t tick_reload;   ///< tick周期(ms)
    uint32_t timeout_ms;    ///< 距离超时的剩余时间(ms)
    uint32_t period_ms;     ///< 距离周期事件的剩余时间(ms)
    uint32_t period_reload; ///< 周期事件周期(ms)
    uint8_t duty;           ///< 当前PWM占空比
    uint8_t enabled;        ///< 使能标志(0:未启用, 1:启用)
    uint8_t mode : 3;       ///< 当前振动模式
    uint8_t direction : 1;  ///< 渐变方向(0:递增, 1:递减)
    uint8_t level : 2;      ///< 当前强度档位
    uint8_t reserved : 2;   ///< 保留位
} VibrState;

// ================== PWM端口相关宏 ==================
// vibrationMotor PWM通道配置（请根据你的硬件实际修改）
#define VIBR1_PWM_PORT           PWM32 // 电机1 PWM通道
#define VIBR2_PWM_PORT           PWM31 // 电机2 PWM通道
#define VIBR3_PWM_PORT           PWM53 // 电机3 PWM通道
#define VIBR4_PWM_PORT           PWM54 // 电机4 PWM通道

// PWM操作宏
#define VIBR_PWM_SET(port, duty) bsp_SetPwmDuty(port, duty)

// PWM模式配置
#define VIBR_PWM_INVERTED        0 // PWM正向模式（0：正向，1：反向）

// PWM占空比计算宏
#if VIBR_PWM_INVERTED
#define VIBR_CALC_PWM_DUTY(value, max) (max - ((value * max) / 100))
#else
#define VIBR_CALC_PWM_DUTY(value, max) ((value * max) / 100)
#endif

// 根据ID获取对应的PWM通道
#define GET_VIBR_PWM_PORT(id) (                                             \
    (id) == VIBR_ID_1 ? VIBR1_PWM_PORT : (id) == VIBR_ID_2 ? VIBR2_PWM_PORT \
                                     : (id) == VIBR_ID_3   ? VIBR3_PWM_PORT \
                                     : (id) == VIBR_ID_4   ? VIBR4_PWM_PORT \
                                                           : VIBR1_PWM_PORT)

// ================== 外部接口声明（兼容原AO接口） ==================
/**
 * @brief 振动电机任务初始化，需在app_init中调用
 */
void vibr_task_init(void);

/**
 * @brief 启动单个振动电机
 * @param id 电机ID
 * @param mode 振动模式
 * @param timeout 超时时间(ms)
 */
void Vibr_Start(VibrID id, VibrMode mode, uint32_t timeout);

/**
 * @brief 停止单个振动电机
 * @param id 电机ID
 */
void Vibr_Stop(VibrID id);

/**
 * @brief 设置单个电机定时器
 * @param id 电机ID
 * @param period 周期(ms)
 * @param count 重复次数
 */
void Vibr_SetTimer(VibrID id, uint32_t period, uint32_t count);

/**
 * @brief 按时间档位启动单个电机
 * @param id 电机ID
 * @param mode 振动模式
 * @param level 时间档位
 */
void Vibr_StartWithTimeLevel(VibrID id, VibrMode mode, VibrTimeLevel level);

/**
 * @brief 按自定义分钟数启动单个电机
 * @param id 电机ID
 * @param mode 振动模式
 * @param minutes 分钟数
 */
void Vibr_StartWithCustomTime(VibrID id, VibrMode mode, uint32_t minutes);

/**
 * @brief 启动多个振动电机
 * @param ids 电机ID数组
 * @param count 数量
 * @param mode 振动模式
 * @param timeout 超时时间(ms)
 */
void Vibr_StartMultiple(const VibrID *ids, uint8_t count, VibrMode mode, uint32_t timeout);

/**
 * @brief 停止多个振动电机
 * @param ids 电机ID数组
 * @param count 数量
 */
void Vibr_StopMultiple(const VibrID *ids, uint8_t count);

/**
 * @brief 停止所有振动电机
 */
void Vibr_StopAll(void);

/**
 * @brief 设置多个电机定时器
 * @param ids 电机ID数组
 * @param count 数量
 * @param period 周期(ms)
 * @param repeatCount 重复次数
 */
void Vibr_SetTimerMultiple(const VibrID *ids, uint8_t count, uint32_t period, uint32_t repeatCount);

/**
 * @brief 按时间档位启动多个电机
 * @param ids 电机ID数组
 * @param count 数量
 * @param mode 振动模式
 * @param level 时间档位
 */
void Vibr_StartMultipleWithTimeLevel(const VibrID *ids, uint8_t count, VibrMode mode, VibrTimeLevel level);

/**
 * @brief 按自定义分钟数启动多个电机
 * @param ids 电机ID数组
 * @param count 数量
 * @param mode 振动模式
 * @param minutes 分钟数
 */
void Vibr_StartMultipleWithCustomTime(const VibrID *ids, uint8_t count, VibrMode mode, uint32_t minutes);

/**
 * @brief 启动所有振动电机
 * @param mode 振动模式
 * @param timeout 超时时间(ms)
 */
void Vibr_StartAll(VibrMode mode, uint32_t timeout);

/**
 * @brief 电机ID数组转bitmask
 * @param ids 电机ID数组
 * @param count 数量
 * @return bitmask
 */
uint8_t Vibr_ConvertIdsToBitMask(const VibrID *ids, uint8_t count);

/**
 * @brief 时间档位转超时时间
 * @param level 档位
 * @return 超时时间(ms)
 */
uint32_t Vibr_GetTimeoutFromLevel(VibrTimeLevel level);

/**
 * @brief 复位电机状态
 * @param state 状态指针
 */
void Vibr_ResetMotorState(VibrState *state);

/**
 * @brief 判断所有电机是否停止
 * @return true-全部停止 false-有电机在运行
 */
bool Vibr_IsAllMotorsStopped(void);

/**
 * @brief 启动单个振动电机（带强度档位）
 * @param id 电机ID
 * @param mode 振动模式
 * @param timeout 超时时间(ms)
 * @param level 振动强度档位
 */
void Vibr_StartWithLevel(VibrID id, VibrMode mode, uint32_t timeout, VibrStrengthLevel level);

/**
 * @brief 启动多个振动电机（带强度档位）
 * @param ids 电机ID数组
 * @param count 数量
 * @param mode 振动模式
 * @param timeout 超时时间(ms)
 * @param level 振动强度档位
 */
void Vibr_StartMultipleWithLevel(const VibrID *ids, uint8_t count, VibrMode mode, uint32_t timeout, VibrStrengthLevel level);

/**
 * @brief 启动所有振动电机（带强度档位）
 * @param mode 振动模式
 * @param timeout 超时时间(ms)
 * @param level 振动强度档位
 */
void Vibr_StartAllWithLevel(VibrMode mode, uint32_t timeout, VibrStrengthLevel level);

#endif // VIBR_TASK_H