#ifndef __BSP_USART_H__
#define __BSP_USART_H__

#include "config.h"

#ifdef USE_USART

#define USART_MAX_NUM 4

#ifdef USE_USART0
#define USART0      0
#define USART0_BAUD 115200
extern void bsp_Usart0Init(unsigned int baud);
#endif

#ifdef USE_USART1
#define USART1      1
#define USART1_BAUD 115200
extern void bsp_Usart1Init(unsigned int baud);
#endif

#ifdef USE_USART2
#define USART2      2
#define USART2_BAUD 115200
extern void bsp_Usart2Init(unsigned int baud);
#endif

#ifdef USE_USART3
#define USART3      3
#define USART3_BAUD 115200
extern void bsp_Usart3Init(unsigned int baud);
#endif

#define USART0_RXBUF_SIZE 64
#define USART0_TXBUF_SIZE 64

#define USART1_RXBUF_SIZE 64
#define USART1_TXBUF_SIZE 64

#define USART2_RXBUF_SIZE 64
#define USART2_TXBUF_SIZE 1024

#define USART3_RXBUF_SIZE 256
#define USART3_TXBUF_SIZE 64

extern unsigned int usart_send_buf(unsigned char usart, unsigned char *buf, unsigned int size);
extern unsigned int usart_recv_buf(unsigned char usart, unsigned char *buf, unsigned int size);

extern ring_buf_t *usart_get_rxrb(unsigned char usart);
extern ring_buf_t *usart_get_txrb(unsigned char usart);

#endif

#endif