/**
 * @file app.c
 * <AUTHOR> (<EMAIL>)
 * @brief APP任务初始化
 * @version 0.1
 * @date 2025-05-05
 *
 * @copyright Copyright (c) 2025
 *
 */

#include "app.h"
#include "param_task.h"

static TaskHandle_t AppTaskCreate_Handle; // 任务句柄

/**
 * @brief AppTaskCreate任务
 *
 */
static void app_task_create_entry(void *pvParameters)
{
#if USE_ASYNC_LOG
    /* 初始化异步日志任务 */
    log_async_task_init();
#endif

    /* 初始化参数任务 */
    param_task_init();

    /* 初始化蓝牙任务 */
    ble_task_init();

    /* 初始化WiFi任务 */
    wifi_task_init();

    /* 初始化电机任务 */
    motor_task_init();

    /* 检查蓝牙配对状态 */
    if (get_pairing_status() == 1) {
        BUZZER_PLAY_POWER_ON();
    }

    vTaskDelete(AppTaskCreate_Handle); // 删除AppTaskCreate任务
}

/**
 * @brief APP任务初始化
 *
 */
void app_init(void)
{
    /* 创建AppTaskCreate任务 */
    if (AppTaskCreate_Handle == NULL) {
        BaseType_t xReturn = xTaskCreate(app_task_create_entry,
                                         "app_task_create",
                                         APP_APP_TASK_STACK_SIZE,
                                         NULL,
                                         APP_APP_PRIO,
                                         &AppTaskCreate_Handle);
        if (xReturn != pdPASS) {
            LOG_ERROR("app", "app_init: xTaskCreate failed");
        }
    }

    /* 初始化LED控制模块 */
    led_task_init();

    /* 初始化RGB任务 */
    rgb_task_init();

    /* 初始化振动器任务 */
    vibr_task_init();

    /* 初始化蜂鸣器任务 */
    buzzer_task_init();

    /* 初始化LIN任务 */
    lin_task_init();

    /* 启动任务监控任务 */
#if MONITOR_ENABLE
    monitor_task_init();
#endif

    /* 初始化消息任务 */
    msg_task_init();

    LED_BLINK_NORMAL(LED_SYS);
}

#if (configUSE_IDLE_HOOK == 1)
/**
 * task.h
 * @code{c}
 * void vApplicationIdleHook( void );
 * @endcode
 *
 * The application idle hook is called by the idle task.
 * This allows the application designer to add background functionality without
 * the overhead of a separate task.
 * NOTE: vApplicationIdleHook() MUST NOT, UNDER ANY CIRCUMSTANCES, CALL A FUNCTION THAT MIGHT BLOCK.
 */
/* MISRA Ref 8.6.1 [External linkage] */
/* More details at: https://github.com/FreeRTOS/FreeRTOS-Kernel/blob/main/MISRA.md#rule-86 */
/* coverity[misra_c_2012_rule_8_6_violation] */
void vApplicationIdleHook(void)
{
    static uint32_t usIdleTickCnt = 0;

    /* 每10秒打印一次CPU使用率 */
    usIdleTickCnt++;
    if (usIdleTickCnt >= 100000) {
        usIdleTickCnt = 0;
    }
}
#endif

#if (configCHECK_FOR_STACK_OVERFLOW > 0)

/**
 * task.h
 * @code{c}
 * void vApplicationStackOverflowHook( TaskHandle_t xTask, char *pcTaskName);
 * @endcode
 *
 * The application stack overflow hook is called when a stack overflow is detected for a task.
 *
 * Details on stack overflow detection can be found here: https://www.FreeRTOS.org/Stacks-and-stack-overflow-checking.html
 *
 * @param xTask the task that just exceeded its stack boundaries.
 * @param pcTaskName A character string containing the name of the offending task.
 */
/* MISRA Ref 8.6.1 [External linkage] */
/* More details at: https://github.com/FreeRTOS/FreeRTOS-Kernel/blob/main/MISRA.md#rule-86 */
/* coverity[misra_c_2012_rule_8_6_violation] */
void vApplicationStackOverflowHook(TaskHandle_t xTask, char *pcTaskName)
{
    /* 堆栈溢出处理代码 */
    /* 可以记录日志、重启系统或采取其他措施 */
    // printf("Stack overflow detected in task: %s\n", pcTaskName);

    /* 停止系统或重启 */
    taskDISABLE_INTERRUPTS();
    for (;;);
}

#endif
