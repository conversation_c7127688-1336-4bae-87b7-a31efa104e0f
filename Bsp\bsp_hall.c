/**
 * @file bsp_hall.c
 * <AUTHOR> (<EMAIL>)
 * @brief hall驱动
 * @version 0.1
 * @date 2024-09-11
 *
 * @copyright Copyright (c) 2024
 *
 */
#include "bsp_hall.h"
#include "bsp.h"

#ifdef USE_HALL

static hall_callback_t g_hallCallback = NULL;
static uint32_t g_lastTrigTime[4]     = {0};             // 上次触发时间
static bool g_lastState[4]            = {false};         // 上次状态
static hall_dir_t g_lastDir[4]        = {HALL_DIR_NONE}; // 上次方向

// 默认配置
static const hall_config_t default_config = {
    .reverse_direction  = false,
    .debounce_time      = 10,
    .stop_timeout       = 1000,
    .min_valid_interval = 5};

static hall_config_t g_hallConfig[4];
static hall_status_t g_hallStatus[4];

// 获取对应的引脚和端口
static uint16_t get_hall_pin(uint8_t hallId)
{
    switch (hallId) {
        case 0:
            return HALL0_PIN;
        case 1:
            return HALL1_PIN;
        case 2:
            return HALL2_PIN;
        case 3:
            return HALL3_PIN;
        default:
            return 0;
    }
}

static GPIO_TypeDef *get_hall_port(uint8_t hallId)
{
    switch (hallId) {
        case 0:
        case 1:
        case 2:
        case 3:
            return HC_GPIOB; // 所有霍尔传感器都在GPIOB上
        default:
            return NULL;
    }
}

/**
 * @brief 霍尔信号检测初始化
 */
void bsp_HallInit(uint8_t hallId)
{
    stc_gpio_init_t stcGpioInit;

    // 使能GPIO时钟
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpiob);

    // 配置GPIO为外部中断输入
    DDL_ZERO_STRUCT(stcGpioInit);
    stcGpioInit.u32Pin      = get_hall_pin(hallId);
    stcGpioInit.u32Mode     = GPIO_MODE_INPUT;          // 输入模式
    stcGpioInit.u32PullUpDn = GPIO_PULL_UP;             // 上拉
    stcGpioInit.u32ExtInt   = GPIO_EXTI_RISING_FALLING; // 双边沿触发
    GPIO_Init(get_hall_port(hallId), &stcGpioInit);

    // 清除中断标志
    GPIO_ExtIrqStateClear(get_hall_port(hallId), get_hall_pin(hallId));

    // 配置NVIC
    EnableNvic(HALL_IRQn, IrqLevel6, TRUE);

    // 初始化配置和状态
    g_hallConfig[hallId] = default_config;
    bsp_HallReset(hallId);
}

/**
 * @brief 读取霍尔传感器状态
 */
bool bsp_ReadHall(uint8_t hallId)
{
    GPIO_TypeDef *port = get_hall_port(hallId);
    uint16_t pin       = get_hall_pin(hallId);

    if (port && pin) {
        return REG_READBITS(port->IN, pin) ? true : false;
    }
    return false;
}

/**
 * @brief 注册回调函数
 */
void bsp_HallRegisterCallback(hall_callback_t callback)
{
    g_hallCallback = callback;
}

/**
 * @brief 使能/失能中断
 */
void bsp_HallInterruptEnable(uint8_t hallId, bool enable)
{
    uint16_t pin       = get_hall_pin(hallId);
    GPIO_TypeDef *port = get_hall_port(hallId);

    if (port && pin) {
        if (enable) {
            REG_SETBITS(port->RISEIE, pin); // 使能上升沿中断
            REG_SETBITS(port->FALLIE, pin); // 使能下降沿中断
        } else {
            REG_CLEARBITS(port->RISEIE, pin); // 禁止上升沿中断
            REG_CLEARBITS(port->FALLIE, pin); // 禁止下降沿中断
        }
    }
}

/**
 * @brief 判断方向
 */
static hall_dir_t determine_direction(uint8_t hallId, bool curState)
{
    uint32_t curTime = BSP_GetTickCount();
    hall_dir_t dir   = g_hallStatus[hallId].last_dir;

    // 检测状态变化
    if (curState != g_hallStatus[hallId].last_state) {
        // 计算时间间隔
        uint32_t interval = curTime - g_hallStatus[hallId].last_trig_time;

        // 去抖动处理
        if (interval < g_hallConfig[hallId].min_valid_interval) {
            g_hallStatus[hallId].error_count++;
            return dir;
        }

        // 检查停止超时
        if (interval > g_hallConfig[hallId].stop_timeout) {
            dir = HALL_DIR_NONE;
        } else {
            // 根据配置判断方向
            if (curState != g_hallStatus[hallId].last_state) {
                if (curState) {
                    dir = g_hallConfig[hallId].reverse_direction ? HALL_DIR_DOWN : HALL_DIR_UP;
                } else {
                    dir = g_hallConfig[hallId].reverse_direction ? HALL_DIR_UP : HALL_DIR_DOWN;
                }
                g_hallStatus[hallId].pulse_count++;
            }
        }

        // 更新状态
        g_hallStatus[hallId].last_state     = curState;
        g_hallStatus[hallId].last_trig_time = curTime;
        g_hallStatus[hallId].last_dir       = dir;
    }

    return dir;
}

/**
 * @brief GPIO中断服务函数
 */
void PORTB_IRQHandler(void)
{
    uint8_t hallId;
    uint16_t pin;
    bool state;
    GPIO_TypeDef *port;
    hall_dir_t dir;

    // 检查每个霍尔传感器的中断标志
    for (hallId = 0; hallId < 4; hallId++) {
        pin  = get_hall_pin(hallId);
        port = get_hall_port(hallId);

        if (port && GPIO_ExtIrqStateGet(port, pin)) {
            // 清除中断标志
            GPIO_ExtIrqStateClear(port, pin);

            // 读取当前状态
            state = REG_READBITS(port->IN, pin) ? true : false;

            // 判断方向
            dir = determine_direction(hallId, state);

            // 调用回调函数
            if (g_hallCallback) {
                g_hallCallback(hallId, state, dir);
            }
        }
    }
}

void bsp_HallSetConfig(uint8_t hallId, const hall_config_t *config)
{
    if (hallId < 4 && config != NULL) {
        g_hallConfig[hallId] = *config;
    }
}

void bsp_HallGetStatus(uint8_t hallId, hall_status_t *status)
{
    if (hallId < 4 && status != NULL) {
        *status = g_hallStatus[hallId];
    }
}

void bsp_HallReset(uint8_t hallId)
{
    if (hallId < 4) {
        g_hallStatus[hallId].pulse_count    = 0;
        g_hallStatus[hallId].error_count    = 0;
        g_hallStatus[hallId].last_dir       = HALL_DIR_NONE;
        g_hallStatus[hallId].last_state     = bsp_ReadHall(hallId);
        g_hallStatus[hallId].last_trig_time = BSP_GetTickCount();
    }
}

#endif