/**
 * @file bsp_led.c
 * <AUTHOR> (<EMAIL>)
 * @brief led驱动
 * @version 0.1
 * @date 2024-10-08
 *
 * @copyright Copyright (c) 2024
 *
 */
#include "bsp_led.h"

/**
 * @brief led端口初始化
 *
 */
static void led_PortConfig(void)
{
    stc_gpio_init_t stcGpioInit;

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpioa); /* GPIOA外设时钟使能 */
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpiob); /* GPIOB外设时钟使能 */

    /* LED端口初始化 */
    DDL_ZERO_STRUCT(stcGpioInit); /* 结构体初始化清零             */
    GPIO_PA05_SET();              /* 端口初始电平->高, 关闭LED    */
    GPIO_PA06_SET();              /* 端口初始电平->高, 关闭LED    */
    GPIO_PB01_SET();              /* 端口初始电平->高, 关闭LED    */

    stcGpioInit.u32Mode        = GPIO_MODE_OUTPUT_PP; /* 端口方向配置->推挽输出       */
    stcGpioInit.u32DriverLevel = GPIO_Driver_Level_H; /* 端口驱动能力配置->高驱动能力  */
    stcGpioInit.u32PullUpDn    = GPIO_PULL_NONE;      /* 端口上下拉配置->无上下拉      */

    stcGpioInit.u32Pin = SYSLED_PIN;      /* 端口引脚 */
    GPIO_Init(SYSLED_PORT, &stcGpioInit); /* 端口初始化 */

    stcGpioInit.u32Pin = WIFILED_PIN;      /* 端口引脚 */
    GPIO_Init(WIFILED_PORT, &stcGpioInit); /* 端口初始化 */

    stcGpioInit.u32Pin = BLELED_PIN;      /* 端口引脚 */
    GPIO_Init(BLELED_PORT, &stcGpioInit); /* 端口初始化 */
}

/**
 * @brief led初始化
 *
 */
void bsp_LedInit(void)
{
    led_PortConfig();
}