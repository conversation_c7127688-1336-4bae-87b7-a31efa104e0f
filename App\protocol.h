#ifndef __PROTOCOL_H__
#define __PROTOCOL_H__

#include <stdint.h>

#define PROTOCOL_DEFAULT_FRONT_LENGTH   12                                                                                                                         // 帧头+版本号+帧控制域+帧流水号+时间戳
#define PROTOCOL_DEFAULT_CRC_LENGTH     2                                                                                                                          // CRC16校验
#define PROTOCOL_DEFAULT_TAIL_LENGTH    2                                                                                                                          // 帧尾
#define PROTOCOL_MAX_PAYLOAD_LENGTH     132                                                                                                                        // 数据负载最大长度
#define PROTOCOL_MAX_TOTAL_LENGTH       (PROTOCOL_DEFAULT_FRONT_LENGTH + PROTOCOL_DEFAULT_CRC_LENGTH + PROTOCOL_DEFAULT_TAIL_LENGTH + PROTOCOL_MAX_PAYLOAD_LENGTH) // 最大总长度

#define PROTOCOL_HEAD                   0xA5A5 // 帧头
#define PROTOCOL_TAIL                   0x0D0A // 帧尾

#define PROTOCOL_APP_CMD                0x80 // 应用层命令
#define PROTOCOL_OTA_CMD                0xF0 // OTA升级命令
#define PROTOCOL_TEST_CMD               0xE0 // 测试命令

#define PROTOCOL_PARTCTRL_CMD           0x01 // 部位控制命令
#define PROTOCOL_MASSAGECTRL_CMD        0x02 // 按摩控制命令
#define PROTOCOL_LIGHTCTRL_CMD          0x03 // 灯光控制命令
#define PROTOCOL_WORKMODECTRL_CMD       0x04 // 工作模式控制命令
#define PROTOCOL_STOPCTRL_CMD           0x05 // 停止控制命令
#define PROTOCOL_FULLCTRL_CMD           0x06 // 全控制命令
#define PROTOCOL_RESERVED1_CMD          0x07 // 保留命令
#define PROTOCOL_MEMORYCTRL_CMD         0x08 // 记忆控制命令
#define PROTOCOL_MASSAGEMODECTRL_CMD    0x09 // 按摩模式控制命令
#define PROTOCOL_MASSAGESTRENTHCTRL_CMD 0x0A // 按摩强度控制命令
#define PROTOCOL_MASSAGESTATUS_CMD      0x0B // 按摩状态命令
#define PROTOCOL_RESERVED2_CMD          0x0C // 保留命令
#define PROTOCOL_CLOSEMASSAGE_CMD       0x0D // 关闭按摩命令
#define PROTOCOL_SMARTMODECTRL_CMD      0x0E // 智能模式控制命令
#define PROTOCOL_SOFTWAREVERSION_CMD    0x0F // 软件版本命令
#define PROTOCOL_RESERVED3_CMD          0x10 // 保留命令
#define PROTOCOL_RESERVED4_CMD          0x11 // 保留命令
#define PROTOCOL_RESERVED5_CMD          0x12 // 保留命令
#define PROTOCOL_RESERVED6_CMD          0x13 // 保留命令
#define PROTOCOL_RESERVED7_CMD          0x14 // 保留命令
#define PROTOCOL_HARTBEAT_CMD           0x15 // 心跳命令
#define PROTOCOL_HARDWAREVERSION_CMD    0x16 // 硬件版本命令

#define PROTOCOL_STARTOTA_CMD           0x01 // 开始OTA命令
#define PROTOCOL_OTATRANSFER_CMD        0x02 // OTA传输命令
#define PROTOCOL_ENDOTA_CMD             0x03 // 结束OTA命令

#define PROTOCOL_SUCCESS_CMD            0x00 // 成功命令
#define PROTOCOL_ERROR_CMD              0xe1 // 错误命令

typedef enum {
    NO_PART = 0, // 无部位
    HEAD_PART,   // 头部
    BACK_PART,   // 背部
    WAIST_PART,  // 腰部
    LEG_PART,    // 腿部
    FULL_PART,   // 全部位
} part_t;

typedef enum {
    NO_SIDE = 0, // 无侧
    LEFT_SIDE,   // 左侧
    RIGHT_SIDE,  // 右侧
    BOTH_SIDE,   // 两侧
} side_t;

typedef enum {
    NO_MOVE = 0, // 无移动
    MOVE_UP,     // 上移
    MOVE_DOWN,   // 下移
    STOP_MOVE,   // 停止移动
} move_t;

typedef enum {
    NO_STRENTH = 0,           // 无强度
    MASSAGE_STRENGTH_LEVEL_1, // 按摩强度1
    MASSAGE_STRENGTH_LEVEL_2, // 按摩强度2
    MASSAGE_STRENGTH_LEVEL_3, // 按摩强度3
} massage_strength_level_t;

typedef enum {
    NO_LIGHT = 0, // 无灯光
    LIGHT_ON,     // 灯光开
    LIGHT_OFF,    // 灯光关
} light_t;

typedef enum {
    NO_WORKMODE = 0,  // 无工作模式
    CAUSUAL_WORKMODE, // 正常模式
    SLEEP_WORKMODE,   // 睡眠模式
    SNORE_WORKMODE,   // 打鼾模式
    SPACE_WORKMODE,   // 空间模式
    READ_WORKMODE,    // 阅读模式
    FEEDING_WORKMODE, // 喂食模式
    YOGA_WORKMODE,    // 瑜伽模式
    WAIST_WORKMODE,   // 腰部模式
    MASSAGE_WORKMODE, // 按摩模式
    SHOW_WORKMODE,    // 展示模式
    DRINK_WORKMODE,   // 饮酒模式
} work_mode_t;

typedef enum{
    NO_SMART_MODE = 0,
    SMART_SLEEP_MODE,
    SMART_YOGA_MODE,
    SMART_RECOVERY_MODE,
    SMART_WAIST_CARE_MODE,
    SMART_WAKEUP_MODE,
    SMART_AUTO_SNORE_MODE,
} smart_mode_t;

typedef enum{
    SMART_MODE_OFF = 0,
    SMART_MODE_ON,
    SMART_MODE_CHECK,
} smart_mode_status_t;

typedef enum {
    NO_MASSAGE_MODE = 0,    // 无按摩模式
    CONSTANT_MASSAGE_MODE,  // 常速模式
    PULSE_MASSAGE_MODE,     // 脉冲模式
    VIBRATION_MASSAGE_MODE, // 振动模式
    WAVE_MASSAGE_MODE,      // 波浪模式
    PULSE1_MASSAGE_MODE,    // 脉冲1模式
    PULSE2_MASSAGE_MODE,    // 脉冲2模式
    PULSE3_MASSAGE_MODE,    // 脉冲3模式
} massage_work_mode_t;

typedef enum {
    NO_MEMORY = 0, // 无记忆
    MEMORY1_MODE,  // 记忆1模式
    MEMORY2_MODE,  // 记忆2模式
    MEMORY3_MODE,  // 记忆3模式
} memory_mode_t;

typedef enum {
    SET_MEMORY_ACTION = 0, // 设置记忆
    GET_MEMORY_ACTION,     // 获取记忆
} memory_action_t;

typedef union {
    struct {
        uint8_t bit0 : 1; // 0-数据帧 1-ACK帧
        uint8_t bit1 : 1; // 默认0
        uint8_t bit2 : 1; // 默认0
        uint8_t bit3 : 1; // 默认0
        uint8_t bit4 : 1; // 0-不加密 1-加密方式1（xor）（加密范围从帧流水号到数据负载）
        uint8_t bit5 : 1;
        uint8_t bit6 : 1; // 0-无需ACK 1-需要ACK
        uint8_t bit7 : 1; // 0-关闭校验 1-开启校验
    };
    uint8_t value;
} frame_ctl_t;

typedef struct {
    uint8_t main_cmd;    // 主命令
    uint8_t sub_cmd;     // 子命令
    uint16_t cmd_len;    // 命令数据长度
    uint8_t cmd_data[0]; // 命令数据
} protocol_payload_t;

/**
 * @brief 协议帧结构体
 *
 * HEAD:         2字节，固定0xA5A5
 * VERSION:      1字节，协议版本号
 * FRAME_CTL:    1字节，帧控制域
 * SEQ_NUM:      2字节，帧流水号
 * TIMESTAMP:    4字节，时间戳
 * PAYLOAD_LEN:  2字节，数据负载长度（大端）
 * PAYLOAD:      N字节，数据负载
 * CRC:          2字节，CRC16校验
 * TAIL:         2字节，固定0x0D0A
 */
#pragma pack(1)
typedef struct
{
    uint16_t head;              // 帧头，固定0xA5A5
    uint8_t version;            // 协议版本号
    frame_ctl_t frame_ctl;      // 帧控制域
    uint16_t seq_num;           // 帧流水号
    uint32_t timestamp;         // 时间戳
    uint16_t payload_len;       // 数据负载长度（大端）
    protocol_payload_t payload; // 数据负载
    uint16_t crc;               // CRC16校验
    uint16_t tail;              // 帧尾，固定0x0D0A
} protocol_frame_t;
#pragma pack()

#pragma pack(1)
typedef union protocol_event {
    uint8_t data[PROTOCOL_MAX_TOTAL_LENGTH];
    protocol_frame_t frame;
} protocol_event_t;
#pragma pack()

void protocol_event_handle(protocol_event_t *event, uint8_t len);

#endif // __PROTOCOL_H__
