#include "bsp_iwdt.h"

/**
 * @brief 看门狗初始化
 * @param timeout_ms 超时时间（毫秒）
 */
void bsp_WatchdogInit(void)
{
    stc_iwdt_init_t stcIwdtInit = {0};

    /* 开启IWDT外设时钟 */
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralIwt);

    /* IWDT 初始化(独立开门狗） */
    /* IWDT 溢出时间 = (计数值(u32ArrCounter)/32800) * 分频系数(u32Prescaler) */
    /* 本例为：(3125/10K)*16 = 5.0s */
    stcIwdtInit.u32Action     = IWDT_OVER_RESET__SLEEPMODE_STOP;
    stcIwdtInit.u32ArrCounter = 3125U;
    stcIwdtInit.u32Window     = 0xFFFU;
    stcIwdtInit.u32Prescaler  = IWDT_RC10K_DIV_16;
    IWDT_Init(&stcIwdtInit);
}

/**
 * @brief 喂狗
 */
void bsp_WatchdogFeed(void)
{
    IWDT_Feed();
}
