/**
 * @file bsp_pwm.c
 * <AUTHOR> (<EMAIL>)
 * @brief pwm驱动
 * @version 0.1
 * @date 2024-09-11
 *
 * @copyright Copyright (c) 2024
 *
 */
#include "bsp_pwm.h"
#include "bsp_buzzer.h"

// 函数声明
static void pwm_PortInit(void);
static void pwm_Config(void);
static void pwm_init(void);

typedef struct {
    uint8_t duty;    // 占空比(0-100)
    uint8_t counter; // 计数器
    uint8_t period;  // 周期(定时器中断次数为一个PWM周期)
} soft_pwm_t;

static soft_pwm_t g_soft_pwm61 = {0, 0, 20};

/**
 * @brief pwm端口配置
 *
 */
static void pwm_PortInit(void)
{
    stc_gpio_init_t stcGpioInit = {0};

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpiob); /* GPIOB外设时钟使能 */
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpioc); /* GPIOC外设时钟使能 */
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpiod); /* GPIOD外设时钟使能 */

    GPIO_PB03_RESET(); /* 端口初始电平->低 */
    GPIO_PB04_RESET(); /* 端口初始电平->低 */
    GPIO_PB06_RESET(); /* 端口初始电平->低 */
    GPIO_PB07_RESET(); /* 端口初始电平->低 */
    GPIO_PB08_RESET(); /* 端口初始电平->低 */
    GPIO_PB09_RESET(); /* 端口初始电平->低 */

    GPIO_PC06_RESET(); /* 端口初始电平->低 */
    GPIO_PC07_RESET(); /* 端口初始电平->低 */
    GPIO_PC08_RESET(); /* 端口初始电平->低 */
    GPIO_PC09_RESET(); /* 端口初始电平->低 */
    GPIO_PC10_RESET(); /* 端口初始电平->低 */
    GPIO_PC11_RESET(); /* 端口初始电平->低 */
    GPIO_PC12_RESET(); /* 端口初始电平->低 */

    GPIO_PD02_RESET(); /* 端口初始电平->低 */
    GPIO_PD03_RESET(); /* 端口初始电平->低 */

    /* 端口初始化 */
    DDL_ZERO_STRUCT(stcGpioInit);                                                                                /* 结构体初始化清零 */
    stcGpioInit.u32Pin      = GPIO_PIN_03 | GPIO_PIN_04 | GPIO_PIN_06 | GPIO_PIN_07 | GPIO_PIN_08 | GPIO_PIN_09; /* 端口引脚 */
    stcGpioInit.u32Mode     = GPIO_MODE_OUTPUT_PP;                                                               /* 端口方向配置->推挽输出 */
    stcGpioInit.u32PullUpDn = GPIO_PULL_NONE;                                                                    /* 端口上下拉配置->无上下拉 */
    GPIOB_Init(&stcGpioInit);                                                                                    /* 端口初始化 */

    /* 端口初始化 */
    DDL_ZERO_STRUCT(stcGpioInit);                                                                                              /* 结构体初始化清零 */
    stcGpioInit.u32Pin      = GPIO_PIN_06 | GPIO_PIN_07 | GPIO_PIN_08 | GPIO_PIN_09 | GPIO_PIN_10 | GPIO_PIN_11 | GPIO_PIN_12; /* 端口引脚 */
    stcGpioInit.u32Mode     = GPIO_MODE_OUTPUT_PP;                                                                             /* 端口方向配置->推挽输出 */
    stcGpioInit.u32PullUpDn = GPIO_PULL_NONE;                                                                                  /* 端口上下拉配置->无上下拉 */
    GPIOC_Init(&stcGpioInit);                                                                                                  /* 端口初始化 */

    /* 端口初始化 */
    DDL_ZERO_STRUCT(stcGpioInit);
    stcGpioInit.u32Pin      = GPIO_PIN_02 | GPIO_PIN_03; /* 端口引脚 */
    stcGpioInit.u32Mode     = GPIO_MODE_OUTPUT_PP;       /* 端口方向配置->推挽输出 */
    stcGpioInit.u32PullUpDn = GPIO_PULL_NONE;            /* 端口上下拉配置->无上下拉 */
    GPIOD_Init(&stcGpioInit);                            /* 端口初始化 */

    GPIO_PB03_AF_ATIM3_CH1B_SET();
    GPIO_PB04_AF_ATIM3_CH2B_SET();
    GPIO_PB06_AF_CTIM2_CH0_SET();
    GPIO_PB07_AF_CTIM2_CH1_SET();
    GPIO_PB08_AF_CTIM2_CH2_SET();
    GPIO_PB09_AF_CTIM2_CH3_SET();

    GPIO_PC06_AF_CTIM1_CH0_SET();
    GPIO_PC07_AF_CTIM1_CH1_SET();
    GPIO_PC08_AF_CTIM1_CH2_SET();
    GPIO_PC09_AF_CTIM1_CH3_SET();
    GPIO_PC10_AF_ATIM3_CH0A_SET();
    GPIO_PC11_AF_ATIM3_CH1A_SET();
    GPIO_PC12_AF_ATIM3_CH2A_SET();

    GPIO_PD02_AF_ATIM3_CH0B_SET();
    // GPIO_PD03_AF_CTIM0_CH0_SET();
    GPIO_PD03_RESET();
}

/**
 * @brief pwm参数配置
 *
 */
static void pwm_Config(void)
{
    {
        uint16_t u16CntValue;
        stc_atim3_mode23_cfg_t stcAtim3BaseCfg         = {0};
        stc_atim3_m23_compare_cfg_t stcAtim3ChxACmpCfg = {0};
        stc_atim3_m23_compare_cfg_t stcAtim3ChxBCmpCfg = {0};

        SYSCTRL_PeriphClkEnable(SysctrlPeripheralATim3); /* Atimer3 外设时钟使能 */

        DDL_ZERO_STRUCT(stcAtim3BaseCfg);                                            /* 结构体初始化清零 */
        stcAtim3BaseCfg.u32WorkMode    = ATIM3_M23_M23CR_WORK_MODE_SAWTOOTH;         /* 锯齿波模式 */
        stcAtim3BaseCfg.u32CountClkSel = ATIM3_M23_M23CR_CT_PCLK;                    /* 定时器功能，计数时钟为内部PCLK */
        stcAtim3BaseCfg.u32PRS         = ATIM3_M23_M23CR_ATIM3CLK_PRS1;              /* PCLK */
        stcAtim3BaseCfg.u32CntDir      = ATIM3_M23_M23CR_DIR_UP_CNT;                 /* 向上计数，在三角波模式时只读 */
        stcAtim3BaseCfg.u32PWMTypeSel  = ATIM3_M23_M23CR_COMP_PWM_INDEPT;            /* 独立输出PWM */
        stcAtim3BaseCfg.u32PWM2sSel    = ATIM3_M23_M23CR_PWM2S_COMPARE_SINGLE_POINT; /* 单点比较功能 */
        stcAtim3BaseCfg.u32ShotMode    = ATIM3_M23_M23CR_SHOT_CYCLE;                 /* 循环计数 */
        stcAtim3BaseCfg.u32URSSel      = ATIM3_M23_M23CR_URS_OV_UND;                 /* 上下溢更新 */
        ATIM3_Mode23_Init(&stcAtim3BaseCfg);                                         /* ATIM3 的模式23功能初始化 */

        ATIM3_Mode23_ARRSet(PWM_PERIOD);
        ATIM3_Mode23_ARR_Buffer_Enable(TRUE); /* 设置重载值,并使能缓存 */

        ATIM3_Mode23_Channel_Compare_Value_Set(ATIM3_COMPARE_CAPTURE_CH0A, DEFAULT_COMPVALUE); /* 设置CH0比较值A */
        ATIM3_Mode23_Channel_Compare_Value_Set(ATIM3_COMPARE_CAPTURE_CH0B, DEFAULT_COMPVALUE); /* 设置CH0比较值B */

        ATIM3_Mode23_Channel_Compare_Value_Set(ATIM3_COMPARE_CAPTURE_CH1A, DEFAULT_COMPVALUE); /* 设置CH1比较值A */
        ATIM3_Mode23_Channel_Compare_Value_Set(ATIM3_COMPARE_CAPTURE_CH1B, DEFAULT_COMPVALUE); /* 设置CH1比较值B */

        ATIM3_Mode23_Channel_Compare_Value_Set(ATIM3_COMPARE_CAPTURE_CH2A, DEFAULT_COMPVALUE); /* 设置CH2比较值A */
        ATIM3_Mode23_Channel_Compare_Value_Set(ATIM3_COMPARE_CAPTURE_CH2B, DEFAULT_COMPVALUE); /* 设置CH2比较值B */

        DDL_ZERO_STRUCT(stcAtim3ChxACmpCfg); /* 结构体初始化清零 */
        stcAtim3ChxACmpCfg.u32CHxCmpCap      = ATIM3_M23_CRCHx_CSA_CSB_COMPARE;
        stcAtim3ChxACmpCfg.u32CHxCmpModeCtrl = ATIM3_M23_FLTR_OCMxx_PWM_MODE_2;  /* OCREFA输出控制OCMA:PWM模式2 */
        stcAtim3ChxACmpCfg.u32CHxCmpBufEn    = ATIM3_M23_CRCHx_BUFEx_ENABLE;     /* A通道缓存控制 */
        stcAtim3ChxACmpCfg.u32CHxCmpIntSel   = ATIM3_M23_M23CR_CISA_DISABLE_IRQ; /* A通道比较中断控制:无 */

        stcAtim3ChxACmpCfg.u32CHxPolarity = ATIM3_M23_FLTR_CCPxx_NORMAL_IN_OUT;              /* 正常输出 */
        ATIM3_Mode23_PortOutput_CHxA_Cfg(ATIM3_M23_OUTPUT_CHANNEL_CH0, &stcAtim3ChxACmpCfg); /* 比较输出端口配置 */
        stcAtim3ChxACmpCfg.u32CHxPolarity = ATIM3_M23_FLTR_CCPxx_NORMAL_IN_OUT;              /* 正常输出 */
        ATIM3_Mode23_PortOutput_CHxA_Cfg(ATIM3_M23_OUTPUT_CHANNEL_CH1, &stcAtim3ChxACmpCfg); /* 比较输出端口配置 */
        stcAtim3ChxACmpCfg.u32CHxPolarity = ATIM3_M23_FLTR_CCPxx_NORMAL_IN_OUT;              /* 正常输出 */
        ATIM3_Mode23_PortOutput_CHxA_Cfg(ATIM3_M23_OUTPUT_CHANNEL_CH2, &stcAtim3ChxACmpCfg); /* 比较输出端口配置 */

        DDL_ZERO_STRUCT(stcAtim3ChxBCmpCfg); /* 结构体初始化清零 */
        stcAtim3ChxBCmpCfg.u32CHxCmpCap      = ATIM3_M23_CRCHx_CSA_CSB_COMPARE;
        stcAtim3ChxBCmpCfg.u32CHxCmpModeCtrl = ATIM3_M23_FLTR_OCMxx_PWM_MODE_2;   /* OCREFB输出控制OCMB:PWM模式2(PWM互补模式下也要设置，避免强制输出) */
        stcAtim3ChxBCmpCfg.u32CHxCmpBufEn    = ATIM3_M23_CRCHx_BUFEx_ENABLE;      /* B通道缓存控制使能 */
        stcAtim3ChxBCmpCfg.u32CHxCmpIntSel   = ATIM3_M23_CRCHx_CISBx_DISABLE_IRQ; /* B通道比较中断控制:无 */

        stcAtim3ChxBCmpCfg.u32CHxPolarity = ATIM3_M23_FLTR_CCPxx_NORMAL_IN_OUT;              /* 正常输出 */
        ATIM3_Mode23_PortOutput_CHxB_Cfg(ATIM3_M23_OUTPUT_CHANNEL_CH0, &stcAtim3ChxBCmpCfg); /* 比较输出端口配置 */
        stcAtim3ChxBCmpCfg.u32CHxPolarity = ATIM3_M23_FLTR_CCPxx_NORMAL_IN_OUT;              /* 正常输出 */
        ATIM3_Mode23_PortOutput_CHxB_Cfg(ATIM3_M23_OUTPUT_CHANNEL_CH1, &stcAtim3ChxBCmpCfg); /* 比较输出端口配置 */
        stcAtim3ChxBCmpCfg.u32CHxPolarity = ATIM3_M23_FLTR_CCPxx_NORMAL_IN_OUT;              /* 正常输出 */
        ATIM3_Mode23_PortOutput_CHxB_Cfg(ATIM3_M23_OUTPUT_CHANNEL_CH2, &stcAtim3ChxBCmpCfg); /* 比较输出端口配置 */

        u16CntValue = 0;
        ATIM3_Mode23_Cnt16Set(u16CntValue); /* 设置计数初值 */

        ATIM3_Mode23_Run();                          /* 运行 */
        ATIM3_Mode23_Manual_PWM_Output_Enable(TRUE); /* 端口输出使能 */
    }

    {
        stc_gtim_init_t stcInitCfg = {0};

        // SYSCTRL_Func_Ctrl(Sysctrl_Fun_Gtim0_0_Btim012_1, FALSE); /* 配置BTIM0/1/2有效，GTIM0无效 */
        SYSCTRL_Func_Ctrl(Sysctrl_Fun_Gtim1_0_Btim345_1, FALSE); /* 配置BTIM3/4/5有效，GTIM0无效 */
        SYSCTRL_Func_Ctrl(Sysctrl_Fun_Gtim2_0_Btim678_1, FALSE); /* 配置BTIM6/7/8有效，GTIM0无效 */

        // SYSCTRL_PeriphClkEnable(SysctrlPeripheralCTim0); /* 使能BTIM0/0/2 外设时钟 */
        SYSCTRL_PeriphClkEnable(SysctrlPeripheralCTim1); /* 使能BTIM0/0/2 外设时钟 */
        SYSCTRL_PeriphClkEnable(SysctrlPeripheralCTim2); /* 使能BTIM0/0/2 外设时钟 */

        stcInitCfg.u32TaskMode      = GTIM_TASK_MODE_CONTINUOUS_COUNTER; /* 连续计数模式 */
        stcInitCfg.u32WorkMode      = GTIM_WORK_MODE_PCLK;               /* 工作模式: 定时模式，计数时钟源来自PCLK */
        stcInitCfg.u32Prescaler     = GTIM_COUNTER_CLK_DIV1;             /* 对计数时钟进行预除频 */
        stcInitCfg.u32ToggleEn      = GTIM_TOGGLE_DISABLE;               /* TOG输出禁止 */
        stcInitCfg.u32ToggleEn      = GTIM_TOGGLE_DISABLE;               /* TOG输出禁止 */
        stcInitCfg.u32AutoReloadVal = PWM_PERIOD;                        /* 自动重载寄存器ARR赋值,计数周期为PRS*(ARR+1)*TPCLK */
        // Gtim_Init(HC_GTIM0, &stcInitCfg);
        Gtim_Init(HC_GTIM1, &stcInitCfg);
        Gtim_Init(HC_GTIM2, &stcInitCfg);

        // RGB通道设置为反向PWM模式
        // Gtim_SetCompareCaptureMode(HC_GTIM0, GTIM_COMPARE_CAPTURE_CH0, GTIM_COMPARE_CAPTURE_PWM_INVERTED); // PWM61 - RGB通道
        Gtim_SetCompareCaptureMode(HC_GTIM2, GTIM_COMPARE_CAPTURE_CH2, GTIM_COMPARE_CAPTURE_PWM_INVERTED); // PWM62 - RGB通道
        Gtim_SetCompareCaptureMode(HC_GTIM2, GTIM_COMPARE_CAPTURE_CH3, GTIM_COMPARE_CAPTURE_PWM_INVERTED); // PWM63 - RGB通道

        // 其他通道使用正向PWM模式
        Gtim_SetCompareCaptureMode(HC_GTIM1, GTIM_COMPARE_CAPTURE_CH0, GTIM_COMPARE_CAPTURE_PWM_NORMAL);
        Gtim_SetCompareCaptureMode(HC_GTIM1, GTIM_COMPARE_CAPTURE_CH1, GTIM_COMPARE_CAPTURE_PWM_NORMAL);
        Gtim_SetCompareCaptureMode(HC_GTIM1, GTIM_COMPARE_CAPTURE_CH2, GTIM_COMPARE_CAPTURE_PWM_NORMAL);
        Gtim_SetCompareCaptureMode(HC_GTIM1, GTIM_COMPARE_CAPTURE_CH3, GTIM_COMPARE_CAPTURE_PWM_NORMAL);
        Gtim_SetCompareCaptureMode(HC_GTIM2, GTIM_COMPARE_CAPTURE_CH0, GTIM_COMPARE_CAPTURE_PWM_NORMAL);
        Gtim_SetCompareCaptureMode(HC_GTIM2, GTIM_COMPARE_CAPTURE_CH1, GTIM_COMPARE_CAPTURE_PWM_NORMAL);

        // Gtim_SetCompareCaptureReg(HC_GTIM0, GTIM_COMPARE_CAPTURE_CH0, DEFAULT_COMPVALUE);
        Gtim_SetCompareCaptureReg(HC_GTIM1, GTIM_COMPARE_CAPTURE_CH0, DEFAULT_COMPVALUE);
        Gtim_SetCompareCaptureReg(HC_GTIM1, GTIM_COMPARE_CAPTURE_CH1, DEFAULT_COMPVALUE);
        Gtim_SetCompareCaptureReg(HC_GTIM1, GTIM_COMPARE_CAPTURE_CH2, DEFAULT_COMPVALUE);
        Gtim_SetCompareCaptureReg(HC_GTIM1, GTIM_COMPARE_CAPTURE_CH3, DEFAULT_COMPVALUE);
        Gtim_SetCompareCaptureReg(HC_GTIM2, GTIM_COMPARE_CAPTURE_CH0, DEFAULT_COMPVALUE);
        Gtim_SetCompareCaptureReg(HC_GTIM2, GTIM_COMPARE_CAPTURE_CH1, DEFAULT_COMPVALUE);
        Gtim_SetCompareCaptureReg(HC_GTIM2, GTIM_COMPARE_CAPTURE_CH2, DEFAULT_COMPVALUE);
        Gtim_SetCompareCaptureReg(HC_GTIM2, GTIM_COMPARE_CAPTURE_CH3, DEFAULT_COMPVALUE);

        Gtim_Enable(HC_GTIM0); /* 启动gtim运行 */
        Gtim_Enable(HC_GTIM1); /* 启动gtim运行 */
        Gtim_Enable(HC_GTIM2); /* 启动gtim运行 */
    }
}

/**
 * @brief 设置PWM通道占空比
 *
 * @param port PWM通道
 * @param duty 占空比值0-100
 */
void bsp_SetPwmDuty(unsigned char port, unsigned short duty)
{
    uint16_t pwm_duty  = 0;
    uint16_t max_value = 0;

    if (duty > 100) {
        duty = 100; // 确保占空比不超过100%
    }

    if (port == PWM31 || port == PWM32 || port == PWM53 || port == PWM54) {
        // ATIM3通道使用特殊计算
        max_value = (PWM_PERIOD * 85 / 100) - 1;
        // 计算并确保结果不超过最大值
        uint32_t calc_duty = (duty * max_value + 50) / 100; // 添加50作为四舍五入补偿
        pwm_duty           = (uint16_t)(calc_duty > max_value ? max_value : calc_duty);
    } else if (port >= PWM61 && port <= PWM63) {
        if (duty == 0) {
            pwm_duty = PWM_PERIOD; // 完全关闭（100%占空比）
        } else if (duty == 100) {
            pwm_duty = 0; // 最大亮度（0%占空比）
        } else {
            // 限制最大亮度为80%
            uint8_t limited_duty = (duty > 80) ? 80 : duty;
            // 反向计算：100% - duty%
            pwm_duty = (PWM_PERIOD * (100 - limited_duty)) / 100;
        }
    } else {
        // 普通PWM通道
        max_value = PWM_PERIOD;
        // 计算并确保结果不超过最大值
        uint32_t calc_duty = (duty * max_value + 50) / 100; // 添加50作为四舍五入补偿
        pwm_duty           = (uint16_t)(calc_duty > max_value ? max_value : calc_duty);
    }

    switch (port) {
        case PWM11:
            Gtim_SetCompareCaptureReg(HC_GTIM1, GTIM_COMPARE_CAPTURE_CH0, pwm_duty);
            break;

        case PWM12:
            Gtim_SetCompareCaptureReg(HC_GTIM1, GTIM_COMPARE_CAPTURE_CH1, pwm_duty);
            break;

        case PWM21:
            Gtim_SetCompareCaptureReg(HC_GTIM1, GTIM_COMPARE_CAPTURE_CH2, pwm_duty);
            break;

        case PWM22:
            Gtim_SetCompareCaptureReg(HC_GTIM1, GTIM_COMPARE_CAPTURE_CH3, pwm_duty);
            break;

        case PWM31:
            ATIM3_Mode23_Channel_Compare_Value_Set(ATIM3_COMPARE_CAPTURE_CH0A, pwm_duty);
            break;

        case PWM32:
            ATIM3_Mode23_Channel_Compare_Value_Set(ATIM3_COMPARE_CAPTURE_CH1A, pwm_duty);
            break;

        case PWM41:
            ATIM3_Mode23_Channel_Compare_Value_Set(ATIM3_COMPARE_CAPTURE_CH2A, pwm_duty);
            break;

        case PWM42:
            ATIM3_Mode23_Channel_Compare_Value_Set(ATIM3_COMPARE_CAPTURE_CH0B, pwm_duty);
            break;

        case PWM51:
            ATIM3_Mode23_Channel_Compare_Value_Set(ATIM3_COMPARE_CAPTURE_CH1B, pwm_duty);
            break;

        case PWM52:
            ATIM3_Mode23_Channel_Compare_Value_Set(ATIM3_COMPARE_CAPTURE_CH2B, pwm_duty);
            break;

        case PWM53:
            Gtim_SetCompareCaptureReg(HC_GTIM2, GTIM_COMPARE_CAPTURE_CH0, pwm_duty);
            break;

        case PWM54:
            Gtim_SetCompareCaptureReg(HC_GTIM2, GTIM_COMPARE_CAPTURE_CH1, pwm_duty);
            break;

        case PWM61:
            g_soft_pwm61.duty   = pwm_duty; // 0~100线性占空比
            g_soft_pwm61.period = 100;      // 100步，1%分辨率
            break;

        case PWM62:
            Gtim_SetCompareCaptureReg(HC_GTIM2, GTIM_COMPARE_CAPTURE_CH2, pwm_duty);
            break;

        case PWM63:
            Gtim_SetCompareCaptureReg(HC_GTIM2, GTIM_COMPARE_CAPTURE_CH3, pwm_duty);
            break;

        default:
            break;
    }
}

/**
 * @brief 软件PWM初始化
 *
 */
static void pwm_init(void)
{
    // 确保所有PWM通道初始化为关闭状态
    bsp_SetPwmDuty(PWM11, 0);
    bsp_SetPwmDuty(PWM12, 0);
    bsp_SetPwmDuty(PWM21, 0);
    bsp_SetPwmDuty(PWM22, 0);
    bsp_SetPwmDuty(PWM51, 0);
    bsp_SetPwmDuty(PWM52, 0);
    bsp_SetPwmDuty(PWM41, 0);
    bsp_SetPwmDuty(PWM42, 0);

    // 震动电机通道明确设置为0占空比（关闭状态）
    bsp_SetPwmDuty(PWM31, 0);
    bsp_SetPwmDuty(PWM32, 0);
    bsp_SetPwmDuty(PWM53, 0);
    bsp_SetPwmDuty(PWM54, 0);

    // RGB通道设置为100（关闭状态，因为是反向PWM）
    bsp_SetPwmDuty(PWM61, 100);
    bsp_SetPwmDuty(PWM62, 100);
    bsp_SetPwmDuty(PWM63, 100);
}

/**
 * @brief 软件PWM更新
 *
 */
void soft_pwm_update(void)
{
    g_soft_pwm61.counter++;
    if (g_soft_pwm61.duty == 0) {
        GPIO_PD03_RESET(); // 低电平，灯亮
    } else if (g_soft_pwm61.duty > 0 && g_soft_pwm61.duty < g_soft_pwm61.period) {
        if (g_soft_pwm61.counter < g_soft_pwm61.duty) {
            GPIO_PD03_SET(); // 高电平，灯灭
        } else {
            GPIO_PD03_RESET(); // 低电平，灯亮
        }
    } else if (g_soft_pwm61.duty >= g_soft_pwm61.period) {
        GPIO_PD03_SET(); // 高电平，灯灭
    }
    if (g_soft_pwm61.counter >= g_soft_pwm61.period) {
        g_soft_pwm61.counter = 0;
    }
}

/**
 * @brief 硬件PWM初始化
 *
 */
void bsp_PwmInit(void)
{
    pwm_PortInit();
    pwm_Config();
    pwm_init();
}
