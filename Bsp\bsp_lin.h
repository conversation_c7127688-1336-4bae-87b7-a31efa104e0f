#ifndef __BSP_LIN_H__
#define __BSP_LIN_H__

#include "config.h"
#include "ringbuffer.h"

#define LIN_MASTER_USART HC_USART0 // 硬件USART实例
#define LIN_BREAK_LEN    18U       // LIN Break长度

#define LIN_READ_PID     0x60
#define LIN_WRITE_PID    0x61

/**
 * @brief LIN角色类型
 */
typedef enum {
    LIN_ROLE_MASTER = 0, // 主机模式
    LIN_ROLE_SLAVE       // 从机模式
} lin_role_t;

/**
 * @brief 初始化LIN接口（主/从模式）
 * @param baudrate LIN通信波特率
 * @param role LIN_ROLE_MASTER或LIN_ROLE_SLAVE
 */
void bsp_lin_init(uint32_t baudrate, lin_role_t role);

/**
 * @brief 主机发送LIN帧
 * @param id 帧ID
 * @param data 数据指针
 * @param len 数据长度
 * @return 0成功，<0失败
 */
int bsp_lin_master_send_frame(uint8_t id, const uint8_t *data, uint8_t len);

/**
 * @brief 主机读取LIN帧（阻塞/超时）
 * @param buf 接收数据缓冲区
 * @param len 期望读取长度
 * @param timeout_ms 超时时间（毫秒）
 * @return 实际读取长度
 */
int bsp_lin_master_read(uint8_t *buf, uint8_t len, uint32_t timeout_ms);

/**
 * @brief 从机接收主机请求（阻塞/超时）
 * @param id 指向接收ID的指针
 * @param data 数据缓冲区
 * @param len 实际接收长度
 * @param timeout_ms 超时时间（毫秒）
 * @return 1成功，0超时
 */
int bsp_lin_slave_receive(uint8_t *id, uint8_t *data, uint8_t *len, uint32_t timeout_ms);

/**
 * @brief 从机响应主机
 * @param id 帧ID
 * @param data 数据指针
 * @param len 数据长度
 * @return 0成功，<0失败
 */
int bsp_lin_slave_respond(uint8_t id, const uint8_t *data, uint8_t len);

/**
 * @brief 计算校验和（根据LIN协议，可以是经典校验和或增强校验和）
 *
 * @param id 消息ID（增强校验和需要）
 * @param data 数据数组
 * @param len 数据长度
 * @return 计算出的校验和
 */
uint8_t lin_calculate_checksum(uint8_t id, const uint8_t *data, uint8_t len);

#endif