/**
 * @file    bsp_spi.c
 * @brief   硬件SPI驱动实现
 * @version 1.0
 * @date    2023-10-01
 *
 * @copyright Copyright (c) 2025
 *
 */
#include "bsp_spi.h"

static volatile uint8_t s_spi_transfer_complete = 0; // 非阻塞传输状态

/**
 * @brief  SPI端口初始化
 * @param  None
 * @retval None
 */
static void spi_port_init(void)
{
    stc_gpio_init_t stcGpioInit = {0};

    /* 开启SPI相关IO的时钟 */
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpioc);
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpiod);

    /* 主机 SPI0_SCK(PC13)、SPI_MOSI(PC15)端口初始化，推挽输出 */
    stcGpioInit.u32Mode     = GPIO_MODE_OUTPUT_PP;
    stcGpioInit.u32Pin      = SPI_SCK_PIN | SPI_MOSI_PIN;
    stcGpioInit.u32PullUpDn = GPIO_PULL_NONE;
    GPIOC_Init(&stcGpioInit);

    /* 主机 SPI_MISO(PC14)端口初始化，上拉输入 */
    stcGpioInit.u32Mode     = GPIO_MODE_INPUT;
    stcGpioInit.u32Pin      = SPI_MISO_PIN;
    stcGpioInit.u32PullUpDn = GPIO_PULL_UP;
    GPIOC_Init(&stcGpioInit);

    /* 主机 SPI0_CS(PD00)端口初始化，推挽输出 */
    stcGpioInit.u32Mode     = GPIO_MODE_OUTPUT_PP;
    stcGpioInit.u32Pin      = SPI_CS_PIN;
    stcGpioInit.u32PullUpDn = GPIO_PULL_NONE;
    GPIOD_Init(&stcGpioInit);

    /* 开启复用功能 */
    GPIO_PC13_AF_SPI0_SCK_SET();
    GPIO_PC15_AF_SPI0_MOSI_SET();
    GPIO_PC14_AF_SPI0_MISO_SET();
    GPIO_PD00_AF_SPI0_CS_SET();
}

/**
 * @brief  配置DMA通道（全双工安全实现）
 * @param  tx_buf: 发送缓冲区
 * @param  rx_buf: 接收缓冲区
 * @param  len: 传输长度
 * @retval None
 */
static void spi_dma_config(const uint8_t *tx_buf, uint8_t *rx_buf, uint16_t len)
{
    stc_dmac_channel_init_t stcDmacChInit = {0};
    static uint8_t dummy_tx = 0xFF;
    static uint8_t dummy_rx;

    /* 打开DMA时钟 */
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralDma);

    /* 配置发送DMA通道（无tx_buf时用dummy） */
    stcDmacChInit.u32Priority      = DMA_FIX_PRIORITY;
    stcDmacChInit.u32TrigSrc       = DMA_TRISRC_SPI0_TXE;
    stcDmacChInit.u32BlockCnt      = 1;
    stcDmacChInit.u32TransferCnt   = len;
    stcDmacChInit.u32TransferMode  = DMA_BLOCK_MODE;
    stcDmacChInit.u32DataSize      = DMA_DATA_BYTE;
    stcDmacChInit.u32SrcAddrFix    = DMA_SRCADDR_INC;
    stcDmacChInit.u32DstAddrFix    = DMA_DSTADDR_FIX;
    stcDmacChInit.u32BTCntReload   = DMA_BTCNT_RELOAD_ENABLE;
    stcDmacChInit.u32SrcAddrReload = DMA_SRCADDR_RELOAD_ENABLE;
    stcDmacChInit.u32DstAddrReload = DMA_DSTADDR_RELOAD_ENABLE;
    stcDmacChInit.u32SrcAddr       = (uint32_t)(tx_buf ? tx_buf : &dummy_tx);
    stcDmacChInit.u32DstAddr       = (uint32_t)(&(HC_SPI0->DR));
    stcDmacChInit.u32ChanelEnMsk   = DMA_AUTO_DISABLE;
    DMACCH_Init(HC_DMAC, SPI_DMA_TX_CHANNEL, &stcDmacChInit);
    DMACCH_EnableIrq(HC_DMAC, DMA_IRQ_TC, SPI_DMA_TX_CHANNEL);
    EnableNvic(DMA_CH2_IRQn, IrqLevel3, TRUE);

    /* 配置接收DMA通道（无rx_buf时用dummy） */
    stcDmacChInit.u32Priority      = DMA_FIX_PRIORITY;
    stcDmacChInit.u32TrigSrc       = DMA_TRISRC_SPI0_RXNE;
    stcDmacChInit.u32BlockCnt      = 1;
    stcDmacChInit.u32TransferCnt   = len;
    stcDmacChInit.u32TransferMode  = DMA_BLOCK_MODE;
    stcDmacChInit.u32DataSize      = DMA_DATA_BYTE;
    stcDmacChInit.u32SrcAddrFix    = DMA_SRCADDR_FIX;
    stcDmacChInit.u32DstAddrFix    = DMA_DSTADDR_INC;
    stcDmacChInit.u32BTCntReload   = DMA_BTCNT_RELOAD_ENABLE;
    stcDmacChInit.u32SrcAddrReload = DMA_SRCADDR_RELOAD_ENABLE;
    stcDmacChInit.u32DstAddrReload = DMA_DSTADDR_RELOAD_ENABLE;
    stcDmacChInit.u32SrcAddr       = (uint32_t)(&(HC_SPI0->DR));
    stcDmacChInit.u32DstAddr       = (uint32_t)(rx_buf ? rx_buf : &dummy_rx);
    stcDmacChInit.u32ChanelEnMsk   = DMA_AUTO_DISABLE;
    DMACCH_Init(HC_DMAC, SPI_DMA_RX_CHANNEL, &stcDmacChInit);
    DMACCH_EnableIrq(HC_DMAC, DMA_IRQ_TC, SPI_DMA_RX_CHANNEL);
    EnableNvic(DMA_CH3_IRQn, IrqLevel3, TRUE);

    /* 先使能RX，再使能TX，保证不丢首字节 */
    DMACCH_Enable(HC_DMAC, SPI_DMA_RX_CHANNEL);
    DMACCH_Enable(HC_DMAC, SPI_DMA_TX_CHANNEL);

    /* 使能DMAC */
    DMAC_Enable();
}

/**
 * @brief  SPI初始化
 * @param  None
 * @retval None
 */
void bsp_SpiInit(void)
{
    stc_spi_init_t SpiInitStruct = {0};

    /* 初始化SPI端口 */
    spi_port_init();

    /* 开启SPI0时钟门控 */
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralSpi0);

    /* 复位SPI0模块 */
    SYSCTRL_PeriphReset(SysctrlPeripheralSpi0);

    /* SPI0模块配置 */
    SpiInitStruct.u32Mode              = SPI_MODE_MASTER;               // 主模式
    SpiInitStruct.u32TransferDirection = SPI_FULL_DUPLEX;               // 全双工
    SpiInitStruct.u32BaudRate          = SPI_BAUDRATE_PCLK_DIV8;        // 降低速率，提高稳定性
    SpiInitStruct.u32CPOL              = SPI_CLOCK_POLARITY_LOW;        // 时钟极性低
    SpiInitStruct.u32CPHA              = SPI_CLOCK_PHASE_1EDGE;         // 时钟相位1边沿
    SpiInitStruct.u32DataWidth         = SPI_DATAWIDTH_8BIT;            // 数据宽度8位
    SpiInitStruct.u32BitOrder          = SPI_MSB_FIRST;                 // 数据传输顺序MSB
    SpiInitStruct.u32NSS               = SPI_NSS_HARD_OUTPUT;           // 片选信号输出
    SpiInitStruct.u32SampleDelay       = SPI_SAMPLE_DELAY;              // 采样延迟
    SpiInitStruct.u32DMAReq            = SPI_DMA_TRANSFER_RX_TX_ENABLE; // 使能DMA请求

    /* 初始化SPI */
    Spi_Init(HC_SPI0, &SpiInitStruct);

    /* 清除所有中断标志位 */
    SPI_ClearFlag_ALL(HC_SPI0);

    /* 使能SPI */
    SPI_Enable(HC_SPI0);
}

/**
 * @brief  SPI DMA传输
 * @param  tx_buf: 发送缓冲区
 * @param  rx_buf: 接收缓冲区
 * @param  len: 传输长度
 * @retval 0=成功, 1=失败
 */
uint8_t bsp_SpiTransfer_DMA(const uint8_t *tx_buf, uint8_t *rx_buf, uint16_t len)
{
    uint32_t timeout = 0;
    /* 参数检查 */
    if (len == 0) {
        return SPI_ERROR;
    }

    /* 确保上一次传输已完成 */
    if (SPI_IsActiveFlag(HC_SPI0, SPI_FLAG_BUSY)) {
        return SPI_ERROR;
    }

    s_spi_transfer_complete = 0;

    /* 禁用DMA通道 */
    DMACCH_Disable(HC_DMAC, SPI_DMA_TX_CHANNEL);
    DMACCH_Disable(HC_DMAC, SPI_DMA_RX_CHANNEL);

    /* 清除DMA状态 */
    DMACCH_ClrStatus(HC_DMAC, SPI_DMA_TX_CHANNEL);
    DMACCH_ClrStatus(HC_DMAC, SPI_DMA_RX_CHANNEL);

    /* 配置DMA通道 */
    spi_dma_config(tx_buf, rx_buf, len);

    /* 等待传输完成 */
    while (!s_spi_transfer_complete) {
        if (++timeout > SPI_TIMEOUT) {
            return SPI_ERROR;
        }
        // vTaskDelay(pdTICKS_TO_MS(1));
    }

    return SPI_OK;
}

/**
 * @brief DMA发送完成中断处理函数
 */
void Dma_Ch2_IRQHandler(void)
{
    if (DMA_STATUS_FINISHED == DMACCH_GetStatus(HC_DMAC, SPI_DMA_TX_CHANNEL)) {
        DMACCH_ClrStatus(HC_DMAC, SPI_DMA_TX_CHANNEL);
        DMACCH_Disable(HC_DMAC, SPI_DMA_TX_CHANNEL); /* 禁用发送通道 */
    }
}

/**
 * @brief DMA接收完成中断处理函数
 */
void Dma_Ch3_IRQHandler(void)
{
    if (DMA_STATUS_FINISHED == DMACCH_GetStatus(HC_DMAC, SPI_DMA_RX_CHANNEL)) {
        DMACCH_ClrStatus(HC_DMAC, SPI_DMA_RX_CHANNEL);
        DMACCH_Disable(HC_DMAC, SPI_DMA_RX_CHANNEL); /* 禁用接收通道 */

        s_spi_transfer_complete = 1;
    }
}

/**
 * @brief  SPI传输函数
 * @param  tx_buf: 发送数据缓冲区
 * @param  rx_buf: 接收数据缓冲区
 * @param  len: 传输长度
 * @retval 0=成功, 1=失败
 */
uint8_t bsp_SpiTransfer(const uint8_t *tx_buf, uint8_t *rx_buf, uint16_t len)
{
    uint16_t i;
    uint32_t timeout;
    uint8_t tx_byte, rx_byte;

    /* 参数检查 */
    if (len == 0) {
        return SPI_ERROR;
    }

    /* 传输数据 */
    for (i = 0; i < len; i++) {
        /* 准备发送的数据 */
        tx_byte = (tx_buf != NULL) ? tx_buf[i] : 0xFF;

        /* 等待发送缓冲区为空 */
        timeout = 0;
        while (!SPI_IsActiveFlag(HC_SPI0, SPI_FLAG_TXE)) {
            if (++timeout > SPI_TIMEOUT) {
                return SPI_ERROR;
            }
        }

        /* 发送数据 */
        Spi_SendData(HC_SPI0, tx_byte);

        /* 等待接收完成 */
        timeout = 0;
        while (!SPI_IsActiveFlag(HC_SPI0, SPI_FLAG_RXNE)) {
            if (++timeout > SPI_TIMEOUT) {
                return SPI_ERROR;
            }
        }

        /* 读取接收到的数据 */
        rx_byte = Spi_ReceiveData(HC_SPI0);
        if (rx_buf != NULL) {
            rx_buf[i] = rx_byte;
        }
    }

    /* 等待SPI不忙 */
    timeout = 0;
    while (SPI_IsActiveFlag(HC_SPI0, SPI_FLAG_BUSY)) {
        if (++timeout > SPI_TIMEOUT) {
            return SPI_ERROR;
        }
    }

    return SPI_OK;
}

/**
 * @brief  设置SPI片选信号
 * @param  state: 0=低电平(选中), 1=高电平(未选中)
 * @retval None
 */
void bsp_SpiSetCS(uint8_t state)
{
    if (state == 0) {
        /* 拉低片选信号 */
        SPI_MasterNSSOutput(HC_SPI0, SPI_NSS_CONFIG_ENABLE);
    } else {
        /* 拉高片选信号 */
        SPI_MasterNSSOutput(HC_SPI0, SPI_NSS_CONFIG_DISABLE);
    }
}
