---
description: 
globs: 
alwaysApply: true
---
# 项目结构与FreeRTOS开发说明

本项目为基于FreeRTOS的嵌入式软件工程，主要目录结构如下：

- [App/](mdc:App): 应用层代码，包含主应用逻辑、FreeRTOS任务(Task)实现、应用事件处理等。
- [Bsp/](mdc:Bsp): 板级支持包（Board Support Package），包括各类硬件驱动（如buzzer、spi、adc、usart等）和相关头文件。
  - [Bsp/buzzer/](mdc:Bsp/buzzer): 蜂鸣器相关驱动及音乐播放实现。
- [Components/](mdc:Components): 组件库，包含AT命令、FAL抽象层、日志、环形缓冲区、SPI Flash等模块。
  - [Components/AT-Command/](mdc:Components/AT-Command): AT命令解析与通信相关代码。
  - [Components/fal/](mdc:Components/fal): Flash抽象层，支持多种Flash设备。
  - [Components/log/](mdc:Components/log): 日志系统实现。
  - [Components/ringbuffer/](mdc:Components/ringbuffer): 环形缓冲区实现。
  - [Components/sfud/](mdc:Components/sfud): SPI Flash通用驱动。
- [FWLib/](mdc:FWLib): 芯片底层库，包括CMSIS、外设驱动、启动文件等。
  - [FWLib/driver/](mdc:FWLib/driver): 芯片外设驱动源码和头文件。
  - [FWLib/CMSIS/](mdc:FWLib/CMSIS): ARM CMSIS标准库。
- [FreeRTOS/](mdc:FreeRTOS): FreeRTOS操作系统内核、移植层及相关配置文件（如FreeRTOSConfig.h、portable/等）。
- [User/](mdc:User): 用户自定义代码和配置（如main.c、config.h等）。
- [build/](mdc:build): 编译输出目录。
- [.vscode/](mdc:.vscode): VSCode工程配置。
- [.eide/](mdc:.eide): EIDE工程配置。

主入口和核心功能一般在[App/](mdc:App)和[User/](mdc:User)目录下实现，硬件相关代码集中在[Bsp/](mdc:Bsp)和[FWLib/](mdc:FWLib)目录，通用组件和中间件在[Components/](mdc:Components)目录。FreeRTOS内核及其移植相关内容在[FreeRTOS/](mdc:FreeRTOS)目录，所有任务、队列、信号量等RTOS对象建议集中管理和注释。

---

## FreeRTOS开发建议
- 所有任务（Task）建议以`xxx_task.c/h`命名，任务入口函数以`xxx_task_entry`命名。
- 任务间通信优先使用FreeRTOS队列、信号量、事件组等机制，避免直接全局变量共享。
- 中断服务程序（ISR）中如需与任务交互，优先使用`FromISR`系列API。
- 配置文件如`FreeRTOSConfig.h`应根据MCU资源和应用需求合理配置。
- 建议所有RTOS对象（任务、队列、信号量等）统一在专用头文件声明，便于管理。
- 任务堆栈大小、优先级等参数需结合实际应用和MCU资源评估设置。

---

后续可补充具体编程规范（如命名、注释、代码风格等），以提升团队协作和代码可维护性。
