---
description: 
globs: 
alwaysApply: true
---
# FreeRTOS嵌入式项目开发与编码统一规范




---

## 1. 项目结构与目录约定
- 应用层：[App/]，主应用逻辑、FreeRTOS任务实现、事件处理。
- 板级支持包：[Bsp/]，各类硬件驱动（如buzzer、spi、adc、usart等）。
- 组件库：[Components/]，如AT命令、FAL抽象层、日志、环形缓冲区、SPI Flash等。
- 芯片底层库：[FWLib/]，CMSIS、外设驱动、启动文件等。
- FreeRTOS内核：[FreeRTOS/]，内核、移植层、配置文件。
- 用户自定义：[User/]，如main.c、config.h等。
- 编译输出：[build/]。
- 工程配置：[.vscode/]、[.eide/]。




---

## 2. 命名规范
- 任务文件：`xxx_task.c`/`xxx_task.h`
- 驱动文件：`bsp_xxx.c`/`bsp_xxx.h`
- 组件文件：`xxx.c`/`xxx.h`
- 组件目录结构：每组件独立目录，含`inc/`（头文件）、`src/`（源码）、`port/`（移植层）、`tests/`（测试）
- 全局变量：`g_`前缀，静态变量：`s_`前缀
- 任务句柄：`TaskHandle_t xxx_task_handle`
- 队列、信号量、事件组等RTOS对象：加类型后缀（如`xxx_queue`、`xxx_sem`、`xxx_evt`）
- 任务入口函数：`void xxx_task_entry(void *pvParameters)`
- 驱动接口：`bsp_xxx_init()`、`bsp_xxx_write()`等
- 组件接口：`log_info()`、`ringbuffer_put()`等




---

## 3. 代码风格
- 缩进4空格，K&R大括号风格
- 每行不超120字符，适当换行
- 头文件需有防重复包含宏
- 禁止魔法数字，常量用`#define`或`const`定义
- 结构体封装硬件寄存器映射，避免魔法数字
- 组件对外API统一在`inc/`头文件声明，禁止应用层直接包含`src/`文件




---

## 4. 注释与文档
- 文件头注释：功能、作者、日期、版本
- 函数头注释：功能、参数、返回值、注意事项
- 关键逻辑、硬件操作、RTOS对象创建与使用处有详细注释
- 推荐中英文结合，硬件寄存器操作附寄存器/位定义说明
- 所有对外API有英文注释，说明参数、返回值、边界条件
- 复杂算法、状态机需有流程说明或流程图
- 公共接口、复杂算法、状态机、关键参数、模块间接口有详细文档
- 设计文档、测试报告、用户手册需及时更新




---

## 5. FreeRTOS对象管理
- 所有任务、队列、信号量、事件组等对象统一在专用头文件声明（如`app_freertos.h`）
- 对象命名带类型后缀
- 创建、删除、使用流程有详细注释，说明用途、优先级、堆栈等参数
- 禁止在中断中直接创建/删除RTOS对象
- 任务间通信优先用队列、事件组，避免全局变量
- ISR与任务交互用`FromISR`后缀API
- 对象使用前检查有效性，防止空指针等异常
- 任务优先级、堆栈大小结合实际评估，避免资源浪费或溢出
- 任务退出需有清理机制，防止资源泄漏




---

## 6. 硬件抽象与驱动
- 所有硬件相关操作封装在Bsp目录，禁止应用层直接操作寄存器
- 每类外设独立驱动文件（如`bsp_uart.c/h`、`bsp_gpio.c/h`）
- 驱动接口提供初始化、读写、控制等标准函数，禁止暴露底层细节
- 所有寄存器操作有详细注释，注明寄存器名、位定义、操作目的
- 驱动需考虑多平台兼容，推荐通过宏或配置文件切换不同MCU/外设实现
- 驱动中断回调在Bsp层实现，应用层通过回调或消息机制获取事件




---

## 7. 组件开发与移植
- 组件初始化函数命名为`xxx_init()`，资源释放为`xxx_deinit()`
- 组件内部静态变量加`s_`前缀，全局变量加`g_`前缀，禁止全局变量泄漏
- 平台相关代码集中在`port/`目录，移植时仅需修改此处
- 移植层有详细注释，说明与平台的接口关系
- 每个组件必须有独立测试用例，测试代码放在`tests/`目录，覆盖正常、异常、边界情况




---

## 8. 日志与调试
- 日志分级（如DEBUG/INFO/WARN/ERROR），关键操作、错误必须记录日志
- 日志包含时间戳、模块、级别、内容
- 正式版本可通过宏控制日志级别，避免影响性能
- 日志接口统一为`log_xxx()`，如`log_info()`、`log_error()`
- 日志缓冲区有溢出保护，避免死循环或阻塞
- 预留调试串口，支持命令行接口
- 关键变量可通过调试命令查询和修改
- 测试接口有开关，发布版本默认关闭




---

## 9. 安全性与内存
- 禁止中断中动态内存分配，ISR变量需volatile
- 指针使用前检查有效性，数组访问有边界检查
- 避免递归，合理规划栈空间
- 所有函数返回值必须检查，错误需日志记录，关键数据需CRC校验




---

## 10. 代码修改与开发流程
- 所有功能性修改必须先绘制流程图，包含起始点、终止点、决策节点、处理步骤、数据流向、接口点、错误处理、关键变量状态变化
- 流程图需注明版本号、作者、日期、修改原因、涉及模块、影响范围
- 修改流程包括：需求分析、设计（含流程图）、实现（严格按流程图）、验证（对照流程图）、文档更新
- 代码评审需对照流程图和设计文档进行，重大变更需评审，自测通过后提交




---

## 11. 流程图规范
- 使用标准流程图符号（椭圆、矩形、菱形、平行四边形、箭头）
- 推荐Mermaid格式，示例：
```mermaid
graph TD
    A[开始] --> B{判断条件}
    B -->|条件1| C[处理1]
    B -->|条件2| D[处理2]
    C --> E[错误处理]
    D --> F[正常处理]
    E --> G[结束]
    F --> G
```




---

## 12. 测试流程与用例
- 关键模块、组件必须有单元测试，测试代码与实现分离
- 测试用例覆盖正常、异常、边界情况，测试结果可追溯
- 系统测试包括负载、异常、启动、复位、掉电保护、长期稳定性等
- 系统测试用例需文档化，结果需可追溯
- 回归测试建议自动化，测试脚本需有注释说明
- 每次重要提交需执行回归测试，保证功能不回退
- 测试报告需包含测试环境、用例、结果、问题记录
- 测试用例、结果需归档，便于后续追溯




---

## 13. 版本控制与提交
- 提交信息清晰描述改动，重要改动有相关测试用例
- 版本号遵循语义化规范，每个版本有更新说明
- 实验性功能在单独分支开发




---

## 14. 状态机设计与事件处理
- 状态机类型命名：`XXX_StateMachine_T`，所有状态机结构体、变量、函数需有统一前缀
- 状态处理函数命名：`XXX_handler_STATE`，如`motor_handler_RUNNING`
- 事件类型命名：`XXX_Event_T`，事件信号从`USER_SIG`开始定义，枚举成员全大写下划线分隔
- 状态机优先级、入口、退出、切换逻辑需有详细注释和流程图
- 状态处理函数要简短，避免耗时操作，复杂逻辑建议拆分子函数
- 事件处理函数需有参数范围检查，避免事件丢失，事件队列长度合理设计
- 状态机、事件相关代码需有状态转换图、流程说明，便于维护和扩展
- 状态机单元测试需覆盖所有状态、事件、边界和异常情况




---

## 15. 功耗优化
- 合理使用MCU睡眠/低功耗模式，任务空闲时进入低功耗
- 未使用外设及时关闭，外设驱动需提供`bsp_xxx_suspend()`/`bsp_xxx_resume()`等接口
- 避免忙等待，优先采用事件驱动、信号量、队列等机制
- CPU频率可动态调整，需有切换保护和日志记录
- 唤醒条件设计合理，避免误唤醒和功耗抖动
- 功耗相关代码需有详细注释，说明功耗优化点和注意事项
- 功耗测试用例需覆盖典型场景，测试报告需记录功耗数据和优化效果




---

## 16. 通讯协议设计与实现
- 通讯协议相关文件建议统一放在`Components/`或专用目录下，命名如`protocol_xxx.c/h`
- 协议帧结构、命令字、校验等常量统一用`#define`或`enum`定义，禁止魔法数字
- 协议解析、组包、校验等功能建议分函数实现，函数命名如`protocol_xxx_parse()`、`protocol_xxx_pack()`、`protocol_xxx_check_crc()`
- 协议状态机建议采用结构体+函数指针实现，便于扩展和维护
- 所有协议相关函数需有详细注释，说明输入、输出、边界条件、异常处理
- 通讯缓冲区需有溢出保护，边界检查，避免内存越界
- 协议超时、重发、错误帧等异常需有详细处理逻辑和日志记录
- 协议相关测试用例需覆盖正常、异常、边界情况，测试代码与实现分离




---

## 17. 异常处理与恢复
- 所有函数返回值必须检查，关键操作需有错误码定义，统一用`enum`或`#define`管理
- 关键路径、外设操作、协议处理等需有超时、重试、降级等机制
- 错误发生时需记录详细日志，日志内容包括时间、模块、错误码、上下文信息
- 重要数据需有CRC或其他校验机制，发现异常时应有恢复或保护措施
- 系统级异常（如堆栈溢出、内存不足、看门狗超时等）需有统一处理入口，建议实现`vApplicationStackOverflowHook`、`vApplicationMallocFailedHook`等FreeRTOS钩子函数
- 异常恢复流程需有详细注释和流程图，关键恢复路径需单元测试覆盖
- 异常处理相关代码需有详细文档，便于维护和追溯




