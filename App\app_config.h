#ifndef __APP_CONFIG_H__
#define __APP_CONFIG_H__

#define MONITOR_ENABLE 0

// 任务优先级从低到高
#define APP_MONITOR_PRIO            (tskIDLE_PRIORITY + 1)
#define APP_PARAM_TASK_PRIO         (tskIDLE_PRIORITY + 1)
#define APP_LED_PRIO                (tskIDLE_PRIORITY + 2)
#define APP_RGB_PRIO                (tskIDLE_PRIORITY + 2)
#define APP_VIBR_PRIO               (tskIDLE_PRIORITY + 2)
#define APP_MOTOR_PRIO              (tskIDLE_PRIORITY + 2)
#define APP_BUZZER_PRIO             (tskIDLE_PRIORITY + 2)
#define APP_MSG_PRIO                (tskIDLE_PRIORITY + 3)
#define APP_LIN_PRIO                (tskIDLE_PRIORITY + 4)
#define APP_BLE_PRIO                (tskIDLE_PRIORITY + 4)
#define APP_WIFI_PRIO               (tskIDLE_PRIORITY + 4)
#define APP_APP_PRIO                (tskIDLE_PRIORITY + 5)

// 任务堆栈大小
#define APP_MSG_TASK_STACK_SIZE     (96)
#define APP_MONITOR_TASK_STACK_SIZE (128)
#define APP_LIN_TASK_STACK_SIZE     (196)
#define APP_PARAM_TASK_STACK_SIZE   (256)
#define APP_BLE_TASK_STACK_SIZE     (80)
#define APP_BUZZER_TASK_STACK_SIZE  (64)
#define APP_LED_TASK_STACK_SIZE     (64)
#define APP_RGB_TASK_STACK_SIZE     (64)
#define APP_VIBR_TASK_STACK_SIZE    (64)
#define APP_WIFI_TASK_STACK_SIZE    (128)
#define APP_MOTOR_TASK_STACK_SIZE   (164)
#define APP_APP_TASK_STACK_SIZE     (512)

#endif
