/**
 * @file bsp_radar.c
 * <AUTHOR> (<EMAIL>)
 * @brief radar驱动
 * @version 0.1
 * @date 2024-09-11
 *
 * @copyright Copyright (c) 2024
 *
 */
#include "bsp_radar.h"

static void radar_PortInit(void)
{
    stc_gpio_init_t stcGpioInit;

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpiob); /* GPIOB外设时钟使能 */
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpioc); /* GPIOC外设时钟使能 */

    /* 端口初始化 */
    DDL_ZERO_STRUCT(stcGpioInit); /* 结构体初始化清零         */

    stcGpioInit.u32Mode     = GPIO_MODE_INPUT; /* 端口方向配置->输入       */
    stcGpioInit.u32PullUpDn = GPIO_PULL_NONE;  /* 端口上下拉配置->无上下拉  */

    stcGpioInit.u32Pin = RADAR1_PIN;      /* 端口引脚 */
    GPIO_Init(RADAR1_PORT, &stcGpioInit); /* 端口初始化 */

    stcGpioInit.u32Pin = RADAR2_PIN;      /* 端口引脚 */
    GPIO_Init(RADAR2_PORT, &stcGpioInit); /* 端口初始化 */

    stcGpioInit.u32Pin = RADAR3_PIN;      /* 端口引脚 */
    GPIO_Init(RADAR3_PORT, &stcGpioInit); /* 端口初始化 */
}

/**
 * @brief 雷达检测初始化
 *
 */
void bsp_RadarInit(void)
{
    radar_PortInit();
}