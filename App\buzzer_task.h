/**
 * @file buzzer_task.h
 * @brief FreeRTOS蜂鸣器任务头文件
 * @version V1.0
 * <AUTHOR>
 * @date 2024-06-09
 * @note 迁移自buzzer_ao.h，适配FreeRTOS
 */
#ifndef __BUZZER_TASK_H__
#define __BUZZER_TASK_H__

#include "app_config.h"
#include "app_log.h"
#include "buzzer_music.h"

/**
 * @brief 蜂鸣器播放模式定义
 */
typedef enum {
    BUZZER_MODE_ONCE,     ///< 播放一次
    BUZZER_MODE_REPEAT,   ///< 循环播放
    BUZZER_MODE_SEQUENCE, ///< 序列播放（预留）
    BUZZER_MODE_RANDOM    ///< 随机播放（预留）
} buzzer_play_mode_t;

// ======================== 消息类型与结构体定义 ========================

/**
 * @brief 蜂鸣器任务消息类型
 */
typedef enum {
    BUZZER_MSG_PLAY_MUSIC,    // 播放预定义音乐
    BUZZER_MSG_PLAY_MUSIC_EX, // 扩展参数播放预定义音乐
    BUZZER_MSG_PLAY_CHORD,    // 播放自定义和弦
    BUZZER_MSG_PLAY_CHORD_EX, // 扩展参数播放自定义和弦
    BUZZER_MSG_STOP,          // 停止播放
    BUZZER_MSG_PAUSE,         // 暂停播放
    BUZZER_MSG_RESUME,        // 恢复播放
    BUZZER_MSG_SET_VOLUME,    // 设置音量
    BUZZER_MSG_BEEP           // 播放单音
} buzzer_msg_type_t;

/**
 * @brief 蜂鸣器任务消息结构体
 */
typedef struct {
    buzzer_msg_type_t type; // 消息类型
    union {
        struct {
            buzzer_music_type_t music_type; // 预定义音乐类型
            buzzer_play_mode_t mode;        // 播放模式
            uint16_t repeat_interval;       // 每次重复播放间隔(ms)
            uint8_t repeat;                 // 重复次数
        } play_music;
        struct {
            const buzzer_note_t *notes; // 自定义音符序列
            buzzer_play_mode_t mode;    // 播放模式
            uint8_t count;              // 音符数量
            uint8_t repeat;             // 重复次数
        } play_chord;
        struct {
            uint16_t freq;    // 单音频率
            uint16_t time_ms; // 单音时长
        } beep;
        uint8_t volume; // 音量(0-100)
    } data;
} buzzer_msg_t;

// ======================== 宏定义与静态变量 ========================

#define BUZZER_QUEUE_LEN       8   // 消息队列长度
#define BUZZER_MAX_CHORD_NOTES 8   // 最大和弦音符数
#define BUZZER_NOTE_GAP_MS     10U // 音符间隔(ms)
#define BUZZER_TICK_MS         5U // 任务调度周期(ms)
#define BUZZER_DEFAULT_ATTACK  5U  // 默认起音时间(ms)
#define BUZZER_DEFAULT_DECAY   50U // 默认衰减时间(ms)
#define BUZZER_DEFAULT_SUSTAIN 80U // 默认持续音量(%)
#define BUZZER_DEFAULT_RELEASE 22U // 默认释放时间(ms)

/**
 * @brief 初始化蜂鸣器任务（需在app_init中调用）
 */
void buzzer_task_init(void);

/**
 * @brief 播放预定义音乐
 * @param type 预定义音乐类型
 */
void Buzzer_PlayMusic(buzzer_music_type_t type);

/**
 * @brief 扩展参数播放预定义音乐
 * @param type 预定义音乐类型
 * @param mode 播放模式
 * @param repeat 重复次数
 * @param repeat_interval_ms 每次重复播放间隔(ms)
 */
void Buzzer_PlayMusicEx(buzzer_music_type_t type, buzzer_play_mode_t mode, uint8_t repeat, uint16_t repeat_interval_ms);

/**
 * @brief 播放自定义和弦
 * @param notes 音符序列指针
 * @param count 音符数量
 */
void Buzzer_PlayChord(const buzzer_note_t *notes, uint8_t count);

/**
 * @brief 扩展参数播放自定义和弦
 * @param notes 音符序列指针
 * @param count 音符数量
 * @param mode 播放模式
 * @param repeat 重复次数
 */
void Buzzer_PlayChordEx(const buzzer_note_t *notes, uint8_t count, buzzer_play_mode_t mode, uint8_t repeat);

/**
 * @brief 停止播放
 */
void Buzzer_Stop(void);

/**
 * @brief 暂停播放
 */
void Buzzer_Pause(void);

/**
 * @brief 恢复播放
 */
void Buzzer_Resume(void);

/**
 * @brief 设置音量(0-100)
 * @param volume 音量百分比
 */
void Buzzer_SetVolume(uint8_t volume);

/**
 * @brief 获取播放状态
 * @return 1:正在播放 0:未播放
 */
uint8_t Buzzer_IsPlaying(void);

/**
 * @brief 获取暂停状态
 * @return 1:已暂停 0:未暂停
 */
uint8_t Buzzer_IsPaused(void);

/**
 * @brief 播放单音beep
 * @param freq 频率(Hz)
 * @param time_ms 持续时间(ms)
 */
void Buzzer_Beep(uint16_t freq, uint16_t time_ms);

// ======================== 辅助宏定义 - 快速播放预定义音乐 ========================
///< 播放开机音效
#define BUZZER_PLAY_POWER_ON()        Buzzer_PlayMusic(BUZZER_MUSIC_POWER_ON)
///< 播放提示音
#define BUZZER_PLAY_ALERT()           Buzzer_PlayMusic(BUZZER_MUSIC_ALERT)
///< 播放成功音
#define BUZZER_PLAY_SUCCESS()         Buzzer_PlayMusic(BUZZER_MUSIC_SUCCESS)
///< 播放错误音
#define BUZZER_PLAY_ERROR()           Buzzer_PlayMusic(BUZZER_MUSIC_ERROR)
///< 播放按键点击音
#define BUZZER_PLAY_KEY_CLICK()       Buzzer_PlayMusic(BUZZER_MUSIC_KEY_CLICK)
///< 播放按键长按音
#define BUZZER_PLAY_KEY_LONG()        Buzzer_PlayMusic(BUZZER_MUSIC_KEY_LONG)
///< 播放按键释放音
#define BUZZER_PLAY_KEY_RELEASE()     Buzzer_PlayMusic(BUZZER_MUSIC_KEY_RELEASE)
///< 播放门铃音效
#define BUZZER_PLAY_DOORBELL()        Buzzer_PlayMusic(BUZZER_MUSIC_DOORBELL)
///< 播放消息提醒
#define BUZZER_PLAY_NOTIFY()          Buzzer_PlayMusic(BUZZER_MUSIC_NOTIFY)
///< 播放设备启动音效
#define BUZZER_PLAY_STARTUP()         Buzzer_PlayMusic(BUZZER_MUSIC_STARTUP)
///< 播放设备关闭音效
#define BUZZER_PLAY_SHUTDOWN()        Buzzer_PlayMusic(BUZZER_MUSIC_SHUTDOWN)
///< 播放模式切换音效
#define BUZZER_PLAY_MODE_SWITCH()     Buzzer_PlayMusic(BUZZER_MUSIC_MODE_SWITCH)
///< 播放场景触发音效
#define BUZZER_PLAY_SCENE()           Buzzer_PlayMusic(BUZZER_MUSIC_SCENE)
///< 播放WiFi开始配网音效
#define BUZZER_PLAY_WIFI_START()      Buzzer_PlayMusic(BUZZER_MUSIC_WIFI_START)
///< 播放WiFi结束配网音效
#define BUZZER_PLAY_WIFI_END()        Buzzer_PlayMusic(BUZZER_MUSIC_WIFI_END)
///< 播放WiFi搜索网络音效
#define BUZZER_PLAY_WIFI_SCAN()       Buzzer_PlayMusic(BUZZER_MUSIC_WIFI_SCAN)
///< 播放WiFi连接中音效
#define BUZZER_PLAY_WIFI_CONNECTING() Buzzer_PlayMusic(BUZZER_MUSIC_WIFI_CONNECTING)
///< 播放WiFi连接成功音效
#define BUZZER_PLAY_WIFI_CONNECTED()  Buzzer_PlayMusic(BUZZER_MUSIC_WIFI_CONNECTED)
///< 播放WiFi连接失败音效
#define BUZZER_PLAY_WIFI_FAILED()     Buzzer_PlayMusic(BUZZER_MUSIC_WIFI_FAILED)
///< 播放遥控器配对开始音效
#define BUZZER_PLAY_REMOTE_START()    Buzzer_PlayMusic(BUZZER_MUSIC_REMOTE_START)
///< 播放遥控器配对中音效
#define BUZZER_PLAY_REMOTE_PAIRING()  Buzzer_PlayMusicEx(BUZZER_MUSIC_REMOTE_PAIRING, BUZZER_MODE_REPEAT, 0, 1000)
///< 播放遥控器配对成功音效
#define BUZZER_PLAY_REMOTE_SUCCESS()  Buzzer_PlayMusic(BUZZER_MUSIC_REMOTE_SUCCESS)
///< 播放遥控器配对失败音效
#define BUZZER_PLAY_REMOTE_FAILED()   Buzzer_PlayMusic(BUZZER_MUSIC_REMOTE_FAILED)

#endif /* __BUZZER_TASK_H__ */