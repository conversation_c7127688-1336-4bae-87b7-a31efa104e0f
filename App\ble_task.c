/**
 * @file ble_task.c
 * <AUTHOR> (<EMAIL>)
 * @brief 蓝牙任务实现
 * @version 0.1
 * @date 2025-05-06
 *
 * @copyright Copyright (c) 2025
 *
 */
#include "ble_task.h"
#include "led_task.h"
#include "buzzer_task.h"
#include "ble_key_event.h"
#include "param_task.h"

// 任务配置
#define BLE_EVENT_QUEUE_LEN (10) // 事件队列长度

// 静态上下文
static ble_task_ctx_t s_ble_ctx;
static TaskHandle_t s_ble_task_handle = NULL;

// 前置声明
static void ble_task_entry(void *pvParameters);
static void ble_pairing_timer_cb(TimerHandle_t xTimer);
static void ble_tick_timer_cb(TimerHandle_t xTimer);
static int ble_uart_rx_poll(void);
static bool ble_verify_frame(const ble_frame_t *frame);
static bool ble_is_valid_mac(const uint8_t *mac);
static uint8_t ble_calculate_checksum(const uint8_t *data, uint8_t len);
static void ble_handle_pairing_success(const uint8_t *mac);
static int ble_mac_equal(const uint8_t *mac1, const uint8_t *mac2);
static void ble_handle_pairing_combo_key(uint32_t key_value, uint8_t key_sta);
static void ble_led_buzzer_pairing_window(void);
static void ble_led_buzzer_pairing_success(void);
static void ble_led_buzzer_pairing_timeout(void);
static void ble_set_error(ble_error_e err, const char *msg);
static void ble_led_buzzer_error(void);

/**
 * @brief 初始化蓝牙任务
 *
 * 初始化蓝牙任务上下文，设置默认参数，创建事件队列和定时器
 *
 * @return 无
 */
void ble_task_init(void)
{
    memset(&s_ble_ctx, 0, sizeof(s_ble_ctx));
    // 读取已保存的遥控器MAC地址
    uint8_t saved_mac[6] = {0};
    param_load_req_t req = {
        .type       = PARAM_LOAD_REMOTE_ID,
        .result     = saved_mac};
    if (param_load_async(&req)) {
        memcpy(s_ble_ctx.mac_addr, saved_mac, 6);
        s_ble_ctx.is_paired = 1;
    } else {
        memset(s_ble_ctx.mac_addr, 0, 6);
        s_ble_ctx.is_paired = 0;
    }
    s_ble_ctx.state                  = BLE_STATE_IDLE;
    s_ble_ctx.error                  = BLE_ERROR_NONE;
    s_ble_ctx.pair_key_threshold     = 3000; // 默认长按阈值
    s_ble_ctx.config.pairing_timeout = 30;   // 默认30秒
    s_ble_ctx.config.baudrate        = 9600; // 默认9600

    // 创建事件队列
    s_ble_ctx.event_queue = xQueueCreate(BLE_EVENT_QUEUE_LEN, sizeof(ble_event_msg_t));
    if (s_ble_ctx.event_queue == NULL) {
        BLE_LOG_ERROR("ble_task_init: xQueueCreate failed");
    }

    // 创建定时器
    s_ble_ctx.pairing_timer = xTimerCreate("BLEPairingTmr", pdMS_TO_TICKS(30000), pdFALSE, NULL, ble_pairing_timer_cb);
    if (s_ble_ctx.pairing_timer == NULL) {
        BLE_LOG_ERROR("ble_task_init: xTimerCreate failed");
    } else {
        // 启动定时器
        xTimerStart(s_ble_ctx.pairing_timer, 0);
    }
    s_ble_ctx.tick_timer = xTimerCreate("BLETickTmr", pdMS_TO_TICKS(5), pdTRUE, NULL, ble_tick_timer_cb);
    if (s_ble_ctx.tick_timer == NULL) {
        BLE_LOG_ERROR("ble_task_init: xTimerCreate failed");
    } else {
        // 启动定时器
        xTimerStart(s_ble_ctx.tick_timer, 0);
    }

    // 串口初始化
    bsp_Usart1Init(s_ble_ctx.config.baudrate);

    // 获取串口1接收缓冲区
    s_ble_ctx.uart_rx_rb = usart_get_rxrb(USART1);

    // 创建互斥锁
    s_ble_ctx.mutex = xSemaphoreCreateMutex();
    if (s_ble_ctx.mutex == NULL) {
        BLE_LOG_ERROR("ble_task_init: xSemaphoreCreateMutex failed");
    }

    // 创建任务
    if (s_ble_task_handle == NULL) {
        BaseType_t xReturn = xTaskCreate(ble_task_entry,
                                         "ble_task",
                                         APP_BLE_TASK_STACK_SIZE,
                                         NULL,
                                         APP_BLE_PRIO,
                                         &s_ble_task_handle);
        if (xReturn != pdPASS) {
            BLE_LOG_ERROR("ble_task_init: xTaskCreate failed");
        }
    }

    // 主动进入配对模式
    ble_event_msg_t evt = {.type = BLE_EVENT_PAIRING};
    if (xQueueSend(s_ble_ctx.event_queue, &evt, 0) != pdPASS) {
        BLE_LOG_ERROR("ble_task_init: xQueueSend failed");
    }
}

/**
 * @brief 蓝牙任务入口
 *
 * 主FreeRTOS任务入口，处理事件队列中的事件
 *
 * @param pvParameters 任务参数
 *
 * @return 无
 */
static void ble_task_entry(void *pvParameters)
{
    ble_event_msg_t event;

    for (;;) {
        if (xQueueReceive(s_ble_ctx.event_queue, &event, portMAX_DELAY) == pdPASS) {
            // 上锁，保护上下文状态
            xSemaphoreTake(s_ble_ctx.mutex, portMAX_DELAY);
            switch (s_ble_ctx.state) {
                case BLE_STATE_IDLE:
                    switch (event.type) {
                        case BLE_EVENT_PAIRING:
                            s_ble_ctx.state                    = BLE_STATE_PAIRING;
                            s_ble_ctx.is_pairing_window_active = 1;
                            xTimerChangePeriod(s_ble_ctx.pairing_timer, pdMS_TO_TICKS(s_ble_ctx.config.pairing_timeout * 1000), 0);
                            xTimerStart(s_ble_ctx.pairing_timer, 0);
                            ble_led_buzzer_pairing_window(); // 配对窗口提示
                            break;
                        case BLE_EVENT_UART_RX:
                            // 已配对且MAC匹配，进入连接状态
                            if (s_ble_ctx.is_paired && ble_is_valid_mac(s_ble_ctx.mac_addr) &&
                                ble_mac_equal(s_ble_ctx.rx_frame.mac, s_ble_ctx.mac_addr)) {
                                s_ble_ctx.state = BLE_STATE_CONNECTED;
                                BLE_LOG_INFO("BLE connected");
                                // TODO: LED/Buzzer indication
                            }
                            break;
                        case BLE_EVENT_TICK:
                            // 空闲态无按键计时
                            break;
                        default:
                            break;
                    }
                    break;
                case BLE_STATE_PAIRING:
                    switch (event.type) {
                        case BLE_EVENT_PAIRING_TIMEOUT:
                            s_ble_ctx.state                    = BLE_STATE_IDLE;
                            s_ble_ctx.is_pairing_window_active = 0;
                            ble_led_buzzer_pairing_timeout(); // 配对超时提示
                            break;
                        case BLE_EVENT_UART_RX: {
                            // 解析按键值和状态
                            uint32_t key_value = s_ble_ctx.rx_frame.key_value;
                            uint8_t key_sta    = s_ble_ctx.rx_frame.key_sta;
                            // 处理配对组合键
                            ble_handle_pairing_combo_key(Swap32(key_value), key_sta);
                            // 已配对且MAC匹配，直接进入连接
                            if (s_ble_ctx.is_paired && ble_is_valid_mac(s_ble_ctx.mac_addr) &&
                                ble_mac_equal(s_ble_ctx.rx_frame.mac, s_ble_ctx.mac_addr)) {
                                s_ble_ctx.state                    = BLE_STATE_CONNECTED;
                                s_ble_ctx.is_pairing_window_active = 0;
                                ble_led_buzzer_pairing_success(); // 配对成功提示
                                BLE_LOG_INFO("BLE connected after pairing");
                                break;
                            }
                            // 新配对（长按判定在TICK事件）
                            break;
                        }
                        case BLE_EVENT_TICK:
                            // 长按判定
                            // BLE_LOG_DEBUG("[PAIR] TICK: is_pair_key_pressed=%d, pair_key_press_time=%d", s_ble_ctx.is_pair_key_pressed, s_ble_ctx.pair_key_press_time);
                            if (s_ble_ctx.is_pair_key_pressed) {
                                s_ble_ctx.pair_key_press_time += 5; // tick 5ms
                                if (s_ble_ctx.pair_key_press_time >= BLE_PAIR_LONGPRESS_THRESHOLD_MS) {
                                    BLE_LOG_INFO("[PAIR] Combo key long press detected, pairing success");
                                    // 长按配对成功
                                    ble_handle_pairing_success(s_ble_ctx.rx_frame.mac);
                                    s_ble_ctx.state                    = BLE_STATE_CONNECTED;
                                    s_ble_ctx.is_pairing_window_active = 0;
                                    s_ble_ctx.is_pair_key_pressed      = 0;
                                    s_ble_ctx.pair_key_press_time      = 0;
                                    ble_led_buzzer_pairing_success(); // 配对成功提示
                                }
                            }
                            break;
                        default:
                            break;
                    }
                    break;
                case BLE_STATE_CONNECTED:
                    switch (event.type) {
                        case BLE_EVENT_DISCONNECT:
                            BLE_LOG_WARN("BLE disconnected");
                            s_ble_ctx.state = BLE_STATE_IDLE;
                            break;
                        case BLE_EVENT_UART_RX: {
                            if (!ble_mac_equal(s_ble_ctx.rx_frame.mac, s_ble_ctx.mac_addr)) {
                                // MAC不匹配，错误处理
                                ble_set_error(BLE_ERROR_MAC_MISMATCH, "MAC不匹配");
                                BLE_LOG_ERROR("MAC mismatch, expected: %02X:%02X:%02X:%02X:%02X:%02X, received: %02X:%02X:%02X:%02X:%02X:%02X",
                                              s_ble_ctx.mac_addr[0], s_ble_ctx.mac_addr[1], s_ble_ctx.mac_addr[2], s_ble_ctx.mac_addr[3], s_ble_ctx.mac_addr[4], s_ble_ctx.mac_addr[5],
                                              s_ble_ctx.rx_frame.mac[0], s_ble_ctx.rx_frame.mac[1], s_ble_ctx.rx_frame.mac[2], s_ble_ctx.rx_frame.mac[3], s_ble_ctx.rx_frame.mac[4], s_ble_ctx.rx_frame.mac[5]);
                                break;
                            }
                            // 解析按键值和状态
                            uint32_t key_value = s_ble_ctx.rx_frame.key_value;
                            uint8_t key_sta    = s_ble_ctx.rx_frame.key_sta;
                            // 分发普通按键事件
                            ble_key_event_handle(Swap32(key_value), key_sta);
                            break;
                        }
                        case BLE_EVENT_TICK:
                            // 长按判定
                            if (s_ble_ctx.is_pair_key_pressed) {
                                s_ble_ctx.pair_key_press_time += 5;
                                if (s_ble_ctx.pair_key_press_time >= BLE_PAIR_LONGPRESS_THRESHOLD_MS) {
                                    // 长按配对成功（可选：重新配对）
                                    ble_handle_pairing_success(s_ble_ctx.rx_frame.mac);
                                    s_ble_ctx.is_pair_key_pressed = 0;
                                    s_ble_ctx.pair_key_press_time = 0;
                                    ble_led_buzzer_pairing_success();
                                }
                            }
                            break;
                        default:
                            break;
                    }
                    break;
                case BLE_STATE_ERROR:
                    ble_led_buzzer_error(); // 错误提示
                    ble_clear_error();
                    s_ble_ctx.state = BLE_STATE_IDLE;
                    break;
                default:
                    s_ble_ctx.state = BLE_STATE_IDLE;
                    break;
            }
            xSemaphoreGive(s_ble_ctx.mutex);
        }
    }
}

/**
 * @brief 配对组合键处理
 *
 * 处理配对组合键的逻辑，包括长按判定和按键状态处理
 *
 * @param key_value 按键值
 * @param key_sta 按键状态
 */
static void ble_handle_pairing_combo_key(uint32_t key_value, uint8_t key_sta)
{
    BLE_LOG_DEBUG("[PAIR] combo_key: value=0x%08X, sta=%d", key_value, key_sta);
    if (key_value == REMOTE_PAIRCOMBO_KEY) {
        if (key_sta == KEY_STA_DOWN) {
            if (s_ble_ctx.is_pair_key_pressed == 0) {
                s_ble_ctx.is_pair_key_pressed = 1;
                s_ble_ctx.pair_key_press_time = 0;
                BLE_LOG_DEBUG("[PAIR] KEY DOWN, start timing");
            }
        } else if (key_sta == KEY_STA_UP) {
            s_ble_ctx.is_pair_key_pressed = 0;
            s_ble_ctx.pair_key_press_time = 0;
            BLE_LOG_DEBUG("[PAIR] KEY UP, stop timing");
        }
    }
}

/**
 * @brief LED/Buzzer接口（预留实现）
 *
 * 预留LED/Buzzer接口实现，用于配对窗口提示和配对成功提示
 *
 * @return 无
 */
static void ble_led_buzzer_pairing_window(void)
{
    BLE_LOG_INFO("Enter pairing window, timeout: %d s", s_ble_ctx.config.pairing_timeout);
    LED_BLINK_SLOW(LED_BLE);      // 蓝牙LED慢闪
    BUZZER_PLAY_REMOTE_PAIRING(); // 配对提示音
}

/**
 * @brief 配对成功提示
 *
 * 预留LED/Buzzer接口实现，用于配对成功提示
 *
 * @return 无
 */
static void ble_led_buzzer_pairing_success(void)
{
    LED_off(LED_BLE); // 配对成功后关闭蓝牙LED
    Buzzer_Stop();    // 停止蜂鸣器
}

/**
 * @brief 配对超时提示
 *
 * 预留LED/Buzzer接口实现，用于配对超时提示
 *
 * @return 无
 */
static void ble_led_buzzer_pairing_timeout(void)
{
    BLE_LOG_WARN("Pairing timeout");
    LED_off(LED_BLE); // 配对超时后关闭蓝牙LED
    Buzzer_Stop();    // 停止蜂鸣器
}

/**
 * @brief 错误提示
 *
 * 预留LED/Buzzer接口实现，用于错误提示
 *
 * @return 无
 */
static void ble_led_buzzer_error(void)
{
    LED_BLINK_ERROR(LED_BLE); // 蓝牙LED错误闪烁
    BUZZER_PLAY_ERROR();      // 错误提示音
}

/**
 * @brief 轮询并解析串口接收缓冲区，返回1表示发现有效帧
 *
 * @return 1表示发现有效帧，0表示未发现
 */
static int ble_uart_rx_poll(void)
{
    static struct {
        uint8_t frame_buffer[sizeof(ble_frame_t)];
        uint16_t frame_index;
        bool frame_start;
        bool frame_head_ok;
        uint32_t last_byte_time;
    } rx_state = {0};

    uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    int found             = 0;
    uint8_t data;

    if (s_ble_ctx.uart_rx_rb == NULL) {
        return 0;
    }

    // 检查帧接收超时
    if (rx_state.frame_start &&
        (current_time - rx_state.last_byte_time > 50)) {
        rx_state.frame_start   = false;
        rx_state.frame_head_ok = false;
        rx_state.frame_index   = 0;
    }

    // 处理所有可用数据
    while (ring_buf_get(s_ble_ctx.uart_rx_rb, &data, 1) == 1) {
        rx_state.last_byte_time = current_time;

        if (!rx_state.frame_start) {
            if (data == 0xAA) {
                rx_state.frame_buffer[0] = data;
                rx_state.frame_index     = 1;
                rx_state.frame_start     = true;
                rx_state.frame_head_ok   = false;
            }
            continue;
        }
        if (rx_state.frame_index == 1) {
            if (data == 0x55) {
                rx_state.frame_buffer[1] = data;
                rx_state.frame_index     = 2;
                rx_state.frame_head_ok   = true;
            } else {
                rx_state.frame_start = false;
                rx_state.frame_index = 0;
            }
            continue;
        }
        if (!rx_state.frame_head_ok) {
            rx_state.frame_start = false;
            rx_state.frame_index = 0;
            continue;
        }
        if (rx_state.frame_index < sizeof(ble_frame_t)) {
            rx_state.frame_buffer[rx_state.frame_index++] = data;
            if (rx_state.frame_index >= sizeof(ble_frame_t)) {
                if (data == 0xFE) {
                    memcpy(&s_ble_ctx.rx_frame, rx_state.frame_buffer, sizeof(ble_frame_t));
                    if (ble_verify_frame(&s_ble_ctx.rx_frame)) {
                        // char rx_hex[3 * sizeof(ble_frame_t) + 1] = {0};
                        // for (uint16_t i = 0; i < sizeof(ble_frame_t); i++) {
                        //     sprintf(rx_hex + 3 * i, "%02X ", rx_state.frame_buffer[i]);
                        // }
                        // BLE_LOG_DEBUG("Received valid BLE frame");
                        // BLE_LOG_DEBUG("Raw frame: %s", rx_hex);
                        found = 1;
                    }
                }
                rx_state.frame_start   = false;
                rx_state.frame_head_ok = false;
                rx_state.frame_index   = 0;
            }
        } else {
            rx_state.frame_start   = false;
            rx_state.frame_head_ok = false;
            rx_state.frame_index   = 0;
        }
    }
    return found;
}

/**
 * @brief 帧校验
 *
 * 校验帧的头部和尾部，以及校验和
 *
 * @param frame 帧
 * @return 校验成功返回true，否则返回false
 */
static bool ble_verify_frame(const ble_frame_t *frame)
{
    if ((Swap16(frame->head) != BLE_FRAME_HEAD) || (frame->tail != BLE_FRAME_TAIL)) {
        ble_set_error(BLE_ERROR_FRAME, "帧头/帧尾错误");
        BLE_LOG_ERROR("Frame head: 0x%04X, tail: 0x%02X", Swap16(frame->head), frame->tail);
        return false;
    }
    uint8_t calc_sum = ble_calculate_checksum(&frame->mac[0], sizeof(ble_frame_t) - 4);
    if (calc_sum != frame->sum) {
        ble_set_error(BLE_ERROR_CHECKSUM, "校验和错误");
        return false;
    }
    if (!ble_is_valid_mac(frame->mac)) {
        ble_set_error(BLE_ERROR_MAC_MISMATCH, "MAC无效");
        return false;
    }
    return true;
}

/**
 * @brief MAC地址有效性检查
 *
 * 检查MAC地址是否有效
 *
 * @param mac MAC地址
 * @return 有效返回true，否则返回false
 */
static bool ble_is_valid_mac(const uint8_t *mac)
{
    bool all_zero = true, all_ff = true;
    for (int i = 0; i < 6; i++) {
        if (mac[i] != 0x00) all_zero = false;
        if (mac[i] != 0xFF) all_ff = false;
    }
    return !(all_zero || all_ff);
}

/**
 * @brief 计算校验和
 *
 * 计算校验和
 *
 * @param data 数据
 * @param len 长度
 * @return 校验和
 */
static uint8_t ble_calculate_checksum(const uint8_t *data, uint8_t len)
{
    uint8_t sum = 0;
    for (uint8_t i = 0; i < len; i++) {
        sum += data[i];
    }
    return sum;
}

/**
 * @brief MAC地址比较
 *
 * 比较两个MAC地址
 *
 * @param mac1 MAC地址1
 * @param mac2 MAC地址2
 * @return 相等返回true，否则返回false
 */
static int ble_mac_equal(const uint8_t *mac1, const uint8_t *mac2)
{
    // 只做比较，不设置错误码
    return (memcmp(mac1, mac2, 6) == 0);
}

/**
 * @brief 配对成功处理
 *
 * 保存MAC地址，设置配对标志，调用外部保存接口
 *
 * @param mac MAC地址
 */
static void ble_handle_pairing_success(const uint8_t *mac)
{
    BLE_LOG_INFO("Pairing success, MAC: %02X:%02X:%02X:%02X:%02X:%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
    BLE_LOG_INFO("[PAIR] ble_handle_pairing_success() called");
    if (memcmp(s_ble_ctx.mac_addr, mac, 6) != 0) {
        memcpy(s_ble_ctx.mac_addr, mac, 6);

        param_save_req_t req = {
            .type     = PARAM_SAVE_REMOTE_ID,
            .data     = s_ble_ctx.mac_addr};
        param_save_async(&req);
    }
    s_ble_ctx.is_paired = 1;
    ble_clear_error();
}

/**
 * @brief 设置错误码并记录日志
 *
 * 设置错误码，记录日志，提示LED/Buzzer
 *
 * @param err 错误码
 * @param msg 错误信息
 */
static void ble_set_error(ble_error_e err, const char *msg)
{
    s_ble_ctx.error = err;
    BLE_LOG_ERROR("BLE ERROR %d: %s", err, msg ? msg : "");
    ble_led_buzzer_error();
    s_ble_ctx.state = BLE_STATE_ERROR;
}

/**
 * @brief 配对窗口定时器回调
 *
 * 配对窗口定时器回调
 *
 * @param xTimer 定时器句柄
 */
static void ble_pairing_timer_cb(TimerHandle_t xTimer)
{
    // 发送配对窗口超时事件到队列
    ble_event_msg_t evt = {.type = BLE_EVENT_PAIRING_TIMEOUT};
    xQueueSend(s_ble_ctx.event_queue, &evt, 0);
}

/**
 * @brief 周期tick定时器回调
 *
 * 周期tick定时器回调
 *
 * @param xTimer 定时器句柄
 */
static void ble_tick_timer_cb(TimerHandle_t xTimer)
{
    // 轮询串口接收缓冲区
    if (ble_uart_rx_poll()) {
        // 有效帧到达，发送事件
        ble_event_msg_t evt = {.type = BLE_EVENT_UART_RX};
        xQueueSend(s_ble_ctx.event_queue, &evt, 0);
    }
    // 发送tick事件到队列（用于按键计时等）
    ble_event_msg_t evt_tick = {.type = BLE_EVENT_TICK};
    xQueueSend(s_ble_ctx.event_queue, &evt_tick, 0);
}

/**
 * @brief 获取错误码
 *
 * 获取错误码
 *
 * @return 错误码
 */
ble_error_e ble_get_error(void)
{
    return s_ble_ctx.error;
}

/**
 * @brief 清除错误码
 *
 * 清除错误码
 */
void ble_clear_error(void)
{
    s_ble_ctx.error = BLE_ERROR_NONE;
}

/**
 * @brief 获取配对状态
 *
 * 获取配对状态
 *
 * @return 配对状态
 */
uint8_t get_pairing_status(void)
{
    return s_ble_ctx.is_paired;
}
