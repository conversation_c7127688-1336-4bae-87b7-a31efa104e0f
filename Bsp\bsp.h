#ifndef __BSP_H__
#define __BSP_H__

#include "bsp_clock.h"
#include "bsp_adc.h"
#include "bsp_usart.h"
#include "bsp_pwm.h"
#include "bsp_radar.h"
#include "bsp_hall.h"
#include "bsp_led.h"
#include "bsp_spi.h"
#include "bsp_iwdt.h"
#include "bsp_log.h"
#include "bsp_buzzer.h"
#include "bsp_lvd.h"
#include "bsp_timer.h"
#include "bsp_lin.h"

#define LOG_CONFIG(baudrate) bsp_Usart2Init(baudrate)

// 系统时钟定义
#define BSP_TICKS_PER_SEC      1000U // 系统时钟频率为1000Hz (1ms)

// 时间转换宏
#define BSP_MS_TO_TICKS(ms)    ((ms) * BSP_TICKS_PER_SEC / 1000U)    // 毫秒转换为时钟节拍
#define BSP_SEC_TO_TICKS(s)    ((s) * BSP_TICKS_PER_SEC)             // 秒转换为时钟节拍
#define BSP_TICKS_TO_MS(ticks) ((ticks) * 1000U / BSP_TICKS_PER_SEC) // 时钟节拍转换为毫秒

extern void bsp_init(void);
extern uint32_t BSP_GetTickCount(void);

#endif