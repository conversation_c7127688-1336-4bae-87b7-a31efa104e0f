/**
 * @file ble_key_event.c
 * <AUTHOR> (<EMAIL>)
 * @brief 蓝牙按键事件处理
 * @version 0.1
 * @date 2025-05-06
 *
 * @copyright Copyright (c) 2025
 *
 */
#include "ble_key_event.h"
#include "ble_task.h"
#include "led_task.h"
#include "buzzer_task.h"
#include "motor_task.h"
#include "msg_task.h"

/**
 * @brief 按键按下事件处理
 *
 * @param key_value 按键值
 * @param key_sta 按键状态
 */
void ble_key_event_handle_down(uint32_t key_value)
{
    control_msg_t msg;

    switch (key_value) {
        case REMOTE_KEY_HEADUP:
            msg.src                   = MSG_SRC_BLE;
            msg.cmd                   = CTRL_CMD_HEAD;
            msg.param.bed_move.part   = MOTOR_ID_BACK1;
            msg.param.bed_move.action = BED_MOVE_UP;
            msg_send(&msg);
            break;
        case REMOTE_KEY_HEADDOWN:
            msg.src                   = MSG_SRC_BLE;
            msg.cmd                   = CTRL_CMD_HEAD;
            msg.param.bed_move.part   = MOTOR_ID_BACK1;
            msg.param.bed_move.action = BED_MOVE_DOWN;
            msg_send(&msg);
            break;
        case REMOTE_KEY_LEGUP:
            msg.src                   = MSG_SRC_BLE;
            msg.cmd                   = CTRL_CMD_LEG;
            msg.param.bed_move.part   = MOTOR_ID_LEG;
            msg.param.bed_move.action = BED_MOVE_UP;
            msg_send(&msg);
            break;
        case REMOTE_KEY_LEGDOWN:
            msg.src                   = MSG_SRC_BLE;
            msg.cmd                   = CTRL_CMD_LEG;
            msg.param.bed_move.part   = MOTOR_ID_LEG;
            msg.param.bed_move.action = BED_MOVE_DOWN;
            msg_send(&msg);
            break;
        default:
            break;
    }
}

/**
 * @brief 蓝牙按键事件处理（抬起）
 *
 * @param key_value 按键值
 * @param key_sta 按键状态
 */
void ble_key_event_handle_up(uint32_t key_value)
{
    control_msg_t msg;
    switch (key_value) {
        case REMOTE_KEY_HEADUP:
        case REMOTE_KEY_HEADDOWN:
            msg.src                   = MSG_SRC_BLE;
            msg.cmd                   = CTRL_CMD_HEAD;
            msg.param.bed_move.part   = MOTOR_ID_BACK1;
            msg.param.bed_move.action = BED_MOVE_STOP;
            msg_send(&msg);
            break;

        case REMOTE_KEY_LEGUP:
        case REMOTE_KEY_LEGDOWN:
            msg.src                   = MSG_SRC_BLE;
            msg.cmd                   = CTRL_CMD_LEG;
            msg.param.bed_move.part   = MOTOR_ID_LEG;
            msg.param.bed_move.action = BED_MOVE_STOP;
            msg_send(&msg);
            break;

        case REMOTE_KEY_LIGHT:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_LIGHT;
            msg.param.value = RGB_NONE;
            msg_send(&msg);
            break;

        case REMOTE_KEY_CUSTOMMODE1:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_MEMORY;
            msg.param.value = GET_MEMORY1;
            msg_send(&msg);
            break;
        case REMOTE_KEY_CUSTOMMODE2:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_MEMORY;
            msg.param.value = GET_MEMORY2;
            msg_send(&msg);
            break;
        case REMOTE_KEY_CUSTOMMODE3:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_MEMORY;
            msg.param.value = GET_MEMORY3;
            msg_send(&msg);
            break;

        case REMOTE_KEY_CUSTOMMODE1_COMBO:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_MEMORY;
            msg.param.value = SET_MEMORY1;
            msg_send(&msg);
            break;
        case REMOTE_KEY_CUSTOMMODE2_COMBO:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_MEMORY;
            msg.param.value = SET_MEMORY2;
            msg_send(&msg);
            break;
        case REMOTE_KEY_CUSTOMMODE3_COMBO:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_MEMORY;
            msg.param.value = SET_MEMORY3;
            msg_send(&msg);
            break;

        case REMOTE_KEY_SPACE:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_WORK_MODE;
            msg.param.value = BED_MODE_SPACE;
            msg_send(&msg);
            break;
        case REMOTE_KEY_MOVIE:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_WORK_MODE;
            msg.param.value = BED_MODE_MOVIE;
            msg_send(&msg);
            break;
        case REMOTE_KEY_FLAT:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_WORK_MODE;
            msg.param.value = BED_MODE_FLAT;
            msg_send(&msg);
            break;
        case REMOTE_KEY_SLEEP:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_WORK_MODE;
            msg.param.value = BED_MODE_SLEEP;
            msg_send(&msg);
            break;
        case REMOTE_KEY_READ:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_WORK_MODE;
            msg.param.value = BED_MODE_READ;
            msg_send(&msg);
            break;

        case REMOTE_KEY_MASSAGEPOWER:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_MASSAGE_POWER;
            msg.param.value = 0;
            msg_send(&msg);
            break;
        case REMOTE_KEY_BACKMASSAGE:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_MASSAGE_BACK_STRENGTH;
            msg.param.value = 0;
            msg_send(&msg);
            break;
        case REMOTE_KEY_LEGMASSAGE:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_MASSAGE_LEG_STRENGTH;
            msg.param.value = 0;
            msg_send(&msg);
            break;
        case REMOTE_KEY_MASSAGEMODE:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_MASSAGE_MODE;
            msg.param.value = 0;
            msg_send(&msg);
            break;
        case REMOTE_KEY_MASSAGETIMER:
            msg.src         = MSG_SRC_BLE;
            msg.cmd         = CTRL_CMD_MASSAGE_TIME;
            msg.param.value = 0;
            msg_send(&msg);
            break;

        default:
            break;
    }
}

/**
 * @brief 蓝牙按键事件处理
 *
 * @param key_value 按键值
 * @param key_sta 按键状态
 */
void ble_key_event_handle(uint32_t key_value, uint8_t key_sta)
{
    // 只处理非配对组合键
    if (key_value == REMOTE_PAIRCOMBO_KEY) {
        return;
    }
    switch (key_sta) {
        case KEY_STA_DOWN:
            ble_key_event_handle_down(key_value);
            break;
        case KEY_STA_UP:
            LED_BLINK_ONCE(LED_BLE);   // 蓝牙LED闪烁一次
            BUZZER_PLAY_KEY_RELEASE(); // 蜂鸣器响一下
            ble_key_event_handle_up(key_value);
            break;
        case KEY_STA_LONGPRESSED:
            // 可选：长按处理
            break;
        case KEY_STA_SHORTPRESSED:
            // 可选：短按处理
            break;
        default:
            break;
    }
}