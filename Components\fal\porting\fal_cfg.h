/*
 * Copyright (c) 2006-2018, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2018-05-17     armink       the first version
 */

#ifndef _FAL_CFG_H_
#define _FAL_CFG_H_

#include "bsp.h"

#define FAL_USING_SFUD_PORT
#define FAL_PART_HAS_TABLE_CFG

#define NOR_FLASH_DEV_NAME     "norflash0"

#define FAL_BL_PARTITION_NAME  "bl"
#define FAL_APP_PARTITION_NAME "app"
#define FAL_ENV_PARTITION_NAME "env"
#define FAL_LOG_PARTITION_NAME "log"
#define FAL_IAP_PARTITION_NAME "iap"

/* ===================== Flash device Configuration ========================= */
extern const struct fal_flash_dev hc32_onchip_flash;
extern struct fal_flash_dev nor_flash0;

/* flash device table */
#define FAL_FLASH_DEV_TABLE \
    {                       \
        &hc32_onchip_flash, \
        &nor_flash0,        \
    }

/* ====================== Partition Configuration ========================== */
#ifdef FAL_PART_HAS_TABLE_CFG
/* 内部Flash分区配置 */
#define BL_PARTITION_START_ADDR  0x00000000                                    /* Bootloader起始地址 */
#define BL_PARTITION_SIZE        28 * 1024                                     /* Bootloader大小 28KB */
#define APP_PARTITION_START_ADDR (BL_PARTITION_START_ADDR + BL_PARTITION_SIZE) /* 应用程序紧接着Bootloader */
#define APP_PARTITION_SIZE       100 * 1024                                    /* 应用程序大小 100KB */

/* 外部Flash(NOR Flash)分区配置 */
#define ENV_PARTITION_START_ADDR 0x00000000                                      /* 环境变量从NOR Flash起始地址开始 */
#define ENV_PARTITION_SIZE       1024 * 1024                                     /* 环境变量区大小 1MB */
#define LOG_PARTITION_START_ADDR (ENV_PARTITION_START_ADDR + ENV_PARTITION_SIZE) /* 日志区紧接着环境变量区 */
#define LOG_PARTITION_SIZE       1024 * 1024                                     /* 日志区大小 1MB */
#define IAP_PARTITION_START_ADDR (LOG_PARTITION_START_ADDR + LOG_PARTITION_SIZE) /* IAP区紧接着日志区 */
#define IAP_PARTITION_SIZE       1024 * 1024                                     /* IAP区大小 1MB */

/* partition table */
#define FAL_PART_TABLE                                                                                                                        \
    {                                                                                                                                         \
        {FAL_PART_MAGIC_WORD, FAL_BL_PARTITION_NAME, "hc32_onchip", BL_PARTITION_START_ADDR, BL_PARTITION_SIZE, 0},         /* Bootloader */  \
        {FAL_PART_MAGIC_WORD, FAL_APP_PARTITION_NAME, "hc32_onchip", APP_PARTITION_START_ADDR, APP_PARTITION_SIZE, 0},      /* Application */ \
        {FAL_PART_MAGIC_WORD, FAL_ENV_PARTITION_NAME, NOR_FLASH_DEV_NAME, ENV_PARTITION_START_ADDR, ENV_PARTITION_SIZE, 0}, /* ENV */         \
        {FAL_PART_MAGIC_WORD, FAL_LOG_PARTITION_NAME, NOR_FLASH_DEV_NAME, LOG_PARTITION_START_ADDR, LOG_PARTITION_SIZE, 0}, /* LOG */         \
        {FAL_PART_MAGIC_WORD, FAL_IAP_PARTITION_NAME, NOR_FLASH_DEV_NAME, IAP_PARTITION_START_ADDR, IAP_PARTITION_SIZE, 0}, /* IAP */         \
    }
#endif /* FAL_PART_HAS_TABLE_CFG */

#endif /* _FAL_CFG_H_ */
