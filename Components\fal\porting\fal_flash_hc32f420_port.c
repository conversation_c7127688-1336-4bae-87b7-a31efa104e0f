#include <fal.h>
#include "flash.h"

static int init(void)
{
    en_result_t ret;
    
    /* 配置Flash等待周期，根据系统时钟频率设置 */
    ret = Flash_WaitCycle(FlashWaitCycle1);
    if (Ok != ret) {
        return -1;
    }
    
    /* 配置Flash缓存 */
    ret = Flash_CacheConfig(FLASH_CACHE_INSTRUCTION_EN | FLASH_CACHE_DATA_EN);
    if (Ok != ret) {
        return -1;
    }
    
    return 0;
}

static int read(long offset, uint8_t *buf, size_t size)
{
    uint32_t addr = hc32_onchip_flash.addr + offset;
    uint32_t *src = (uint32_t *)addr;
    uint32_t *dst = (uint32_t *)buf;
    size_t word_size = size / 4;
    size_t remain_size = size % 4;
    
    /* 配置读模式 */
    en_result_t ret = Flash_OpModeConfig(FlashReadMode);
    if (Ok != ret) {
        return -1;
    }
    
    /* 按字读取 */
    for (size_t i = 0; i < word_size; i++) {
        dst[i] = src[i];
    }
    
    /* 读取剩余字节 */
    if (remain_size > 0) {
        uint8_t *src_byte = (uint8_t *)&src[word_size];
        uint8_t *dst_byte = (uint8_t *)&dst[word_size];
        for (size_t i = 0; i < remain_size; i++) {
            dst_byte[i] = src_byte[i];
        }
    }
    
    return size;
}

static int write(long offset, const uint8_t *buf, size_t size)
{
    uint32_t addr = hc32_onchip_flash.addr + offset;
    uint32_t *src = (uint32_t *)buf;
    uint32_t *dst = (uint32_t *)addr;
    size_t word_size = size / 4;
    size_t remain_size = size % 4;
    en_result_t ret;
    
    /* 配置写模式 */
    ret = Flash_OpModeConfig(FlashWriteMode);
    if (Ok != ret) {
        return -1;
    }
    
    /* 解锁目标扇区 */
    uint32_t sector_num = (addr - hc32_onchip_flash.addr) / hc32_onchip_flash.blk_size;
    uint32_t lock_value = 1u << (sector_num % 32);
    ret = Flash_LockSet(sector_num < 32 ? FlashLock0 : FlashLock1, lock_value);
    if (Ok != ret) {
        return -1;
    }
    
    /* 按字写入 */
    for (size_t i = 0; i < word_size; i++) {
        ret = Flash_WriteWord(addr + i * 4, &src[i], 1);
        if (Ok != ret) {
            Flash_LockAll();
            return -1;
        }
    }
    
    /* 写入剩余字节 */
    if (remain_size > 0) {
        uint16_t half_word = 0;
        uint8_t *src_byte = (uint8_t *)&src[word_size];
        uint8_t *dst_byte = (uint8_t *)&dst[word_size];
        
        /* 将剩余字节转换为半字 */
        for (size_t i = 0; i < remain_size; i++) {
            half_word |= (src_byte[i] << (i * 8));
        }
        
        ret = Flash_WriteHalfWord(addr + word_size * 4, &half_word, 1);
        if (Ok != ret) {
            Flash_LockAll();
            return -1;
        }
    }
    
    /* 上锁Flash */
    ret = Flash_LockAll();
    if (Ok != ret) {
        return -1;
    }
    
    return size;
}

static int erase(long offset, size_t size)
{
    uint32_t addr = hc32_onchip_flash.addr + offset;
    uint32_t sector_size = hc32_onchip_flash.blk_size;
    uint32_t sector_count = (size + sector_size - 1) / sector_size;
    en_result_t ret;
    
    /* 配置擦除模式 */
    ret = Flash_OpModeConfig(FlashSectorEraseMode);
    if (Ok != ret) {
        return -1;
    }
    
    /* 按扇区擦除 */
    for (uint32_t i = 0; i < sector_count; i++) {
        uint32_t sector_addr = addr + i * sector_size;
        uint32_t sector_num = (sector_addr - hc32_onchip_flash.addr) / sector_size;
        
        /* 解锁目标扇区 */
        uint32_t lock_value = 1u << (sector_num % 32);
        ret = Flash_LockSet(sector_num < 32 ? FlashLock0 : FlashLock1, lock_value);
        if (Ok != ret) {
            Flash_LockAll();
            return -1;
        }
        
        /* 擦除扇区 */
        ret = Flash_SectorErase(sector_addr);
        if (Ok != ret) {
            Flash_LockAll();
            return -1;
        }
    }
    
    /* 上锁Flash */
    ret = Flash_LockAll();
    if (Ok != ret) {
        return -1;
    }
    
    return size;
}

const struct fal_flash_dev hc32_onchip_flash =
    {
        .name       = "hc32_onchip",
        .addr       = 0x08000000,
        .len        = 128 * 1024,
        .blk_size   = 4 * 1024,
        .ops        = {init, read, write, erase},
        .write_gran = 8,
};