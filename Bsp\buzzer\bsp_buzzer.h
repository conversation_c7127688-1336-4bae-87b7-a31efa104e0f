#ifndef __BSP_BUZZER_H__
#define __BSP_BUZZER_H__

#include "config.h"

// 蜂鸣器型号和参数
// 型号: SFN-1407PA7.6
// 额定频率: 4.0KHz
// 最大输入电压: 30Vp-p
// 电容量: 12±30%nF
// 声压级: ≥85dB at 4.0KHz方波5Vp-p
// 消耗电流: ≤5mA at 4.0KHz方波5Vp-p

// 蜂鸣器频率范围定义
#define BUZZER_FREQ_MIN           50   // 最低频率
#define BUZZER_FREQ_MAX           4000 // 最高频率（标称频率）
#define BUZZER_FREQ_NOMINAL       4000 // 标称频率(根据规格书为4.0KHz)

// 标准音阶定义
#define BUZZER_NOTE_REST          0 // 休止符

// 低音区（L）
#define BUZZER_NOTE_C2            130 // 低音do
#define BUZZER_NOTE_D2            146 // 低音re
#define BUZZER_NOTE_E2            164 // 低音mi
#define BUZZER_NOTE_F2            174 // 低音fa
#define BUZZER_NOTE_G2            196 // 低音sol
#define BUZZER_NOTE_A2            220 // 低音la
#define BUZZER_NOTE_B2            246 // 低音si

// 中音区
#define BUZZER_NOTE_C3            261 // 中音do
#define BUZZER_NOTE_D3            293 // 中音re
#define BUZZER_NOTE_E3            329 // 中音mi
#define BUZZER_NOTE_F3            349 // 中音fa
#define BUZZER_NOTE_G3            392 // 中音sol
#define BUZZER_NOTE_A3            440 // 中音la
#define BUZZER_NOTE_B3            494 // 中音si

// 高音区
#define BUZZER_NOTE_C4            523 // 高音do
#define BUZZER_NOTE_D4            587 // 高音re
#define BUZZER_NOTE_E4            659 // 高音mi
#define BUZZER_NOTE_F4            698 // 高音fa
#define BUZZER_NOTE_G4            784 // 高音sol
#define BUZZER_NOTE_A4            880 // 高音la
#define BUZZER_NOTE_B4            987 // 高音si

// 更高音区
#define BUZZER_NOTE_C5            1046 // 更高音do
#define BUZZER_NOTE_D5            1175 // 更高音re
#define BUZZER_NOTE_E5            1318 // 更高音mi
#define BUZZER_NOTE_F5            1397 // 更高音fa
#define BUZZER_NOTE_G5            1568 // 更高音sol
#define BUZZER_NOTE_A5            1760 // 更高音la
#define BUZZER_NOTE_B5            1976 // 更高音si

// 最高音区
#define BUZZER_NOTE_C6            2093 // 最高音do
#define BUZZER_NOTE_D6            2349 // 最高音re
#define BUZZER_NOTE_E6            2637 // 最高音mi
#define BUZZER_NOTE_F6            2794 // 最高音fa
#define BUZZER_NOTE_G6            3136 // 最高音sol
#define BUZZER_NOTE_A6            3520 // 最高音la
#define BUZZER_NOTE_B6            3951 // 最高音si
#define BUZZER_NOTE_C7            4186 // 最高音do
#define BUZZER_NOTE_E7            5274 // 最高音mi（新增）

// 蜂鸣器音符时值定义
#define BUZZER_DURATION_WHOLE     1200 // 全音符
#define BUZZER_DURATION_HALF      600  // 半音符
#define BUZZER_DURATION_QUARTER   300  // 四分音符
#define BUZZER_DURATION_EIGHTH    150  // 八分音符
#define BUZZER_DURATION_SIXTEENTH 75   // 十六分音符

// 蜂鸣器IO定义
#define BUZZER_PWR_PORT           HC_GPIOD
#define BUZZER_PWR_PIN            GPIO_PIN_01

// 蜂鸣器控制宏
#define BUZZER_PWR_ON()           GPIO_PD01_RESET() // PNP晶体管需要低电平导通
#define BUZZER_PWR_OFF()          GPIO_PD01_SET()   // PNP晶体管需要高电平截止

// 蜂鸣器音色定义
typedef enum {
    BUZZER_TONE_SQUARE, // 方波
    BUZZER_TONE_PULSE,  // 脉冲波
    BUZZER_TONE_BRIGHT, // 明亮音色
    BUZZER_TONE_SOFT    // 柔和音色
} buzzer_tone_t;

// 蜂鸣器音符结构体
typedef struct {
    uint16_t freq;        // 频率
    uint16_t duration;    // 持续时间(ms)
    uint8_t duty;         // 占空比(0-100)，默认50
    uint8_t legato;       // 连音标志，1表示与下一个音符连接不停顿
    uint8_t power_stable; // 电源稳定标志，1表示需要等待电源稳定
    uint8_t attack;       // 起音时间(ms)
    uint8_t decay;        // 衰减时间(ms)
    uint8_t sustain;      // 持续音量(0-100)
    uint16_t release;     // 释放时间(ms)，支持更长的尾音
    buzzer_tone_t tone;   // 音色类型
} buzzer_note_t;

// 基础硬件控制函数
void bsp_BuzzerInit(void);                           // 初始化蜂鸣器硬件
void bsp_BuzzerOn(uint16_t freq);                    // 开启蜂鸣器
void bsp_BuzzerOff(void);                            // 关闭蜂鸣器
void bsp_BuzzerSetFreq(uint16_t freq, uint8_t duty, uint16_t elapsed_time_ms, uint16_t note_duration_ms); // 设置蜂鸣器频率和占空比，支持ADSR包络

// PWM控制函数
void bsp_BuzzerPwmInit(void); // 初始化蜂鸣器PWM

// 音色控制函数
void bsp_BuzzerSetADSR(uint8_t attack, uint8_t decay, uint8_t sustain, uint8_t release); // 设置ADSR包络

#endif
