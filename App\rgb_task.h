#ifndef RGB_TASK_H
#define RGB_TASK_H

#include "app_config.h"
#include "app_log.h"
#include "bsp.h"

// =================== RGB 类型定义 ===================

/**
 * @brief RGB类型
 */
typedef enum {
    RGB_TYPE_SINGLE = 0, ///< 单色RGB
    RGB_TYPE_RGB,        ///< 三色RGB
    RGB_TYPE_MAX
} RgbType;

/**
 * @brief RGB通道
 */
typedef enum {
    RGB_CHANNEL_SINGLE = 0, ///< 单色通道
    RGB_CHANNEL_RGB,        ///< 三色通道
    RGB_CHANNEL_MAX
} RgbChannel;

/**
 * @brief RGB灯效类型
 */
typedef enum {
    RGB_EFFECT_NONE = 0, ///< 无灯效
    RGB_EFFECT_STATIC,   ///< 常亮
    RGB_EFFECT_BREATH,   ///< 呼吸灯
    RGB_EFFECT_RAINBOW,  ///< 彩虹灯
    RGB_EFFECT_FLASH,    ///< 闪烁
    RGB_EFFECT_MAX
} RgbEffect;

/**
 * @brief RGB响应模式
 */
typedef enum {
    RGB_RESPONSE_NORMAL = 0, ///< 正常响应
    RGB_RESPONSE_FAST,       ///< 快速响应
    RGB_RESPONSE_MAX
} RgbResponseMode;

// =================== RGB 事件结构体 ===================

/**
 * @brief RGB事件结构体
 */
typedef struct {
    uint32_t period;    ///< 周期
    RgbChannel channel; ///< 通道
    RgbEffect effect;   ///< 灯效
    union {
        struct {
            uint8_t r;      ///< 红色分量
            uint8_t g;      ///< 绿色分量
            uint8_t b;      ///< 蓝色分量
        } color;            ///< 颜色结构体
        uint8_t brightness; ///< 亮度
    };
} RgbEvt;

// =================== RGB 状态结构体 ===================

/**
 * @brief RGB状态结构体
 */
typedef struct {
    uint32_t period;       ///< 周期
    uint32_t tickCount;    ///< 计时
    uint16_t step;         ///< 步进/动画进度
    uint8_t r;             ///< 红色分量
    uint8_t g;             ///< 绿色分量
    uint8_t b;             ///< 蓝色分量
    uint8_t brightness;    ///< 亮度
    uint8_t isOn : 1;      ///< 是否开启
    uint8_t effect : 3;    ///< 灯效类型
    uint8_t direction : 1; ///< 呼吸/渐变方向
    uint8_t reserved : 3;  ///< 保留
} RgbState;

// =================== 硬件相关宏定义 ===================

#define RGB_SINGLE_PWM_PORT           PWM61                                                                      ///< 单色RGB PWM端口
#define RGB_RED_PWM_PORT              PWM63                                                                      ///< 红色PWM端口
#define RGB_GREEN_PWM_PORT            PWM61                                                                      ///< 绿色PWM端口
#define RGB_BLUE_PWM_PORT             PWM62                                                                      ///< 蓝色PWM端口
#define RGB_SET_PWM_DUTY(port, duty)  bsp_SetPwmDuty(port, duty)                                                 ///< 设置PWM占空比
#define GET_RGB_PWM_PORT(channel)     ((channel) == RGB_CHANNEL_SINGLE ? RGB_SINGLE_PWM_PORT : RGB_RED_PWM_PORT) ///< 获取PWM端口
#define GET_RGB_COLOR_PWM_PORT(color) ((color) == 0 ? RGB_RED_PWM_PORT : (color) == 1 ? RGB_GREEN_PWM_PORT \
                                                                                      : RGB_BLUE_PWM_PORT) ///< 获取颜色PWM端口
#define RGB_BREATH_STEP    8                                                                               ///< 呼吸步进
#define RGB_RAINBOW_STEP   8                                                                               ///< 彩虹步进
#define RGB_FLASH_ON_TIME  500                                                                             ///< 闪烁亮时间(ms)
#define RGB_FLASH_OFF_TIME 500                                                                             ///< 闪烁灭时间(ms)
#define RGB_DEFAULT_PERIOD 1000                                                                            ///< 默认周期(ms)
#define RGB_TICK_MS        10                                                                              ///< 任务周期(ms)
#define RGB_MAX_BRIGHTNESS 255                                                                             ///< 最大亮度
#define RGB_MAX_PWM_LIMIT  95                                                                              ///< 最大PWM占空比

// =================== 颜色常量 ===================
#define RGB_COLOR_RED      {255, 0, 0}     ///< 红色
#define RGB_COLOR_GREEN    {0, 255, 0}     ///< 绿色
#define RGB_COLOR_BLUE     {0, 0, 255}     ///< 蓝色
#define RGB_COLOR_WHITE    {255, 255, 255} ///< 白色
#define RGB_COLOR_OFF      {0, 0, 0}       ///< 关闭

// =================== FreeRTOS 任务接口 ===================
/**
 * @brief 初始化RGB任务
 */
void rgb_task_init(void);
/**
 * @brief 打开RGB
 */
void RGB_TurnOn(RgbChannel channel);
/**
 * @brief 关闭RGB
 */
void RGB_TurnOff(RgbChannel channel);
/**
 * @brief 设置RGB颜色
 */
void RGB_SetColor(RgbChannel channel, uint8_t r, uint8_t g, uint8_t b);
/**
 * @brief 设置RGB亮度
 */
void RGB_SetBrightness(RgbChannel channel, uint8_t brightness);
/**
 * @brief 设置RGB灯效
 */
void RGB_SetEffect(RgbChannel channel, RgbEffect effect, uint32_t period);
/**
 * @brief 设置RGB响应模式
 */
void RGB_SetResponseMode(RgbResponseMode mode);
/**
 * @brief 获取RGB响应模式
 */
RgbResponseMode RGB_GetResponseMode(void);
/**
 * @brief 设置RGB类型
 */
void RGB_SetType(RgbType type);
/**
 * @brief 获取RGB类型
 */
RgbType RGB_GetType(void);

#endif // RGB_TASK_H