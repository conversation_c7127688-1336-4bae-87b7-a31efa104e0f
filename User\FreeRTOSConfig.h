/*
 * FreeRTOS Kernel V11.1.0
 * Copyright (C) 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * SPDX-License-Identifier: MIT
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 * https://www.FreeRTOS.org
 * https://github.com/FreeRTOS
 *
 */

#ifndef FREERTOS_CONFIG_H
#define FREERTOS_CONFIG_H

#if defined(__ICCARM__) || defined(__CC_ARM) || defined(__GNUC__)
#include <stdint.h>
extern uint32_t SystemCoreClock;
#endif

/******************************************************************************/
/* Hardware description related definitions. **********************************/
/******************************************************************************/

/* 定义CPU主频, 单位: Hz, 无默认需定义 */
#define configCPU_CLOCK_HZ                        ((unsigned long)SystemCoreClock)

/******************************************************************************/
/* Scheduling behaviour related definitions. **********************************/
/******************************************************************************/

/* 定义系统时钟节拍频率, 单位: Hz, 无默认需定义 */
#define configTICK_RATE_HZ                        (1000U)
/* 1: 抢占式调度器, 0: 协程式调度器, 无默认需定义 */
#define configUSE_PREEMPTION                      1
/* 1: 使能时间片调度, 默认: 1 */
#define configUSE_TIME_SLICING                    1
/* 1: 使用硬件计算下一个要运行的任务, 0: 使用软件算法计算下一个要运行的任务, 默认: 0 */
#define configUSE_PORT_OPTIMISED_TASK_SELECTION   0
/* 1: 使能tickless低功耗模式, 默认: 0 */
#define configUSE_TICKLESS_IDLE                   0
/* 定义最大优先级数, 最大优先级=configMAX_PRIORITIES-1, 无默认需定义 */
#define configMAX_PRIORITIES                      32U
/* 定义空闲任务的栈空间大小, 单位: Word, 无默认需定义 */
#define configMINIMAL_STACK_SIZE                  32U
/* 定义任务名最大字符数, 默认: 16 */
#define configMAX_TASK_NAME_LEN                   16U
/* 1: 定义系统时钟节拍计数器的数据类型为16位无符号数, 无默认需定义，一般使用32位单片机，所以这一位一般为0 */
#define configTICK_TYPE_WIDTH_IN_BITS             TICK_TYPE_WIDTH_32_BITS
/* 1: 使能在抢占式调度下,同优先级的任务能抢占空闲任务, 默认: 1 */
#define configIDLE_SHOULD_YIELD                   1
/* 定义任务通知数组的大小, 默认: 1 */
#define configTASK_NOTIFICATION_ARRAY_ENTRIES     1U
/* 定义可以注册的信号量和消息队列的个数, 默认: 0 */
#define configQUEUE_REGISTRY_SIZE                 0U
/* 头文件FreeRTOS.h包含一系列#define宏定义，用来映射版本V8.0.0和V8.0.0之前版本的数据类型名字。这些宏可以确保RTOS内核升级到V8.0.0或以上版本时，之前的应用代码不用做任何修改。在FreeRTOSConfig.h文件中设置宏configENABLE_BACKWARD_COMPATIBILITY为0会去掉这些宏定义，并且需要用户确认升级之前的应用没有用到这些名字。 */
#define configENABLE_BACKWARD_COMPATIBILITY       1
/* 设置每个任务的线程本地存储指针数组大小。 */
#define configNUM_THREAD_LOCAL_STORAGE_POINTERS   0
/* 设置用于在调用xTaskCreate()时指定堆栈深度的类型，以及使用堆栈大小的各种其他位置(例如，当返回堆栈高水位标记时)。旧版本的FreeRTOS使用UBaseType_t类型的变量指定堆栈大小，但发现这在8位微控制器上限制太大。configSTACK_DEPTH_TYPE允许应用程序开发人员指定要使用的类型，从而消除了这种限制。 */
#define configSTACK_DEPTH_TYPE                    size_t
/* FreeRTOS消息缓冲区使用configMESSAGE_BUFFER_LENGTH_TYPE类型的变量来存储每个消息的长度。如果configMESSAGE_BUFFER_LENGTH_TYPE没有定义，那么它将默认为size_t。如果存储在消息缓冲区中的消息永远不会超过255字节，那么定义configMESSAGE_BUFFER_LENGTH_TYPE为uint8_t将在32位微控制器上为每条消息节省3字节。同样，如果存储在消息缓冲区中的消息永远不会超过65535字节，那么定义configMESSAGE_BUFFER_LENGTH_TYPE为uint16_t将在32位微控制器上为每条消息节省2字节。 */
#define configMESSAGE_BUFFER_LENGTH_TYPE          size_t
/* 1: 任务创建时分配Newlib的重入结构体, 默认: 0 */
#define configUSE_NEWLIB_REENTRANT                0

/******************************************************************************/
/* Software timer related definitions. ****************************************/
/******************************************************************************/

/* 1: 使能软件定时器, 默认: 0 */
#define configUSE_TIMERS                          1
/* 定义软件定时器任务的优先级, 无默认configUSE_TIMERS为1时需定义 */
#define configTIMER_TASK_PRIORITY                 (configMAX_PRIORITIES - 1U)
/* 定义软件定时器任务的栈空间大小, 无默认configUSE_TIMERS为1时需定义 */
#define configTIMER_TASK_STACK_DEPTH              168
/* 定义软件定时器命令队列的长度, 无默认configUSE_TIMERS为1时需定义 */
#define configTIMER_QUEUE_LENGTH                  10U

/******************************************************************************/
/* Memory allocation related definitions. *************************************/
/******************************************************************************/

/* 1: 支持静态申请内存, 默认: 0 */
#define configSUPPORT_STATIC_ALLOCATION           0
/* 1: 支持动态申请内存, 默认: 1 */
#define configSUPPORT_DYNAMIC_ALLOCATION          1
/* FreeRTOS堆中可用的RAM总量, 单位: Byte, 无默认需定义 */
#define configTOTAL_HEAP_SIZE                     ((size_t)(14 * 1024))
/* 1: 用户手动分配FreeRTOS内存堆(ucHeap), 默认: 0 */
#define configAPPLICATION_ALLOCATED_HEAP          0
/* 1: 用户自行实现任务创建时使用的内存申请与释放函数, 默认: 0 */
#define configSTACK_ALLOCATION_FROM_SEPARATE_HEAP 0
/* MiniListItem_t用于FreeRTOS列表中的开始和结束标记节点，ListItem_t用于FreeRTOS列表中的所有其他节点。当configUSE_MINI_LIST_ITEM设置为0时，minilisttem_t和ListItem_t都是相同的。当configUSE_MINI_LIST_ITEM设置为1时，minilisttem_t包含的字段比ListItem_t少3个，这节省了一些RAM，但代价是违反了一些编译器用于优化的严格别名规则。如果未定义，configUSE_MINI_LIST_ITEM默认为1，以便向后兼容。 */
#define configUSE_MINI_LIST_ITEM                  1

/******************************************************************************/
/* Interrupt nesting behaviour configuration. *********************************/
/******************************************************************************/
// /* Cortex-M specific definitions. */
#ifdef __NVIC_PRIO_BITS
// /* __BVIC_PRIO_BITS will be specified when CMSIS is being used. */
#define configPRIO_BITS __NVIC_PRIO_BITS
#else
#define configPRIO_BITS 4 /* 15 priority levels */
#endif

// /* 在调用"设置优先级"函数时可以使用的最低中断优先级 */
#define configLIBRARY_LOWEST_INTERRUPT_PRIORITY      7

// /* 可以被任何中断服务程序使用的最高中断优先级，它可以调用来中断安全的FreeRTOS API函数。
// 不要从任何比这个优先级更高的中断调用中断安全的FREERTOS API函数!(优先级越高，数值越低）*/
#define configLIBRARY_MAX_SYSCALL_INTERRUPT_PRIORITY 5

// /* 内核端口层本身使用的中断优先级。这些对所有Cortex-M端口都是通用的，并且不依赖于任何特定的库函数。*/
#define configKERNEL_INTERRUPT_PRIORITY              (configLIBRARY_LOWEST_INTERRUPT_PRIORITY << (8 - configPRIO_BITS))
// /* !!!! configMAX_SYSCALL_INTERRUPT_PRIORITY 不能设置为零 !!!!
// See http://www.FreeRTOS.org/RTOS-Cortex-M3-M4.html. */
#define configMAX_SYSCALL_INTERRUPT_PRIORITY         (configLIBRARY_MAX_SYSCALL_INTERRUPT_PRIORITY << (8 - configPRIO_BITS))

#define configMAX_API_CALL_INTERRUPT_PRIORITY        (configLIBRARY_MAX_SYSCALL_INTERRUPT_PRIORITY << (8 - configPRIO_BITS))

/******************************************************************************/
/* Hook and callback function related definitions. ****************************/
/******************************************************************************/

/* 1: 使能空闲任务钩子函数, 无默认需定义  */
#define configUSE_IDLE_HOOK                       0
/* 1: 使能系统时钟节拍中断钩子函数, 无默认需定义 */
#define configUSE_TICK_HOOK                       0
/* 1: 使能栈溢出检测方法1, 2: 使能栈溢出检测方法2, 默认: 0 */
#define configUSE_MALLOC_FAILED_HOOK              0
/* 1: 使能定时器服务任务首次执行前的钩子函数, 默认: 0 */
#define configUSE_DAEMON_TASK_STARTUP_HOOK        0
/* 1: 使能动态内存申请失败钩子函数, 默认: 0 */
#define configCHECK_FOR_STACK_OVERFLOW            0

/******************************************************************************/
/* Run time and task stats gathering related definitions. *********************/
/******************************************************************************/

/* 1: 使能任务运行时间统计功能, 默认: 0 */
#define configGENERATE_RUN_TIME_STATS             0
/* 1: 使能可视化跟踪调试, 默认: 0 */
#define configUSE_TRACE_FACILITY                  1
/* 1: configUSE_TRACE_FACILITY为1时，会编译vTaskList()和vTaskGetRunTimeStats()函数,默认: 0 */
#define configUSE_STATS_FORMATTING_FUNCTIONS      1
/* 1: 使能静态内存管理, 默认: 0 */
#define configKERNEL_PROVIDED_STATIC_MEMORY       1

/******************************************************************************/
/* Definitions that include or exclude functionality. *************************/
/******************************************************************************/

#define configUSE_TASK_NOTIFICATIONS              1
#define configUSE_MUTEXES                         1
#define configUSE_RECURSIVE_MUTEXES               1
#define configUSE_COUNTING_SEMAPHORES             1
/* 设置成1使能队列集功能（可以阻塞、挂起到多个队列和信号量），设置成0取消队列集功能。 */
#define configUSE_QUEUE_SETS                      1
#define configUSE_APPLICATION_TASK_TAG            1
#define INCLUDE_vTaskPrioritySet                  1
#define INCLUDE_uxTaskPriorityGet                 1
#define INCLUDE_vTaskDelete                       1
#define INCLUDE_vTaskSuspend                      1
#define INCLUDE_xResumeFromISR                    1
#define INCLUDE_vTaskDelayUntil                   1
#define INCLUDE_vTaskDelay                        1
#define INCLUDE_xTaskGetSchedulerState            1
#define INCLUDE_xTaskGetCurrentTaskHandle         1
#define INCLUDE_uxTaskGetStackHighWaterMark       1
#define INCLUDE_xTaskGetIdleTaskHandle            1
#define INCLUDE_eTaskGetState                     1
#define INCLUDE_xEventGroupSetBitFromISR          1
#define INCLUDE_xTimerPendFunctionCall            1
#define INCLUDE_xTaskAbortDelay                   1
#define INCLUDE_xTaskGetHandle                    1
#define INCLUDE_xTaskResumeFromISR                1

#endif /* FREERTOS_CONFIG_H */
