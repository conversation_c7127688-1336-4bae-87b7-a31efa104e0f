#ifndef __WIFI_TASK_H__
#define __WIFI_TASK_H__

#include "app_config.h"
#include "app_log.h"
#include "bsp.h"

// 迁移自 wifi_ao.h 的主要结构体

typedef enum {
    AT_IDLE = 0,
    AT_TEST,
    AT_RST,
    AT_GET_BLENAME,
    AT_GET_BLEADDR,
    AT_SET_BLENAME,
    AT_SET_WIFIMODE,
    AT_DISCONN_AP,
    AT_CHECK_SAVED_PARAM,
    AT_LOAD_SAVED_PARAM,
    AT_WAIT_APP_CFG,
    AT_CONN_AP,
    AT_MQTT_CFG,
    AT_MQTTCONN_BROKER,
    AT_MQTT_SUB,
    AT_CMD_MAX,
} wifi_step_e;

typedef enum {
    QOS0 = 0,
    QOS1,
    QOS2,
} qos_e;

typedef struct WifiTaskCtx {
    struct at_obj *at_obj;           // 指针类型，4/8字节
    uint32_t connect_start_time;     // 4字节
    uint16_t mqtt_server_port;       // 2字节
    uint8_t init_step;               // 1字节
    uint8_t mqtt_pub_qos;            // 1字节
    uint8_t mqtt_pub_retain;         // 1字节
    bool use_saved_param;            // 1字节
    uint8_t ble_mac_addr[6];         // 6字节
    char wifi_ssid[32];              // 32字节
    char wifi_passwd[32];            // 32字节
    char mqtt_server_addr[64];       // 64字节
    char mqtt_username[32];          // 32字节
    char mqtt_password[32];          // 32字节
} WifiTaskCtx;

#define MQTT_SUB_TOPIC "bed/test"
#define MQTT_PUB_TOPIC "bed/test"

void wifi_task_init(void);
void wifi_task_send_mqtt_data(const char *data);

#endif /* __WIFI_TASK_H__ */