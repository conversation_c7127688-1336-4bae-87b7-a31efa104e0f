#ifndef PARAM_H
#define PARAM_H

#include "app_config.h"
#include "app_log.h"
#include "bsp.h"

#define PARAM_MOTOR_NUM 4

// 参数类型定义
typedef enum {
    PARAM_SAVE_MOTOR,
    PARAM_SAVE_REMOTE_ID,
    PARAM_SAVE_CUSTOM_ANGLE,
    PARAM_SAVE_WIFI,           
    PARAM_SAVE_MQTT            
} param_save_type_t;

typedef enum {
    PARAM_LOAD_MOTOR,
    PARAM_LOAD_REMOTE_ID,
    PARAM_LOAD_CUSTOM_ANGLE,
    PARAM_LOAD_WIFI,           
    PARAM_LOAD_MQTT            
} param_load_type_t;

// 电机参数保存结构体
typedef struct {
    uint16_t up_travel_commutations;
    uint16_t down_travel_commutations;
    uint8_t is_calibrated;
} motor_param_store_t;

// 床自定义角度结构体
typedef struct {
    uint8_t back1_angle;
    uint8_t back2_angle;
    uint8_t waist_angle;
    uint8_t leg_angle;
} custom_bed_angle_t;

// 保存电机参数上下文结构体
typedef struct {
    uint8_t id;
    motor_param_store_t param; // 不再用指针
} motor_param_save_ctx_t;

// 保存自定义角度上下文结构体
typedef struct {
    uint8_t index;
    custom_bed_angle_t angle; // 不再用指针
} custom_angle_save_ctx_t;

// WiFi参数结构体
typedef struct {
    char ssid[32];
    char password[32];
} wifi_param_t;

// MQTT参数结构体
typedef struct {
    char server[64];
    uint16_t port;
    char username[32];
    char password[32];
} mqtt_param_t;

// 新的保存请求结构体
typedef struct {
    param_save_type_t type;
    const void *data; // 只传指针
    SemaphoreHandle_t done_sem; // 用于同步保存
    bool *success;              // 用于同步保存
} param_save_req_t;

// 新的读取请求结构体
typedef struct {
    param_load_type_t type;
    const void *arg;    // 读取参数（如id、index等）
    void *result;       // 结果缓冲区
    SemaphoreHandle_t done_sem;
    bool *success;
} param_load_req_t;

// 参数操作类型
typedef enum {
    PARAM_OP_SAVE,
    PARAM_OP_LOAD,
} param_op_type_t;

// 参数操作消息体
typedef struct {
    param_op_type_t op_type;
    union {
        param_save_req_t save;
        param_load_req_t load;
    } msg;
} param_msg_t;

// ================= 查表法相关类型定义 =================
// 查表法相关类型定义
typedef fdb_err_t (*param_save_handler_t)(const void *data);
typedef bool (*param_load_handler_t)(const void *arg, void *result);

// 查表项
typedef struct {
    uint8_t type;
    param_save_handler_t save_handler;
    param_load_handler_t load_handler;
} param_handler_entry_t;

bool param_save_async(const param_save_req_t *req);
bool param_load_async(param_load_req_t *req);

void param_task_init(void);

// 类型安全同步API
bool param_load_motor(uint8_t id, motor_param_store_t *out);
bool param_save_motor(uint8_t id, const motor_param_store_t *in);

bool param_load_custom_angle(uint8_t index, custom_bed_angle_t *out);
bool param_save_custom_angle(uint8_t index, const custom_bed_angle_t *in);

bool param_load_wifi(wifi_param_t *out);
bool param_save_wifi(const wifi_param_t *in);

bool param_load_mqtt(mqtt_param_t *out);
bool param_save_mqtt(const mqtt_param_t *in);

bool param_save_sync(param_save_req_t *req);

#endif // PARAM_H