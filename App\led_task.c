/**
 * @file led_task.c
 * <AUTHOR> (<EMAIL>)
 * @brief LED任务实现       
 * @version 0.1
 * @date 2025-05-06
 * 
 * @copyright Copyright (c) 2025
 * 
 */
#include "led_task.h"

#define LED_QUEUE_LENGTH 8

static LedControlContext s_ledCtx;          /**< 全局LED控制上下文 */
static QueueHandle_t s_ledQueue     = NULL; /**< LED控制消息队列 */
static TaskHandle_t s_ledTaskHandle = NULL; /**< LED控制任务句柄 */

/**
 * @brief 打开指定LED(硬件操作)
 * @param ledId LED编号
 */
static void led_hw_on(uint8_t ledId)
{
    switch (ledId) {
        case LED_SYS:
            SYSLED_PIN_RESET();
            break;
        case LED_WIFI:
            WIFILED_PIN_RESET();
            break;
        case LED_BLE:
            BLELED_PIN_RESET();
            break;
        default:
            break;
    }
}

/**
 * @brief 关闭指定LED(硬件操作)
 * @param ledId LED编号
 */
static void led_hw_off(uint8_t ledId)
{
    switch (ledId) {
        case LED_SYS:
            SYSLED_PIN_SET();
            break;
        case LED_WIFI:
            WIFILED_PIN_SET();
            break;
        case LED_BLE:
            BLELED_PIN_SET();
            break;
        default:
            break;
    }
}

/**
 * @brief 切换指定LED(硬件操作)
 * @param ledId LED编号
 */
static void led_hw_toggle(uint8_t ledId)
{
    switch (ledId) {
        case LED_SYS:
            SYSLED_TOGGLE();
            break;
        case LED_WIFI:
            WIFILED_TOGGLE();
            break;
        case LED_BLE:
            BLELED_TOGGLE();
            break;
        default:
            break;
    }
}

/**
 * @brief LED闪烁定时器回调(FreeRTOS软件定时器)
 * @param xTimer 定时器句柄
 *
 * 切换LED状态, 管理闪烁次数, 到达次数后自动停止并熄灭LED。
 */
static void led_blink_timer_cb(TimerHandle_t xTimer)
{
    uint32_t ledId = (uint32_t)pvTimerGetTimerID(xTimer);
    if (ledId >= LED_MAX) return;
    LedControlContext *ctx = &s_ledCtx;
    // 翻转LED状态
    ctx->state[ledId] ^= 1;
    if (ctx->state[ledId]) {
        led_hw_on(ledId);
    } else {
        led_hw_off(ledId);
    }
    // 处理闪烁计数
    if (ctx->blink[ledId].count > 0) {
        ctx->blink[ledId].count--;
        if (ctx->blink[ledId].count == 0) {
            // 闪烁结束, 确保LED熄灭
            ctx->state[ledId] = 0;
            led_hw_off(ledId);
            xTimerStop(xTimer, 0);
            return;
        }
    }
    // 设置下一个周期
    uint16_t next = ctx->state[ledId] ? ctx->blink[ledId].onTime : ctx->blink[ledId].offTime;
    if (next > 0) {
        xTimerChangePeriod(xTimer, pdMS_TO_TICKS(next), 0);
    }
}

/**
 * @brief LED控制任务
 * @param param 未使用
 *
 * 该任务接收LED控制消息, 并执行相应操作。
 */
static void led_task_entry(void *param)
{
    (void)param;
    LedMsg msg;

    for (;;) {
        if (xQueueReceive(s_ledQueue, &msg, portMAX_DELAY) == pdPASS) {
            uint8_t id = msg.ledId;
            if (id >= LED_MAX) continue;
            switch (msg.type) {
                case LED_MSG_ON:
                    s_ledCtx.state[id] = 1;
                    led_hw_on(id);
                    if (s_ledCtx.blink[id].timer) xTimerStop(s_ledCtx.blink[id].timer, 0);
                    break;
                case LED_MSG_OFF:
                    s_ledCtx.state[id] = 0;
                    led_hw_off(id);
                    if (s_ledCtx.blink[id].timer) xTimerStop(s_ledCtx.blink[id].timer, 0);
                    break;
                case LED_MSG_TOGGLE:
                    s_ledCtx.state[id] ^= 1;
                    if (s_ledCtx.state[id]) {
                        led_hw_on(id);
                    } else {
                        led_hw_off(id);
                    }
                    if (s_ledCtx.blink[id].timer) xTimerStop(s_ledCtx.blink[id].timer, 0);
                    break;
                case LED_MSG_BLINK:
                    // 简单闪烁: on/off等时, 持续
                    s_ledCtx.blink[id].onTime  = msg.onTime;
                    s_ledCtx.blink[id].offTime = msg.offTime;
                    s_ledCtx.blink[id].count   = 0;
                    s_ledCtx.state[id]         = 1;
                    led_hw_on(id);
                    if (s_ledCtx.blink[id].timer) {
                        xTimerChangePeriod(s_ledCtx.blink[id].timer, pdMS_TO_TICKS(msg.onTime), 0);
                        xTimerStart(s_ledCtx.blink[id].timer, 0);
                    }
                    break;
                case LED_MSG_BLINK_EX:
                    // 高级闪烁: 自定义on/off/count
                    s_ledCtx.blink[id].onTime  = msg.onTime;
                    s_ledCtx.blink[id].offTime = msg.offTime;
                    s_ledCtx.blink[id].count   = (msg.count == 0) ? 0 : (msg.count * 2 - 1);
                    s_ledCtx.state[id]         = 1;
                    led_hw_on(id);
                    if (s_ledCtx.blink[id].timer) {
                        xTimerChangePeriod(s_ledCtx.blink[id].timer, pdMS_TO_TICKS(msg.onTime), 0);
                        xTimerStart(s_ledCtx.blink[id].timer, 0);
                    }
                    break;
                default:
                    break;
            }
        }
    }
}

/**
 * @brief 打开指定LED(线程安全)
 * @param ledId LED编号
 */
void LED_on(Led_TypeDef ledId)
{
    LedMsg msg = {LED_MSG_ON, 0, 0, 0, ledId};
    xQueueSend(s_ledQueue, &msg, 0);
}

/**
 * @brief 关闭指定LED(线程安全)
 * @param ledId LED编号
 */
void LED_off(Led_TypeDef ledId)
{
    LedMsg msg = {LED_MSG_OFF, 0, 0, 0, ledId};
    xQueueSend(s_ledQueue, &msg, 0);
}

/**
 * @brief 切换指定LED(线程安全)
 * @param ledId LED编号
 */
void LED_toggle(Led_TypeDef ledId)
{
    LedMsg msg = {LED_MSG_TOGGLE, 0, 0, 0, ledId};
    xQueueSend(s_ledQueue, &msg, 0);
}

/**
 * @brief 指定LED等时闪烁(持续)
 * @param ledId LED编号
 * @param period 闪烁周期(ms)
 */
void LED_blink(Led_TypeDef ledId, uint16_t period)
{
    if (period == 0) return;
    LedMsg msg = {LED_MSG_BLINK, period / 2, period / 2, 0, ledId};
    xQueueSend(s_ledQueue, &msg, 0);
}

/**
 * @brief 高级闪烁控制
 * @param ledId LED编号
 * @param onTime 亮灯时间(ms)
 * @param offTime 灭灯时间(ms)
 * @param count 闪烁次数(0=持续)
 */
void LED_blinkEx(Led_TypeDef ledId, uint16_t onTime, uint16_t offTime, uint16_t count)
{
    if (onTime == 0 && offTime == 0) return;
    LedMsg msg = {LED_MSG_BLINK_EX, onTime, offTime, count, ledId};
    xQueueSend(s_ledQueue, &msg, 0);
}

/**
 * @brief LED控制模块初始化
 *
 * 必须在使用任何LED控制API前调用。初始化硬件、创建队列、定时器和任务。
 */
void led_task_init(void)
{
    memset(&s_ledCtx, 0, sizeof(s_ledCtx));
    // 初始化LED硬件
    bsp_LedInit();
    // 创建控制队列
    s_ledQueue = xQueueCreate(LED_QUEUE_LENGTH, sizeof(LedMsg));
    if (s_ledQueue == NULL) {
        LED_LOG_ERROR("led_task_init: xQueueCreate failed");
    }
    // 为每个LED创建定时器
    for (int i = 0; i < LED_MAX; ++i) {
        s_ledCtx.blink[i].timer = xTimerCreate("led_blink",
                                               pdMS_TO_TICKS(100),
                                               pdFALSE,
                                               (void *)(uint32_t)i,
                                               led_blink_timer_cb);
        if (s_ledCtx.blink[i].timer == NULL) {
            LED_LOG_ERROR("led_task_init: xTimerCreate failed");
        }
    }
    // 创建LED控制任务
    BaseType_t xReturn = xTaskCreate(led_task_entry,
                                     "led_task",
                                     APP_LED_TASK_STACK_SIZE,
                                     NULL,
                                     APP_LED_PRIO,
                                     &s_ledTaskHandle);
    if (xReturn != pdPASS) {
        LED_LOG_ERROR("led_task_init: xTaskCreate failed");
    }
}