#include "bsp_lvd.h"

/**
 * @brief  LVD 配置函数
 * @retval None
 */
static void lvd_config(void)
{
    stc_lvd_init_t stcLvdCfg = {0};

    SYSCTRL_PeriphClkEnable(SysctrlPeripherallvd); /* 开LVD时钟 */

    stcLvdCfg.u32TriggerAction = LVD_TRIGGER_ACTION_INT;     /* 配置触发产生中断 */
    stcLvdCfg.u32TriggerMode   = LVD_TRIGGER_MODE_HIGHLEVEL; /* 配置高电平触发 */
    stcLvdCfg.u32InputSource   = LVD_INPUT_AVCC;             /* 配置LVD输入源为AVCC */
    stcLvdCfg.u32ThresholdVolt = LVD_THRESHOLD_VOLT2p9V;     /* 配置LVD阈值电压为2.9v */
    stcLvdCfg.u32Filter        = LVD_FILTER_420US;           /* 配置滤波，滤波宽度小于6820us */
    LVD_Init(HC_LVD, &stcLvdCfg);                            /* 初始化LVD */

    LVD_ClearFlag_IT(HC_LVD);              /* 清除中断标志位 */
    LVD_EnableIT(HC_LVD);                  /* 使能中断 */
    EnableNvic(LVD_IRQn, IrqLevel4, TRUE); /* 使能中断向量NVIC */

    LVD_Enable(HC_LVD); /* LVD 模块使能 */
}

/**
 * @brief  LVD 初始化函数
 * @retval None
 */
void bsp_LvdInit(void)
{
    lvd_config();
}

/**
 * @brief  LVD 中断服务函数
 * @retval None
 */
void Lvd_IRQHandler(void)
{
    if (TRUE == LVD_IsActiveFlag_IT(HC_LVD)) {
        LVD_ClearFlag_IT(HC_LVD);
    }
}
