/**
 * @file    bsp_spi.h
 * @brief   硬件SPI驱动头文件
 * @version 1.0
 * @date    2023-10-01
 *
 * @copyright Copyright (c) 2025
 *
 */
#ifndef __BSP_SPI_H
#define __BSP_SPI_H

#include <stdint.h>
#include "bsp.h"

/* 定义SPI引脚 */
#define SPI_SCK_PORT       HC_GPIOC
#define SPI_MOSI_PORT      HC_GPIOC
#define SPI_MISO_PORT      HC_GPIOC
#define SPI_CS_PORT        HC_GPIOD

#define SPI_SCK_PIN        GPIO_PIN_13
#define SPI_MOSI_PIN       GPIO_PIN_15
#define SPI_MISO_PIN       GPIO_PIN_14
#define SPI_CS_PIN         GPIO_PIN_00

/* SPI超时时间(循环次数) */
#define SPI_TIMEOUT        5000

/* DMA通道定义 */
#define SPI_DMA_TX_CHANNEL DMA_CHANNEL2
#define SPI_DMA_RX_CHANNEL DMA_CHANNEL3

/* 返回值定义 */
#define SPI_OK     0
#define SPI_ERROR  1

/* 函数声明 */
void bsp_SpiInit(void);
uint8_t bsp_SpiTransfer(const uint8_t *tx_buf, uint8_t *rx_buf, uint16_t len);
void bsp_SpiSetCS(uint8_t state);
uint8_t bsp_SpiTransfer_DMA(const uint8_t *tx_buf, uint8_t *rx_buf, uint16_t len);

#endif /* __BSP_SPI_H */ 