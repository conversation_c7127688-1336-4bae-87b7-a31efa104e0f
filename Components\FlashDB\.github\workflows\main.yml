name: AutoTestCI
on: [push, pull_request]
jobs:
  test:
    name: AutoTest
    runs-on: ubuntu-latest
    env:
      TEST_BSP_ROOT: ../AutoTestBsp
      UTEST_RUNNER_PATH: ../UtestRunner
      RTT_EXEC_PATH: /opt/gcc-arm-none-eabi-10-2020-q4-major/bin
    steps:
    - uses: actions/checkout@v1
    - name: Prepare env
      run: |
        sudo apt-get update
        sudo apt-get -yqq install scons qemu-system git
        wget -q https://github.com/RT-Thread/toolchains-ci/releases/download/v1.3/gcc-arm-none-eabi-10-2020-q4-major-x86_64-linux.tar.bz2
        sudo tar xjf gcc-arm-none-eabi-10-2020-q4-major-x86_64-linux.tar.bz2 -C /opt
        git clone https://github.com/armink/UtestRunner.git $UTEST_RUNNER_PATH
        git clone https://github.com/armink/FlashDBAutoTestBSP.git $TEST_BSP_ROOT
    - name: Build bsp
      run: |
        cp -rf src/* $TEST_BSP_ROOT/packages/FlashDB/src
        cp -rf tests/* $TEST_BSP_ROOT/packages/FlashDB/tests
        cp -rf inc/fdb_def.h $TEST_BSP_ROOT/packages/FlashDB/inc/fdb_def.h
        cp -rf inc/fdb_low_lvl.h $TEST_BSP_ROOT/packages/FlashDB/inc/fdb_low_lvl.h
        cp -rf inc/flashdb.h $TEST_BSP_ROOT/packages/FlashDB/inc/flashdb.h
        /opt/gcc-arm-none-eabi-10-2020-q4-major/bin/arm-none-eabi-gcc --version
        scons -j$(nproc) -C $TEST_BSP_ROOT
    - name: Start test
      run: |
        python3 $UTEST_RUNNER_PATH/qemu_runner.py --elf $TEST_BSP_ROOT/rtthread.elf --sd $TEST_BSP_ROOT/sd.bin
        cat rtt_console.log
