/**
 * @file motor_task.c
 * @brief 推杆电机任务实现，三维表驱动状态机
 * <AUTHOR>
 * @date 2025-05-06
 */
#include "motor_task.h"
#include "param_task.h"

#define MOTOR_TASK_QUEUE_LEN  10
#define MOTOR_ANGLE_TOLERANCE 0.5f // 允许的最小角度误差（单位：度）

// =========================
// 内部函数声明
// =========================
static void motor_task_entry(void *param);
static void motor_task_timer_cb(TimerHandle_t xTimer);
static void motor_handle_cmd(const motor_task_cmd_t *cmd);
static void motor_periodic_check(void);
static void motor_stop(uint8_t id);
static void motor_update_position(uint8_t id);
static void motor_check_limit(uint8_t id);
static void motor_check_error(uint8_t id);
static void motor_target_angle_control(uint8_t id);

// =========================
// 静态变量与对象
// =========================
static motor_task_param_t s_motor_param[MOTOR_TASK_NUM];
static QueueHandle_t s_motor_cmd_queue  = NULL;
static TimerHandle_t s_motor_timer      = NULL;
static TaskHandle_t s_motor_task_handle = NULL;
// 新增：记录上次限位方向
static motor_task_dir_t s_motor_last_limit_dir[MOTOR_TASK_NUM] = {MOTOR_TASK_DIR_STOP};

// =========================
// 文件级静态变量：用于位置估算的起点和时间
// =========================
static uint16_t s_segment_start_commutation[MOTOR_TASK_NUM] = {0}; // 分段起点计数

// =========================
// 硬件抽象宏与映射
// =========================
static const unsigned char motor_pwm_up[MOTOR_TASK_NUM]   = {PWM11, PWM21, PWM42, PWM52};
static const unsigned char motor_pwm_down[MOTOR_TASK_NUM] = {PWM12, PWM22, PWM41, PWM51};

#define MOTOR_PWM_SET(id, dir, speed) motor_set_pwm((id), (dir), (speed))
#define MOTOR_GET_CURRENT(id)         bsp_GetFilteredCurrent(id)

// 事件处理函数声明
static void motor_action_up(uint8_t id, const motor_task_cmd_t *cmd);
static void motor_action_down(uint8_t id, const motor_task_cmd_t *cmd);
static void motor_action_stop(uint8_t id, const motor_task_cmd_t *cmd);
static void motor_action_move_to_angle(uint8_t id, const motor_task_cmd_t *cmd);
static void motor_action_ignore(uint8_t id, const motor_task_cmd_t *cmd);
static void motor_action_limit(uint8_t id, const motor_task_cmd_t *cmd);
static void motor_action_overcurrent(uint8_t id, const motor_task_cmd_t *cmd);
static void motor_action_stall(uint8_t id, const motor_task_cmd_t *cmd);

// =========================
// 校准相关类型与变量
// =========================

// 三维状态表
static const motor_state_table_entry_t motor_state_table[MOTOR_STATE_MAX][MOTOR_TASK_CMD_MAX][EVT_MAX] = {
    // IDLE
    [MOTOR_STATE_IDLE] = {
        [MOTOR_TASK_CMD_UP] = {
            [EVT_NONE]        = {motor_action_up, MOTOR_STATE_MOVING},
            [EVT_LIMIT]       = {motor_action_limit, MOTOR_STATE_STOPPED},
            [EVT_OVERCURRENT] = {motor_action_overcurrent, MOTOR_STATE_ERROR},
            [EVT_STALL]       = {motor_action_stall, MOTOR_STATE_ERROR},
        },
        [MOTOR_TASK_CMD_DOWN] = {
            [EVT_NONE]        = {motor_action_down, MOTOR_STATE_MOVING},
            [EVT_LIMIT]       = {motor_action_limit, MOTOR_STATE_STOPPED},
            [EVT_OVERCURRENT] = {motor_action_overcurrent, MOTOR_STATE_ERROR},
            [EVT_STALL]       = {motor_action_stall, MOTOR_STATE_ERROR},
        },
        [MOTOR_TASK_CMD_STOP] = {
            [EVT_NONE] = {motor_action_stop, MOTOR_STATE_STOPPED},
        },
        [MOTOR_TASK_CMD_MOVE_TO_ANGLE] = {
            [EVT_NONE]        = {motor_action_move_to_angle, MOTOR_STATE_MOVING},
            [EVT_LIMIT]       = {motor_action_limit, MOTOR_STATE_STOPPED},
            [EVT_OVERCURRENT] = {motor_action_overcurrent, MOTOR_STATE_ERROR},
            [EVT_STALL]       = {motor_action_stall, MOTOR_STATE_ERROR},
        },
    },
    // MOVING
    [MOTOR_STATE_MOVING] = {
        [MOTOR_TASK_CMD_UP] = {
            [EVT_NONE]        = {motor_action_ignore, MOTOR_STATE_MOVING},
            [EVT_LIMIT]       = {motor_action_limit, MOTOR_STATE_STOPPED},
            [EVT_OVERCURRENT] = {motor_action_overcurrent, MOTOR_STATE_ERROR},
            [EVT_STALL]       = {motor_action_stall, MOTOR_STATE_ERROR},
        },
        [MOTOR_TASK_CMD_DOWN] = {
            [EVT_NONE]        = {motor_action_ignore, MOTOR_STATE_MOVING},
            [EVT_LIMIT]       = {motor_action_limit, MOTOR_STATE_STOPPED},
            [EVT_OVERCURRENT] = {motor_action_overcurrent, MOTOR_STATE_ERROR},
            [EVT_STALL]       = {motor_action_stall, MOTOR_STATE_ERROR},
        },
        [MOTOR_TASK_CMD_STOP] = {
            [EVT_NONE] = {motor_action_stop, MOTOR_STATE_STOPPED},
        },
        [MOTOR_TASK_CMD_MOVE_TO_ANGLE] = {
            [EVT_NONE]        = {motor_action_move_to_angle, MOTOR_STATE_MOVING},
            [EVT_LIMIT]       = {motor_action_limit, MOTOR_STATE_STOPPED},
            [EVT_OVERCURRENT] = {motor_action_overcurrent, MOTOR_STATE_ERROR},
            [EVT_STALL]       = {motor_action_stall, MOTOR_STATE_ERROR},
        },
    },
    // STOPPED
    [MOTOR_STATE_STOPPED] = {
        [MOTOR_TASK_CMD_UP] = {
            [EVT_NONE] = {motor_action_up, MOTOR_STATE_MOVING},
        },
        [MOTOR_TASK_CMD_DOWN] = {
            [EVT_NONE] = {motor_action_down, MOTOR_STATE_MOVING},
        },
        [MOTOR_TASK_CMD_STOP] = {
            [EVT_NONE] = {motor_action_stop, MOTOR_STATE_STOPPED},
        },
        [MOTOR_TASK_CMD_MOVE_TO_ANGLE] = {
            [EVT_NONE] = {motor_action_move_to_angle, MOTOR_STATE_MOVING},
        },
    },
    // ERROR
    [MOTOR_STATE_ERROR] = {
        [MOTOR_TASK_CMD_UP] = {
            [EVT_NONE] = {motor_action_ignore, MOTOR_STATE_ERROR},
        },
        [MOTOR_TASK_CMD_DOWN] = {
            [EVT_NONE] = {motor_action_ignore, MOTOR_STATE_ERROR},
        },
        [MOTOR_TASK_CMD_STOP] = {
            [EVT_NONE] = {motor_action_stop, MOTOR_STATE_STOPPED},
        },
        [MOTOR_TASK_CMD_MOVE_TO_ANGLE] = {
            [EVT_NONE] = {motor_action_ignore, MOTOR_STATE_ERROR},
        },
    },
    // MANUAL
    [MOTOR_STATE_MANUAL] = {
        [MOTOR_TASK_CMD_UP] = {
            [EVT_NONE] = {motor_action_up, MOTOR_STATE_MANUAL},
        },
        [MOTOR_TASK_CMD_DOWN] = {
            [EVT_NONE] = {motor_action_down, MOTOR_STATE_MANUAL},
        },
        [MOTOR_TASK_CMD_STOP] = {
            [EVT_NONE] = {motor_action_stop, MOTOR_STATE_STOPPED},
        },
        [MOTOR_TASK_CMD_MOVE_TO_ANGLE] = {
            [EVT_NONE] = {motor_action_ignore, MOTOR_STATE_MANUAL},
        },
    },
    // HOMING
    [MOTOR_STATE_HOMING] = {
        [MOTOR_TASK_CMD_UP] = {
            [EVT_NONE]        = {motor_action_up, MOTOR_STATE_HOMING},
            [EVT_LIMIT]       = {motor_action_limit, MOTOR_STATE_STOPPED},
            [EVT_OVERCURRENT] = {motor_action_overcurrent, MOTOR_STATE_ERROR},
            [EVT_STALL]       = {motor_action_stall, MOTOR_STATE_ERROR},
        },
        [MOTOR_TASK_CMD_DOWN] = {
            [EVT_NONE]        = {motor_action_down, MOTOR_STATE_HOMING},
            [EVT_LIMIT]       = {motor_action_limit, MOTOR_STATE_STOPPED},
            [EVT_OVERCURRENT] = {motor_action_overcurrent, MOTOR_STATE_ERROR},
            [EVT_STALL]       = {motor_action_stall, MOTOR_STATE_ERROR},
        },
        [MOTOR_TASK_CMD_STOP] = {
            [EVT_NONE] = {motor_action_stop, MOTOR_STATE_STOPPED},
        },
        [MOTOR_TASK_CMD_MOVE_TO_ANGLE] = {
            [EVT_NONE] = {motor_action_ignore, MOTOR_STATE_HOMING},
        },
    },
};

/**
 * @brief 三维分发接口
 */
static void motor_dispatch(uint8_t id, motor_task_cmd_type_t cmd, motor_event_t evt, const motor_task_cmd_t *cmd_struct)
{
    if (id >= MOTOR_TASK_NUM) return;
    motor_task_param_t *p   = &s_motor_param[id];
    motor_state_t cur_state = p->state;
    if (cmd >= MOTOR_TASK_CMD_MAX || evt >= EVT_MAX) return;

    const motor_state_table_entry_t *entry = &motor_state_table[cur_state][cmd][evt];
    if (entry->action) {
        entry->action(id, cmd_struct);
        p->state = entry->next_state;
    }
}

/**
 * @brief 设置电机PWM
 */
static void motor_set_pwm(uint8_t id, motor_task_dir_t dir, uint8_t speed)
{
    if (id >= MOTOR_TASK_NUM) return;
    if (dir == MOTOR_TASK_DIR_UP) {
        bsp_SetPwmDuty(motor_pwm_up[id], speed);
        bsp_SetPwmDuty(motor_pwm_down[id], 0);
    } else if (dir == MOTOR_TASK_DIR_DOWN) {
        bsp_SetPwmDuty(motor_pwm_up[id], 0);
        bsp_SetPwmDuty(motor_pwm_down[id], speed);
    } else {
        bsp_SetPwmDuty(motor_pwm_up[id], 0);
        bsp_SetPwmDuty(motor_pwm_down[id], 0);
    }
}

/**
 * @brief 保存电机参数到flash
 * @param id 电机ID
 */
static void motor_save_param(uint8_t id)
{
    motor_param_store_t param = {
        .up_travel_commutations   = s_motor_param[id].up_travel_commutations,
        .down_travel_commutations = s_motor_param[id].down_travel_commutations,
        .is_calibrated            = s_motor_param[id].is_calibrated,
    };
    param_save_motor(id, &param);
}

// =========================
// 主任务、定时器、命令处理
// =========================

/**
 * @brief 电机任务初始化
 */
void motor_task_init(void)
{
    MOTOR_LOG_INFO("motor_task_init: 电机任务初始化");
    // 初始化参数
    memset(s_motor_param, 0, sizeof(s_motor_param));
    for (uint8_t i = 0; i < MOTOR_TASK_NUM; i++) {
        motor_param_store_t param;
        if (param_load_motor(i, &param)) {
            s_motor_param[i].is_calibrated            = param.is_calibrated;

            if (param.up_travel_commutations < 1000 || param.down_travel_commutations < 1000) {
                s_motor_param[i].up_travel_commutations   = (i == MOTOR_ID_BACK1) ? MOTOR_BACK1_DEFAULT_TRAVEL_UP_COMMUTATIONS : (i == MOTOR_ID_BACK2) ? MOTOR_BACK2_DEFAULT_TRAVEL_UP_COMMUTATIONS
                                                                                                                             : (i == MOTOR_ID_LEG)     ? MOTOR_LEG_DEFAULT_TRAVEL_UP_COMMUTATIONS
                                                                                                                                                       : MOTOR_WAIST_DEFAULT_TRAVEL_UP_COMMUTATIONS;
                s_motor_param[i].down_travel_commutations = (i == MOTOR_ID_BACK1) ? MOTOR_BACK1_DEFAULT_TRAVEL_DOWN_COMMUTATIONS : (i == MOTOR_ID_BACK2) ? MOTOR_BACK2_DEFAULT_TRAVEL_DOWN_COMMUTATIONS
                                                                                                                               : (i == MOTOR_ID_LEG)     ? MOTOR_LEG_DEFAULT_TRAVEL_DOWN_COMMUTATIONS
                                                                                                                                                         : MOTOR_WAIST_DEFAULT_TRAVEL_DOWN_COMMUTATIONS;
                MOTOR_LOG_INFO("motor_task_init: 电机%d 默认校准值: up_travel_commutations=%u, down_travel_commutations=%u", i, s_motor_param[i].up_travel_commutations, s_motor_param[i].down_travel_commutations);
            } else {
                s_motor_param[i].up_travel_commutations   = param.up_travel_commutations;
                s_motor_param[i].down_travel_commutations = param.down_travel_commutations;
                // 打印读出的校准值
                MOTOR_LOG_INFO("motor_task_init: 电机%d 读出校准值: up_travel_commutations=%u, down_travel_commutations=%u", i, s_motor_param[i].up_travel_commutations, s_motor_param[i].down_travel_commutations);
            }
        } else {
            s_motor_param[i].is_calibrated            = true;
            s_motor_param[i].up_travel_commutations   = (i == MOTOR_ID_BACK1) ? MOTOR_BACK1_DEFAULT_TRAVEL_UP_COMMUTATIONS : (i == MOTOR_ID_BACK2) ? MOTOR_BACK2_DEFAULT_TRAVEL_UP_COMMUTATIONS
                                                                                                                         : (i == MOTOR_ID_LEG)     ? MOTOR_LEG_DEFAULT_TRAVEL_UP_COMMUTATIONS
                                                                                                                                                   : MOTOR_WAIST_DEFAULT_TRAVEL_UP_COMMUTATIONS;
            s_motor_param[i].down_travel_commutations = (i == MOTOR_ID_BACK1) ? MOTOR_BACK1_DEFAULT_TRAVEL_DOWN_COMMUTATIONS : (i == MOTOR_ID_BACK2) ? MOTOR_BACK2_DEFAULT_TRAVEL_DOWN_COMMUTATIONS
                                                                                                                           : (i == MOTOR_ID_LEG)     ? MOTOR_LEG_DEFAULT_TRAVEL_DOWN_COMMUTATIONS
                                                                                                                                                     : MOTOR_WAIST_DEFAULT_TRAVEL_DOWN_COMMUTATIONS;
            // 打印默认校准值
            MOTOR_LOG_INFO("motor_task_init: 电机%d 默认校准值: up_travel_commutations=%u, down_travel_commutations=%u", i, s_motor_param[i].up_travel_commutations, s_motor_param[i].down_travel_commutations);
            // 保存默认校准值
            motor_param_store_t def_param = {
                .up_travel_commutations   = s_motor_param[i].up_travel_commutations,
                .down_travel_commutations = s_motor_param[i].down_travel_commutations,
                .is_calibrated            = s_motor_param[i].is_calibrated,
            };
            param_save_motor(i, &def_param);
        }
        s_motor_param[i].id                 = i;
        s_motor_param[i].dir                = MOTOR_TASK_DIR_STOP;
        s_motor_param[i].speed              = 0;
        s_motor_param[i].state              = MOTOR_STATE_HOMING;
        s_motor_param[i].max_angle          = (i == MOTOR_ID_BACK1) ? MOTOR_BACK1_MAX_ANGLE : (i == MOTOR_ID_BACK2) ? MOTOR_BACK2_MAX_ANGLE
                                                                                          : (i == MOTOR_ID_LEG)     ? MOTOR_LEG_MAX_ANGLE
                                                                                                                    : MOTOR_WAIST_MAX_ANGLE;
        s_motor_param[i].target_angle       = 0.0f;
        s_motor_param[i].is_moving_to_angle = false;
        s_motor_param[i].last_dir           = MOTOR_TASK_DIR_STOP;
        s_motor_param[i].commutation_count  = 0;
        // 分段累计机制初始化
        s_motor_param[i].segment_commutations = 0;
        s_motor_param[i].total_commutations   = 0;
        s_motor_param[i].last_move_dir        = MOTOR_TASK_DIR_STOP;
        s_motor_param[i].force_limit          = 0;
    }
    // 创建命令队列
    s_motor_cmd_queue = xQueueCreate(MOTOR_TASK_QUEUE_LEN, sizeof(motor_task_cmd_t));
    if (s_motor_cmd_queue == NULL) {
        MOTOR_LOG_ERROR("电机任务命令队列创建失败");
    }
    // 创建定时器
    s_motor_timer = xTimerCreate("motor_timer", pdMS_TO_TICKS(MOTOR_TASK_TIMER_PERIOD_MS), pdTRUE, NULL, motor_task_timer_cb);
    if (s_motor_timer == NULL) {
        MOTOR_LOG_ERROR("电机任务定时器创建失败");
    } else {
        // 启动定时器
        xTimerStart(s_motor_timer, 0);
        MOTOR_LOG_INFO("motor_task_init: 定时器创建并启动");
    }
    // 创建任务
    BaseType_t xReturn = xTaskCreate(motor_task_entry,
                                     "motor_task",
                                     APP_MOTOR_TASK_STACK_SIZE,
                                     NULL,
                                     APP_MOTOR_PRIO,
                                     &s_motor_task_handle);
    if (xReturn != pdPASS) {
        MOTOR_LOG_ERROR("电机任务创建失败");
    } else {
        MOTOR_LOG_INFO("motor_task_init: 电机任务创建成功");
    }

    // 先执行复位操作，校准流程将在复位完成后自动启动
    MOTOR_LOG_INFO("motor_task_init: 开始电机复位流程");
    for (uint8_t i = 0; i < MOTOR_TASK_NUM; i++) {
        s_motor_param[i].is_homing = true;

        // 发送向下命令，执行复位
        motor_task_cmd_t cmd = {
            .cmd      = MOTOR_TASK_CMD_DOWN,
            .motor_id = i,
        };
        motor_task_send_cmd(&cmd);
    }
    MOTOR_LOG_INFO("motor_task_init: 电机复位命令已下发，校准将在复位完成后自动启动");
}

/**
 * @brief 电机任务入口
 */
static void motor_task_entry(void *param)
{
    MOTOR_LOG_INFO("motor_task_entry: 电机任务启动");
    motor_task_cmd_t cmd;
    for (;;) {
        // 优先处理命令队列（非阻塞）
        while (xQueueReceive(s_motor_cmd_queue, &cmd, 0) == pdPASS) {
            motor_handle_cmd(&cmd);
        }
        // 等待定时器通知
        ulTaskNotifyTake(pdTRUE, portMAX_DELAY);
        // 周期性检测
        motor_periodic_check(); // 10ms执行一次
    }
}

/**
 * @brief 定时器回调
 */
static void motor_task_timer_cb(TimerHandle_t xTimer)
{
    xTaskNotifyGive(s_motor_task_handle);
}

/**
 * @brief 表驱动命令处理入口
 */
static void motor_handle_cmd(const motor_task_cmd_t *cmd)
{
    uint8_t id = cmd->motor_id;
    if (id >= MOTOR_TASK_NUM) return;

    s_motor_param[id].last_cmd = cmd->cmd;
    motor_dispatch(id, cmd->cmd, EVT_NONE, cmd);
}

// =========================
// 事件检测与周期性检查
// =========================

static void motor_periodic_check(void)
{
    // 组装方向数组
    uint8_t dir_array[MOTOR_TASK_NUM];
    for (uint8_t i = 0; i < MOTOR_TASK_NUM; i++) {
        dir_array[i] = s_motor_param[i].dir;
    }
    adc_data_process(dir_array);

    for (uint8_t i = 0; i < MOTOR_TASK_NUM; i++) {
        motor_task_param_t *p = &s_motor_param[i];
        // 更新位置、限位、错误
        if (p->dir != MOTOR_TASK_DIR_STOP) {
            motor_update_position(i);
            motor_check_limit(i);
            motor_check_error(i);
        }
        // 目标角度控制
        motor_target_angle_control(i);
    }
}

/**
 * @brief 限位检测
 */
static void motor_check_limit(uint8_t id)
{
    bool is_limit = false;

    uint16_t current = MOTOR_GET_CURRENT(id);
    if ((s_motor_param[id].dir != MOTOR_TASK_DIR_STOP) && (current < MOTOR_LIMIT_CURRENT_THRESHOLD) && (!s_motor_param[id].is_at_limit)) {
        MOTOR_LOG_INFO("motor_check_limit: id=%d, 限位触发, 电流=%u", id, current);
        motor_dispatch(id, s_motor_param[id].last_cmd, EVT_LIMIT, NULL);
        is_limit = true;
    }

    // 只有在初始复位时才会检查所有电机是否都复位完成
    if (is_limit && s_motor_param[id].is_homing) {
        is_limit                    = false;
        s_motor_param[id].is_homing = false;
        // 检查所有电机是否都复位完成
        bool all_done = true;
        for (uint8_t i = 0; i < MOTOR_TASK_NUM; i++) {
            if (s_motor_param[i].is_homing) {
                all_done = false;
                break;
            }
        }
        if (all_done) {
            MOTOR_LOG_INFO("所有电机复位完成");
            for (uint8_t i = 0; i < MOTOR_TASK_NUM; i++) {
                s_motor_param[i].state = MOTOR_STATE_IDLE;
            }
        }
    }
}

/**
 * @brief 错误检测（过流/堵转）
 */
static void motor_check_error(uint8_t id)
{
    // 过流保护（峰值）
    if (get_overcurrent_flag(id)) {
        MOTOR_LOG_ERROR("MOTOR[%d] 过流保护, 峰值=%u", id, get_peak_current(id));
        motor_dispatch(id, s_motor_param[id].last_cmd, EVT_OVERCURRENT, NULL);
        return;
    }
    // 堵转保护（积分标志）
    if (get_blocked_flag(id)) {
        MOTOR_LOG_ERROR("MOTOR[%d] 堵转保护, 标志位触发", id);
        motor_dispatch(id, s_motor_param[id].last_cmd, EVT_STALL, NULL);
        return;
    }
}

/**
 * @brief 目标角度控制逻辑，基于percent，极限角度强制到限位
 */
static void motor_target_angle_control(uint8_t id)
{
    if (s_motor_param[id].is_moving_to_angle && s_motor_param[id].is_calibrated && s_motor_param[id].max_angle > 0) {
        float cur_angle       = s_motor_param[id].angle;
        float target          = s_motor_param[id].target_angle;
        float tolerance       = 0.5f; // 角度误差容差0.5°
        float target_percent  = target / s_motor_param[id].max_angle;
        float current_percent = cur_angle / s_motor_param[id].max_angle;
        motor_task_param_t *p = &s_motor_param[id];
        if (p->force_limit) {
            // 只有到达限位（is_at_limit）才停止
            if (p->is_at_limit) {
                motor_stop(id);
                p->is_moving_to_angle = false;
                p->force_limit        = 0;
                MOTOR_LOG_INFO("MOTOR[%d] 强制到达限位点，已停止", id);
            }
        } else {
            if (fabsf(current_percent - target_percent) < (tolerance / s_motor_param[id].max_angle)) {
                motor_stop(id);
                p->is_moving_to_angle = false;
                MOTOR_LOG_INFO("MOTOR[%d] 到达目标角度 %.2f，当前角度 %.2f (percent: %.3f, 误差%.2f°)", id, target, cur_angle, current_percent, cur_angle - target);
            }
        }
        // 若遇到限位/过流/堵转等异常，也应终止移动
        if (s_motor_param[id].error != MOTOR_TASK_ERR_NONE || s_motor_param[id].is_at_limit) {
            s_motor_param[id].is_moving_to_angle = false;
            p->force_limit                       = 0;
            MOTOR_LOG_INFO("MOTOR[%d] 因异常或限位停止移动到角度 %.2f", id, target);
        }
    }
}

/**
 * @brief 更新电机当前位置（基于换相数进行行程和角度估算），并在到达限位时自学习行程时间
 *
 * @param id 电机ID
 *
 * 行程自学习说明：
 *   - 每次从底部到顶部或顶部到底部运动到限位时，自动更新up_time/down_time
 *   - 角度与位置线性换算，减少累计误差
 */
static void motor_update_position(uint8_t id)
{
    if (id >= MOTOR_TASK_NUM) return;
    motor_task_param_t *p = &s_motor_param[id];
    if (p->is_homing) return;
    // 获取当前换相计数
    uint16_t comm_count  = get_commutation_count(id);
    p->commutation_count = comm_count;
    // 分段累计：每次更新segment_commutations
    p->segment_commutations = comm_count;
    // 未校准或全行程换相数为0时不更新
    if (!s_motor_param[id].is_calibrated || (s_motor_param[id].up_travel_commutations == 0 && s_motor_param[id].down_travel_commutations == 0)) {
        p->position = 0;
        p->angle    = 0.0f;
        return;
    }

    float percent    = 0.0f;
    float avg_travel = (s_motor_param[id].up_travel_commutations + s_motor_param[id].down_travel_commutations) / 2.0f;

    // 根据限位状态和当前计数值计算位置
    if (p->is_at_limit) {
        if (p->limit_dir == MOTOR_TASK_DIR_UP) {
            percent = 1.0f;
        } else if (p->limit_dir == MOTOR_TASK_DIR_DOWN) {
            percent = 0.0f;
        }
    } else if (avg_travel > 0) {
        if (p->dir == MOTOR_TASK_DIR_UP) {
            percent = (float)(p->total_commutations + comm_count) / avg_travel;
        } else if (p->dir == MOTOR_TASK_DIR_DOWN) {
            percent = 1.0f - (float)(p->total_commutations + comm_count) / avg_travel;
        } else {
            percent = (float)p->position / 100.0f;
        }
        if (percent > 1.0f) percent = 1.0f;
        if (percent < 0.0f) percent = 0.0f;
    } else {
        percent = (float)p->position / 100.0f;
    }

    p->position = (uint32_t)(percent * 100.0f);
    p->angle    = percent * p->max_angle;

    // 每500ms记录一次详细日志，避免日志过多
    static uint32_t last_log_time[MOTOR_TASK_NUM] = {0};
    uint32_t now                                  = xTaskGetTickCount();
    if (now - last_log_time[id] > 500) {
        last_log_time[id] = now;
        MOTOR_LOG_DEBUG("位置更新: id=%d, 方向=%d, 换相计数=%u, 位置=%u, 角度=%.2f, 上行总计数=%u, 下行总计数=%u, 平均行程=%.1f",
                        id, p->dir, comm_count, p->position, p->angle,
                        s_motor_param[id].up_travel_commutations, s_motor_param[id].down_travel_commutations, avg_travel);
    }
}

// =========================
// 具体动作处理函数
// =========================

/**
 * @brief 向上运动命令处理
 * @param id 电机ID
 * @param cmd 命令
 */
static void motor_action_up(uint8_t id, const motor_task_cmd_t *cmd)
{
    // 限位状态下判断方向
    if (s_motor_param[id].is_at_limit) {
        if (s_motor_param[id].limit_dir == MOTOR_TASK_DIR_UP) {
            MOTOR_LOG_INFO("MOTOR[%d] 已在上限位，忽略上升命令", id);
            return;
        } else {
            // 反向操作，解除限位
            s_motor_param[id].is_at_limit = false;
        }
    }
    MOTOR_LOG_INFO("MOTOR[%d] CMD_UP", id);

    // 方向切换时分段累计合并并重置计数器
    if (s_motor_param[id].last_move_dir != MOTOR_TASK_DIR_UP) {
        s_motor_param[id].total_commutations += s_motor_param[id].segment_commutations;
        s_motor_param[id].segment_commutations = 0;
        s_motor_param[id].last_move_dir        = MOTOR_TASK_DIR_UP;
        commutation_count_reset(id);
        MOTOR_LOG_INFO("MOTOR[%d] 方向切换为上行，分段累计合并", id);
    }

    // 如果是从下限位开始向上运动，重置计数器
    if (s_motor_param[id].position == 0 ||
        (s_motor_param[id].is_at_limit && s_motor_param[id].limit_dir == MOTOR_TASK_DIR_DOWN)) {
        commutation_count_reset(id);
        MOTOR_LOG_INFO("MOTOR[%d] 从下限位开始向上运动，重置计数器", id);
    }

    s_motor_param[id].dir         = MOTOR_TASK_DIR_UP;
    s_motor_param[id].speed       = 100;
    s_motor_param[id].is_at_limit = false;
    MOTOR_PWM_SET(id, MOTOR_TASK_DIR_UP, s_motor_param[id].speed);
    s_motor_param[id].last_dir = MOTOR_TASK_DIR_UP;
}

/**
 * @brief 向下运动命令处理
 * @param id 电机ID
 * @param cmd 命令
 */
static void motor_action_down(uint8_t id, const motor_task_cmd_t *cmd)
{
    // 限位状态下判断方向
    if (s_motor_param[id].is_at_limit) {
        if (s_motor_param[id].limit_dir == MOTOR_TASK_DIR_DOWN) {
            MOTOR_LOG_INFO("MOTOR[%d] 已在下限位，忽略下降命令", id);
            return;
        } else {
            // 反向操作，解除限位
            s_motor_param[id].is_at_limit = false;
        }
    }
    MOTOR_LOG_INFO("MOTOR[%d] CMD_DOWN", id);

    // 方向切换时分段累计合并并重置计数器
    if (s_motor_param[id].last_move_dir != MOTOR_TASK_DIR_DOWN) {
        s_motor_param[id].total_commutations += s_motor_param[id].segment_commutations;
        s_motor_param[id].segment_commutations = 0;
        s_motor_param[id].last_move_dir        = MOTOR_TASK_DIR_DOWN;
        commutation_count_reset(id);
        MOTOR_LOG_INFO("MOTOR[%d] 方向切换为下行，分段累计合并", id);
    }

    // 如果是从上限位开始向下运动，重置计数器
    if (s_motor_param[id].position == 100 ||
        (s_motor_param[id].is_at_limit && s_motor_param[id].limit_dir == MOTOR_TASK_DIR_UP)) {
        commutation_count_reset(id);
        MOTOR_LOG_INFO("MOTOR[%d] 从上限位开始向下运动，重置计数器", id);
    }

    s_motor_param[id].dir         = MOTOR_TASK_DIR_DOWN;
    s_motor_param[id].speed       = 100;
    s_motor_param[id].is_at_limit = false;
    MOTOR_PWM_SET(id, MOTOR_TASK_DIR_DOWN, s_motor_param[id].speed);
    s_motor_param[id].last_dir = MOTOR_TASK_DIR_DOWN;
}

/**
 * @brief 停止命令处理
 * @param id 电机ID
 * @param cmd 命令
 */
static void motor_action_stop(uint8_t id, const motor_task_cmd_t *cmd)
{
    MOTOR_LOG_INFO("MOTOR[%d] CMD_STOP", id);
    motor_stop(id);
}

/**
 * @brief 移动到目标角度命令处理
 * @param id 电机ID
 * @param cmd 命令
 */
static void motor_action_move_to_angle(uint8_t id, const motor_task_cmd_t *cmd)
{
    MOTOR_LOG_INFO("MOTOR[%d] CMD_MOVE_TO_ANGLE: target=%.2f", id, cmd->data.angle);
    // 校准后，最大角度大于0
    if (s_motor_param[id].is_calibrated && s_motor_param[id].max_angle > 0) {
        float target = cmd->data.angle;
        // 限制目标角度范围
        if (target < 0) target = 0;
        if (target > s_motor_param[id].max_angle) target = s_motor_param[id].max_angle;
        // 设置目标角度
        s_motor_param[id].target_angle       = target;
        s_motor_param[id].is_moving_to_angle = true;
        float cur_angle                      = s_motor_param[id].angle;
        motor_task_param_t *p                = &s_motor_param[id];
        // 平均行程法下的目标percent
        float avg_travel      = (p->up_travel_commutations + p->down_travel_commutations) / 2.0f;
        float target_percent  = target / p->max_angle;
        float current_percent = cur_angle / p->max_angle;
        // 极限角度强制到限位
        if (target_percent <= 0.0f || target_percent >= 1.0f) {
            p->force_limit = 1;
        } else {
            p->force_limit = 0;
        }
        if (fabsf(target_percent - current_percent) < 0.01f && !p->force_limit) { // 1%容差
            MOTOR_LOG_INFO("MOTOR[%d] 已在目标角度1%%以内，无需移动", id);
            s_motor_param[id].is_moving_to_angle = false;
            return;
        }
        MOTOR_LOG_INFO("MOTOR[%d] 移动到角度 %.2f, 当前角度 %.2f (percent: %.3f -> %.3f, force_limit=%d)", id, target, cur_angle, current_percent, target_percent, p->force_limit);
        // 分段累计：方向切换时合并分段
        if (current_percent < target_percent) {
            if (p->last_move_dir != MOTOR_TASK_DIR_UP) {
                p->total_commutations += p->segment_commutations;
                p->segment_commutations = 0;
                p->last_move_dir        = MOTOR_TASK_DIR_UP;
                MOTOR_LOG_INFO("MOTOR[%d] 方向切换为上行，分段累计合并", id);
                commutation_count_reset(id); // 切换方向后重置硬件计数器
            }
            p->dir         = MOTOR_TASK_DIR_UP;
            p->speed       = 100;
            p->is_at_limit = false;
            MOTOR_PWM_SET(id, MOTOR_TASK_DIR_UP, p->speed);
        } else if (current_percent > target_percent) {
            if (p->last_move_dir != MOTOR_TASK_DIR_DOWN) {
                p->total_commutations += p->segment_commutations;
                p->segment_commutations = 0;
                p->last_move_dir        = MOTOR_TASK_DIR_DOWN;
                MOTOR_LOG_INFO("MOTOR[%d] 方向切换为下行，分段累计合并", id);
                commutation_count_reset(id); // 切换方向后重置硬件计数器
            }
            p->dir         = MOTOR_TASK_DIR_DOWN;
            p->speed       = 100;
            p->is_at_limit = false;
            MOTOR_PWM_SET(id, MOTOR_TASK_DIR_DOWN, p->speed);
        } else {
            p->is_moving_to_angle = false;
            motor_stop(id);
        }
    } else {
        MOTOR_LOG_ERROR("MOTOR[%d] 未校准或最大角度为0，无法移动到指定角度", id);
    }
}

/**
 * @brief 忽略无效命令
 * @param id 电机ID
 * @param cmd 命令
 */
static void motor_action_ignore(uint8_t id, const motor_task_cmd_t *cmd)
{
    // 忽略无效命令
    MOTOR_LOG_INFO("MOTOR[%d] 状态%u下忽略命令%u", id, s_motor_param[id].state, cmd ? cmd->cmd : 0);
}

/**
 * @brief 限位事件处理
 * @param id 电机ID
 * @param cmd 命令
 */
static void motor_action_limit(uint8_t id, const motor_task_cmd_t *cmd)
{
    bool save_flag            = false;
    motor_task_dir_t last_dir = s_motor_param[id].last_dir;
    motor_task_param_t *p     = &s_motor_param[id];
    MOTOR_LOG_INFO("MOTOR[%d] 限位触发, 方向=%d", id, last_dir);
    // 1. 记录限位方向
    p->limit_dir = last_dir;
    // 严格流程校准判断
    bool is_full_up   = (s_motor_last_limit_dir[id] == MOTOR_TASK_DIR_DOWN && last_dir == MOTOR_TASK_DIR_UP);
    bool is_full_down = (s_motor_last_limit_dir[id] == MOTOR_TASK_DIR_UP && last_dir == MOTOR_TASK_DIR_DOWN);
    if (is_full_up && !p->is_homing) {
        // 完整下->上流程，保存up_travel_commutations
        p->total_commutations += p->segment_commutations;
        commutation_count_reset(id);
        p->position                   = 100;
        p->angle                      = p->max_angle;
        uint16_t last_up_travel       = p->up_travel_commutations;
        uint16_t min_commutation_diff = (uint16_t)(MOTOR_ANGLE_TOLERANCE * p->total_commutations / p->max_angle + 0.5f);
        if (abs((int)p->total_commutations - (int)last_up_travel) > min_commutation_diff) {
            p->up_travel_commutations = p->total_commutations;
            save_flag                 = true;
            MOTOR_LOG_INFO("电机%d限位校准: 上行计数=%u (变化=%d, 阈值=%u, 角度容差=%.2f°)", id, p->up_travel_commutations, (int)p->total_commutations - (int)last_up_travel, min_commutation_diff, MOTOR_ANGLE_TOLERANCE);
        } else {
            MOTOR_LOG_INFO("电机%d上行校准变化小于阈值(%u计数≈%.2f°)，不更新", id, min_commutation_diff, MOTOR_ANGLE_TOLERANCE);
        }
        p->is_at_limit = true;
        p->state       = MOTOR_STATE_STOPPED;
        p->last_cmd    = MOTOR_TASK_CMD_STOP;
        motor_stop(id);
        // 平均行程法+限位归零：所有累计变量清零
        p->segment_commutations = 0;
        p->total_commutations   = 0;
        p->commutation_count    = 0;
    } else if (is_full_down && !p->is_homing) {
        // 完整上->下流程，保存down_travel_commutations
        p->total_commutations += p->segment_commutations;
        commutation_count_reset(id);
        p->position                   = 0;
        p->angle                      = 0.0f;
        uint16_t last_down_travel     = p->down_travel_commutations;
        uint16_t min_commutation_diff = (uint16_t)(MOTOR_ANGLE_TOLERANCE * p->total_commutations / p->max_angle + 0.5f);
        if (abs((int)p->total_commutations - (int)last_down_travel) > min_commutation_diff) {
            p->down_travel_commutations = p->total_commutations;
            save_flag                   = true;
            MOTOR_LOG_INFO("电机%d限位校准: 下行计数=%u (变化=%d, 阈值=%u, 角度容差=%.2f°)", id, p->down_travel_commutations, (int)p->total_commutations - (int)last_down_travel, min_commutation_diff, MOTOR_ANGLE_TOLERANCE);
        } else {
            MOTOR_LOG_INFO("电机%d下行校准变化小于阈值(%u计数≈%.2f°)，不更新", id, min_commutation_diff, MOTOR_ANGLE_TOLERANCE);
        }
        p->is_at_limit = true;
        p->state       = MOTOR_STATE_STOPPED;
        p->last_cmd    = MOTOR_TASK_CMD_STOP;
        motor_stop(id);
        // 平均行程法+限位归零：所有累计变量清零
        p->segment_commutations = 0;
        p->total_commutations   = 0;
        p->commutation_count    = 0;
    } else {
        // 不是完整流程，只做限位标志和停止，不保存
        commutation_count_reset(id);
        if (last_dir == MOTOR_TASK_DIR_UP) {
            p->position = 100;
            p->angle    = p->max_angle;
        } else if (last_dir == MOTOR_TASK_DIR_DOWN) {
            p->position = 0;
            p->angle    = 0.0f;
        }
        p->is_at_limit = true;
        p->state       = MOTOR_STATE_STOPPED;
        p->last_cmd    = MOTOR_TASK_CMD_STOP;
        motor_stop(id);
        // 平均行程法+限位归零：所有累计变量清零
        p->segment_commutations = 0;
        p->total_commutations   = 0;
        p->commutation_count    = 0;
    }
    if (save_flag) motor_save_param(id);
    // 4. 更新上次限位方向
    s_motor_last_limit_dir[id] = last_dir;
}

/**
 * @brief 过流事件处理
 * @param id 电机ID
 * @param cmd 命令
 */
static void motor_action_overcurrent(uint8_t id, const motor_task_cmd_t *cmd)
{
    MOTOR_LOG_ERROR("MOTOR[%d] 过流事件，进入错误状态", id);
    motor_stop(id);
    s_motor_param[id].error = MOTOR_TASK_ERR_OVERCURRENT;
}

/**
 * @brief 堵转事件处理
 * @param id 电机ID
 * @param cmd 命令
 */
static void motor_action_stall(uint8_t id, const motor_task_cmd_t *cmd)
{
    MOTOR_LOG_ERROR("MOTOR[%d] 堵转事件，进入错误状态", id);
    motor_stop(id);
    s_motor_param[id].error = MOTOR_TASK_ERR_STALL;
}

// =========================
// 目标角度控制、位置更新、外部API
// =========================

/**
 * @brief 电机停止
 *
 * @param id 电机ID
 */
static void motor_stop(uint8_t id)
{
    s_motor_param[id].dir   = MOTOR_TASK_DIR_STOP;
    s_motor_param[id].speed = 0;
    MOTOR_PWM_SET(id, MOTOR_TASK_DIR_STOP, 0);
}

/**
 * @brief 发送电机控制命令
 *
 * @param cmd 命令
 */
void motor_task_send_cmd(const motor_task_cmd_t *cmd)
{
    if (s_motor_cmd_queue) {
        xQueueSend(s_motor_cmd_queue, cmd, 0);
    }
}

/**
 * @brief 设置指定电机速度
 *
 * @param motor_id 电机ID
 * @param speed 速度百分比
 */
void motor_task_set_speed(uint8_t motor_id, uint8_t speed)
{
    if (motor_id < MOTOR_TASK_NUM) {
        s_motor_param[motor_id].speed = speed;
        if (s_motor_param[motor_id].dir != MOTOR_TASK_DIR_STOP) {
            MOTOR_PWM_SET(motor_id, s_motor_param[motor_id].dir, speed);
        }
    }
}

/**
 * @brief 获取指定电机当前运动方向
 *
 * @param motor_id 电机ID
 * @return 当前方向
 */
motor_task_dir_t motor_task_get_dir(uint8_t motor_id)
{
    if (motor_id < MOTOR_TASK_NUM) {
        return s_motor_param[motor_id].dir;
    }
    return MOTOR_TASK_DIR_STOP;
}

/**
 * @brief 获取指定电机当前位置
 *
 * @param motor_id 电机ID
 * @return 当前位置
 */
uint32_t motor_task_get_position(uint8_t motor_id)
{
    if (motor_id < MOTOR_TASK_NUM) {
        return s_motor_param[motor_id].position;
    }
    return 0;
}

/**
 * @brief 获取指定电机当前角度
 *
 * @param motor_id 电机ID
 * @return 当前角度
 */
float motor_task_get_angle(uint8_t motor_id)
{
    if (motor_id < MOTOR_TASK_NUM) {
        return s_motor_param[motor_id].angle;
    }
    return 0.0f;
}

/**
 * @brief 获取指定电机当前错误状态
 *
 * @param motor_id 电机ID
 * @return 错误类型
 */
motor_task_err_t motor_task_get_error(uint8_t motor_id)
{
    if (motor_id < MOTOR_TASK_NUM) {
        return s_motor_param[motor_id].error;
    }
    return MOTOR_TASK_ERR_NONE;
}

/**
 * @brief 清除指定电机错误状态
 *
 * @param motor_id 电机ID
 */
void motor_task_clear_error(uint8_t motor_id)
{
    if (motor_id < MOTOR_TASK_NUM) {
        s_motor_param[motor_id].error = MOTOR_TASK_ERR_NONE;
        s_motor_param[motor_id].state = MOTOR_STATE_IDLE;
    }
}
