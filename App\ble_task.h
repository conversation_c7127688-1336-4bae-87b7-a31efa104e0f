/*
 * File:    ble_task.h
 * Author:  wqearth
 * Date:    2024-06-09
 * Version: v1.0
 * Brief:   蓝牙模块（FreeRTOS版本）头文件
 *          从QP AO迁移为FreeRTOS任务/事件队列实现
 */
#ifndef BLE_TASK_H
#define BLE_TASK_H

#include "app_config.h"
#include "app_log.h"
#include "bsp.h"

// 事件类型定义
typedef enum {
    BLE_EVENT_UART_RX = 0,     // 串口数据接收事件
    BLE_EVENT_TIMEOUT,         // 通用超时事件
    BLE_EVENT_TICK,            // 周期性tick事件（如按键计时等）
    BLE_EVENT_PAIRING_TIMEOUT, // 配对窗口超时
    BLE_EVENT_KEY,             // 按键事件
    BLE_EVENT_PAIRING,         // 配对事件（手动触发）
    BLE_EVENT_DISCONNECT,      // 断开连接事件
    BLE_EVENT_ERROR,           // 错误事件
    BLE_EVENT_USER,            // 用户自定义事件
} ble_event_type_e;

// 蓝牙主状态定义
typedef enum {
    BLE_STATE_IDLE = 0,  // 空闲
    BLE_STATE_PAIRING,   // 配对中
    BLE_STATE_CONNECTED, // 已连接
    BLE_STATE_ERROR      // 错误
} ble_state_e;

// 蓝牙错误码定义
typedef enum {
    BLE_ERROR_NONE = 0,    // 无错误
    BLE_ERROR_TIMEOUT,     // 超时
    BLE_ERROR_CHECKSUM,    // 校验和错误
    BLE_ERROR_FRAME,       // 帧格式错误
    BLE_ERROR_BUFFER,      // 缓冲区错误
    BLE_ERROR_STATE,       // 状态错误
    BLE_ERROR_MEMORY,      // 内存分配错误
    BLE_ERROR_MAC_MISMATCH // MAC地址不匹配
} ble_error_e;

// 按键状态定义
typedef enum {
    KEY_STA_IDLE = 0,     // 按键空闲
    KEY_STA_DOWN,         // 按键按下
    KEY_STA_UP,           // 按键抬起
    KEY_STA_SHORTPRESSED, // 短按
    KEY_STA_LONGPRESSED   // 长按
} key_sta_e;

// 蓝牙数据帧结构体
#pragma pack(1)
typedef struct {
    uint16_t head;      // 帧头0xAA55
    uint8_t mac[6];     // 遥控器MAC地址
    uint8_t len;        // 帧长度
    uint32_t key_value; // 按键值
    key_sta_e key_sta;  // 按键状态
    uint8_t sum;        // 校验和
    uint8_t tail;       // 帧尾0xFE
} ble_frame_t;
#pragma pack()

// 蓝牙配置参数结构体
typedef struct {
    uint32_t pairing_timeout; // 配对超时时间（秒）
    uint32_t baudrate;        // 串口波特率
} ble_config_t;

// 蓝牙任务主上下文结构体
typedef struct {
    ble_state_e state;                    // 当前状态
    ble_error_e error;                    // 当前错误码
    uint8_t is_paired : 1;                // 是否已配对
    uint8_t is_light_on : 1;              // 灯光开关状态
    uint8_t is_pair_key_pressed : 1;      // 配对组合键是否按下
    uint8_t is_pairing_window_active : 1; // 是否处于配对窗口期
    uint8_t reserved : 4;                 // 保留字段
    uint32_t pair_key_press_time;         // 配对组合键按下计时（ms）
    uint32_t pair_key_threshold;          // 配对组合键长按阈值（ms）
    uint8_t mac_addr[6];                  // 已配对MAC地址
    ble_config_t config;                  // 配置参数
    ble_frame_t rx_frame;                 // 接收帧缓冲区
    QueueHandle_t event_queue;            // 事件队列
    TimerHandle_t pairing_timer;          // 配对窗口定时器
    TimerHandle_t tick_timer;             // 周期tick定时器
    TimerHandle_t save_param_timer;       // 参数保存定时器
    ring_buf_t *uart_rx_rb;               // 串口1接收缓冲区指针
    SemaphoreHandle_t mutex;              // 上下文互斥锁
} ble_task_ctx_t;

// 事件消息结构体
typedef struct {
    ble_event_type_e type; // 事件类型
    union {
        ble_frame_t frame; // 数据帧
        struct {
            uint32_t key_value;
            uint8_t key_sta;
        } key;               // 按键信息
        ble_error_e error;   // 错误码
        uint32_t user_param; // 用户参数
    } data;
} ble_event_msg_t;

/**
 * @brief 蓝牙帧头和帧尾定义
 */
#define BLE_FRAME_HEAD                  0xAA55 /**< 蓝牙帧头标识 */
#define BLE_FRAME_TAIL                  0xFE   /**< 蓝牙帧尾标识 */

/**
 * @brief 配对长按阈值定义
 */
#define BLE_PAIR_LONGPRESS_THRESHOLD_MS 3000 /**< 配对长按阈值(ms) */

/**
 * @brief 遥控器按键定义
 */
#define REMOTE_KEY_LOCK                 (1 << 0)  // 锁定
#define REMOTE_KEY_PAIR                 (1 << 1)  // 对码
#define REMOTE_KEY_LIGHT                (1 << 2)  // 灯光
#define REMOTE_KEY_CUSTOMMODE1          (1 << 3)  // 自定义模式1
#define REMOTE_KEY_CUSTOMMODE2          (1 << 4)  // 自定义模式2
#define REMOTE_KEY_SPACE                (1 << 5)  // 太空模式
#define REMOTE_KEY_MOVIE                (1 << 6)  // 观影模式
#define REMOTE_KEY_LEGDOWN              (1 << 7)  // 腿部下降
#define REMOTE_KEY_HEADUP               (1 << 8)  // 头部上升
#define REMOTE_KEY_LEGUP                (1 << 9)  // 腿部上升
#define REMOTE_KEY_FLAT                 (1 << 10) // 平躺模式
#define REMOTE_KEY_SLEEP                (1 << 11) // 睡眠模式
#define REMOTE_KEY_HEADDOWN             (1 << 12) // 头部下降
#define REMOTE_KEY_READ                 (1 << 13) // 阅读模式
#define REMOTE_KEY_BACKMASSAGE          (1 << 14) // 背部按摩
#define REMOTE_KEY_MASSAGETIMER         (1 << 15) // 按摩定时器
#define REMOTE_KEY_LEGMASSAGE           (1 << 16) // 腿部按摩
#define REMOTE_KEY_MASSAGEMODE          (1 << 17) // 按摩模式
#define REMOTE_KEY_CUSTOMMODE3          (1 << 18) // 自定义模式3
#define REMOTE_KEY_MASSAGEPOWER         (1 << 19) // 按摩电源

#define REMOTE_KEY_CUSTOMMODE1_COMBO    (REMOTE_KEY_CUSTOMMODE1 | REMOTE_KEY_MASSAGEMODE) // 自定义模式1上升
#define REMOTE_KEY_CUSTOMMODE2_COMBO    (REMOTE_KEY_CUSTOMMODE2 | REMOTE_KEY_MASSAGEMODE) // 自定义模式2上升
#define REMOTE_KEY_CUSTOMMODE3_COMBO    (REMOTE_KEY_CUSTOMMODE3 | REMOTE_KEY_MASSAGEMODE) // 自定义模式3上升

/**
 * @brief 配对组合键定义
 */
#define REMOTE_PAIRCOMBO_KEY            (REMOTE_KEY_PAIR | REMOTE_KEY_MASSAGEMODE) // 配对组合键

// 外部接口声明（与原AO接口一致，适配FreeRTOS实现）
void ble_task_init(void);
ble_error_e ble_get_error(void);
void ble_clear_error(void);
uint8_t get_pairing_status(void);

#endif // BLE_TASK_H