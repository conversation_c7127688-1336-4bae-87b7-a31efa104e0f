#ifndef __BSP_PWM_H__
#define __BSP_PWM_H__

#include "config.h"

#define DEFAULT_COMPVALUE 0

#define PWM_PERIOD        (3200 - 1) // 定时器频率15KHz

// 蜂鸣器PWM配置参数
#define BUZZER_PWM_FREQ   4000                             // 蜂鸣器频率4kHz
#define BUZZER_PWM_PERIOD (12000000 / BUZZER_PWM_FREQ - 1) // 12MHz/4kHz = 3000 - 1 = 2999

enum pwm_port {
    PWM11 = 0, // 背部上升
    PWM12,     // 背部下降
    PWM21,     // 腰部上升
    PWM22,     // 腰部下降
    PWM51,     // 腿部1上升
    PWM52,     // 腿部1下降
    PWM41,     // 腿部2上升
    PWM42,     // 腿部2下降

    PWM31, // 震动1
    PWM32, // 震动2
    PWM53, // 震动3
    PWM54, // 震动4
    PWM61, // G
    PWM62, // B
    PWM63, // R
};

extern void bsp_PwmInit(void);
extern void bsp_SetPwmDuty(unsigned char port, unsigned short duty); // 设置通道占空比百分比，范围：0-100
extern void soft_pwm_update(void);

#endif