#include "msg_task.h"
#include "bed_control.h"
#include "vibr_task.h"

#define CONTROL_MSG_QUEUE_SIZE 5

static QueueHandle_t control_msg_queue;

// ================== 单色RGB灯条开关状态变量 ==================
static bool s_rgb_on = false; // false: 关闭, true: 打开

/**
 * @brief 处理控制消息
 * @param msg 控制消息
 */
static void msg_handle(const control_msg_t *msg)
{
    if (!msg) return;
    switch (msg->cmd) {
        case CTRL_CMD_HEAD:
        case CTRL_CMD_BACK:
            if (msg->param.bed_move.action == BED_MOVE_UP) {
                bed_control_up((uint8_t)msg->param.bed_move.part);
            } else if (msg->param.bed_move.action == BED_MOVE_DOWN) {
                bed_control_down((uint8_t)msg->param.bed_move.part);
            } else if (msg->param.bed_move.action == BED_MOVE_STOP) {
                bed_control_stop((uint8_t)msg->param.bed_move.part);
            }
            break;

        case CTRL_CMD_WAIST:
            if (msg->param.bed_move.action == BED_MOVE_UP) {
                bed_control_up((uint8_t)msg->param.bed_move.part);
            } else if (msg->param.bed_move.action == BED_MOVE_DOWN) {
                bed_control_down((uint8_t)msg->param.bed_move.part);
            } else if (msg->param.bed_move.action == BED_MOVE_STOP) {
                bed_control_stop((uint8_t)msg->param.bed_move.part);
            }
            break;

        case CTRL_CMD_LEG:
            if (msg->param.bed_move.action == BED_MOVE_UP) {
                bed_control_up((uint8_t)msg->param.bed_move.part);
            } else if (msg->param.bed_move.action == BED_MOVE_DOWN) {
                bed_control_down((uint8_t)msg->param.bed_move.part);
            } else if (msg->param.bed_move.action == BED_MOVE_STOP) {
                bed_control_stop((uint8_t)msg->param.bed_move.part);
            }
            break;

        case CTRL_CMD_FULL:
            break;

        case CTRL_CMD_STOP:
            break;

        case CTRL_CMD_WORK_MODE:
            bed_control_set_mode((uint8_t)msg->param.value);
            break;

        case CTRL_CMD_MEMORY:
            if (msg->param.value == SET_MEMORY1) {
                bed_control_set_custom_mode(0);
            } else if (msg->param.value == SET_MEMORY2) {
                bed_control_set_custom_mode(1);
            } else if (msg->param.value == SET_MEMORY3) {
                bed_control_set_custom_mode(2);
            } else if (msg->param.value == GET_MEMORY1) {
                bed_control_get_custom_mode(0);
            } else if (msg->param.value == GET_MEMORY2) {
                bed_control_get_custom_mode(1);
            } else if (msg->param.value == GET_MEMORY3) {
                bed_control_get_custom_mode(2);
            }
            break;

        case CTRL_CMD_SMART_MODE:
            break;

        case CTRL_CMD_LIGHT:
            if (msg->param.value == RGB_ON) {
                s_rgb_on = false;
            } else if (msg->param.value == RGB_OFF) {
                s_rgb_on = true;
            }
            if (!s_rgb_on) {
                bed_control_rgb_on();
                s_rgb_on = true;
            } else {
                bed_control_rgb_off();
                s_rgb_on = false;
            }
            break;

        case CTRL_CMD_MASSAGE_POWER:
            if (msg->src == MSG_SRC_BLE) {
                bed_control_massage_power_ctrl();
            } else if (msg->src == MSG_SRC_NET) {
                if (msg->param.value == MASSAGE_POWER_OFF) {
                    bed_control_set_massage_power(false);
                }
            }
            break;
        case CTRL_CMD_MASSAGE_STRENGTH:
            if (msg->src == MSG_SRC_NET) {
                if (msg->param.massage.strength == MASSAGE_STRENGTH_1) {
                    bed_control_set_massage_strength(msg->param.massage.part, VIBR_LEVEL_LOW);
                } else if (msg->param.massage.strength == MASSAGE_STRENGTH_2) {
                    bed_control_set_massage_strength(msg->param.massage.part, VIBR_LEVEL_MEDIUM);
                } else if (msg->param.massage.strength == MASSAGE_STRENGTH_3) {
                    bed_control_set_massage_strength(msg->param.massage.part, VIBR_LEVEL_HIGH);
                }
                bed_control_set_massage_power(true);
            }
            break;
        case CTRL_CMD_MASSAGE_BACK_STRENGTH:
            if (msg->src == MSG_SRC_BLE) {
                bed_control_back_massage_strength_ctrl();
            } else if (msg->src == MSG_SRC_NET) {
                if (msg->param.value == MASSAGE_STRENGTH_1) {
                    bed_control_set_massage_strength(MASSAGE_PART_BACK, VIBR_LEVEL_LOW);
                } else if (msg->param.value == MASSAGE_STRENGTH_2) {
                    bed_control_set_massage_strength(MASSAGE_PART_BACK, VIBR_LEVEL_MEDIUM);
                } else if (msg->param.value == MASSAGE_STRENGTH_3) {
                    bed_control_set_massage_strength(MASSAGE_PART_BACK, VIBR_LEVEL_HIGH);
                }
            }
            break;
        case CTRL_CMD_MASSAGE_LEG_STRENGTH:
            if (msg->src == MSG_SRC_BLE) {
                bed_control_leg_massage_strength_ctrl();
            } else if (msg->src == MSG_SRC_NET) {
                if (msg->param.value == MASSAGE_STRENGTH_1) {
                    bed_control_set_massage_strength(MASSAGE_PART_LEG, VIBR_LEVEL_LOW);
                } else if (msg->param.value == MASSAGE_STRENGTH_2) {
                    bed_control_set_massage_strength(MASSAGE_PART_LEG, VIBR_LEVEL_MEDIUM);
                } else if (msg->param.value == MASSAGE_STRENGTH_3) {
                    bed_control_set_massage_strength(MASSAGE_PART_LEG, VIBR_LEVEL_HIGH);
                }
            }
            break;
        case CTRL_CMD_MASSAGE_MODE:
            if (msg->src == MSG_SRC_BLE) {
                bed_control_massage_mode_ctrl();
            } else if (msg->src == MSG_SRC_NET) {
                if (msg->param.massage.time != 0) {
                    bed_control_set_massage_time(msg->param.massage.part, msg->param.massage.time * 60000, true);
                }
                if (msg->param.massage.mode == MASSAGE_MODE_CONSTANT) {
                    bed_control_set_massage_mode(msg->param.massage.part, VIBR_MODE_CONTINUOUS);
                } else if (msg->param.massage.mode == MASSAGE_MODE_PULSE1) {
                    bed_control_set_massage_mode(msg->param.massage.part, VIBR_MODE_PULSE1);
                } else if (msg->param.massage.mode == MASSAGE_MODE_PULSE2) {
                    bed_control_set_massage_mode(msg->param.massage.part, VIBR_MODE_PULSE2);
                } else if (msg->param.massage.mode == MASSAGE_MODE_PULSE3) {
                    bed_control_set_massage_mode(msg->param.massage.part, VIBR_MODE_PULSE3);
                }
            }
            break;
        case CTRL_CMD_MASSAGE_TIME:
            if (msg->src == MSG_SRC_BLE) {
                bed_control_massage_time_ctrl();
            }
            break;

        default:
            // 非床位相关指令可忽略或记录日志
            break;
    }
}

/**
 * @brief 消息处理任务
 *
 * @param pvParameters
 */
static void msg_task_entry(void *pvParameters)
{
    control_msg_t msg;
    while (1) {
        if (xQueueReceive(control_msg_queue, &msg, portMAX_DELAY) == pdPASS) {
            MSG_LOG_INFO("control_msg_task_entry: 收到消息 src=%d cmd=%d param=%lu", msg.src, msg.cmd, msg.param);
            switch (msg.cmd) {
                case CTRL_CMD_HEAD:
                case CTRL_CMD_BACK:
                case CTRL_CMD_WAIST:
                case CTRL_CMD_LEG:
                case CTRL_CMD_STOP:
                case CTRL_CMD_WORK_MODE:
                case CTRL_CMD_MEMORY:
                case CTRL_CMD_SMART_MODE:
                case CTRL_CMD_LIGHT:
                case CTRL_CMD_MASSAGE_POWER:
                case CTRL_CMD_MASSAGE_BACK_STRENGTH:
                case CTRL_CMD_MASSAGE_LEG_STRENGTH:
                case CTRL_CMD_MASSAGE_MODE:
                case CTRL_CMD_MASSAGE_TIME:
                case CTRL_CMD_FULL:
                    msg_handle(&msg);
                    break;
                default:
                    MSG_LOG_WARN("control_msg_task_entry: 未知或未处理的cmd=%d", msg.cmd);
                    break;
            }
        }
    }
}

/**
 * @brief 消息处理任务初始化
 */
void msg_task_init(void)
{
    if (control_msg_queue == NULL) {
        control_msg_queue = xQueueCreate(CONTROL_MSG_QUEUE_SIZE, sizeof(control_msg_t));
    }
    BaseType_t xReturn = xTaskCreate(msg_task_entry,
                                     "msg_task",
                                     APP_MSG_TASK_STACK_SIZE,
                                     NULL,
                                     APP_MSG_PRIO,
                                     NULL);
    if (xReturn != pdPASS) {
        MSG_LOG_ERROR("msg_task_init: xTaskCreate failed");
    }
}

/**
 * @brief 投递消息到控制消息队列
 *
 * @param msg
 */
void msg_send(control_msg_t *msg)
{
    if (control_msg_queue != NULL) {
        xQueueSend(control_msg_queue, msg, portMAX_DELAY);
    }
}
