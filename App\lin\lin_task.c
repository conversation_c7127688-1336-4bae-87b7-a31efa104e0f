/**
 * @file lin_task.c
 * <AUTHOR> (<EMAIL>)
 * @brief LIN主机任务实现
 * @version 0.1
 * @date 2025-05-10
 *
 * @copyright Copyright (c) 2025
 *
 */

#include "lin_task.h"
#include "protocol.h"

static lin_mode_t g_lin_mode          = LIN_MODE_MASTER;
TaskHandle_t g_lin_master_task_handle = NULL;

static lin_schedule_entry_t g_lin_schedule_table[LIN_SCHEDULE_TABLE_SIZE] = {0};

static lin_callback_entry_t g_lin_callbacks[LIN_MAX_CALLBACKS] = {0};
static lin_msg_callback_t g_lin_default_callback               = NULL;

static lin_long_frame_send_t s_long_frame_send = {0};

static uint8_t lin_slave_send_frame_buf[28] = {0};
static uint8_t lin_slave_send_frame_len      = 0;

// ===================== 优化版子函数声明 =====================
static bool lin_entry_is_valid(const lin_schedule_entry_t *entry);
static bool lin_entry_need_schedule(lin_schedule_entry_t *entry, uint32_t now_tick);
static void lin_entry_execute(lin_schedule_entry_t *entry, uint32_t now_tick);
static void lin_entry_handle_error(lin_schedule_entry_t *entry, lin_error_type_t err_type);

/**
 * @brief 注册指定msg_id的回调
 * @param msg_id LIN消息ID
 * @param cb 回调函数指针
 */
void lin_register_msgid_callback(uint8_t msg_id, lin_msg_callback_t cb)
{
    for (int i = 0; i < LIN_MAX_CALLBACKS; i++) {
        if (g_lin_callbacks[i].cb == NULL || g_lin_callbacks[i].msg_id == msg_id) {
            g_lin_callbacks[i].msg_id = msg_id;
            g_lin_callbacks[i].cb     = cb;
            return;
        }
    }
    // 可加日志：回调表已满
    LIN_LOG_ERROR("LIN callback table is full");
}

/**
 * @brief 注册默认回调
 * @param cb 回调函数指针
 */
void lin_register_default_callback(lin_msg_callback_t cb)
{
    g_lin_default_callback = cb;
}

/**
 * @brief 按消息ID分发回调
 * @param msg 消息
 */
static void lin_invoke_callback(const lin_schedule_entry_t *entry)
{
    for (int i = 0; i < LIN_MAX_CALLBACKS; i++) {
        if (g_lin_callbacks[i].cb && g_lin_callbacks[i].msg_id == entry->msg_id) {
            g_lin_callbacks[i].cb(entry);
            return;
        }
    }
    if (g_lin_default_callback) {
        g_lin_default_callback(entry);
    }
}

/**
 * @brief 初始化调度表
 *
 */
static void lin_schedule_table_init(void)
{
    memset(g_lin_schedule_table, 0, sizeof(g_lin_schedule_table));
}

/**
 * @brief LIN消息接收函数
 *
 * @param msg 消息结构体
 * @param expect_len 期望接收的最大长度（包括校验和）
 * @param timeout_ms 超时时间
 * @return 接收成功返回true，失败返回false
 */
static bool lin_receive_msg(lin_msg_t *msg, uint8_t expect_len, uint32_t timeout_ms)
{
    if (!msg) return false;

    // 最大接收9字节（8数据字节+1校验和），最小接收2字节（1数据字节+1校验和）
    uint8_t buf[9] = {0};
    int rlen       = bsp_lin_master_read(buf, expect_len, timeout_ms);

    if (rlen > 1) { // 至少要有1个数据字节和1个校验和字节
        // 实际数据长度为接收长度减1（减去校验和）
        msg->len = rlen - 1;
        // 复制数据部分（不包括校验和）
        memcpy(msg->data, buf, msg->len);
        // 将校验和单独保存到校验和字段
        msg->checksum = buf[rlen - 1];
        return true;
    }
    return false;
}

/**
 * @brief LIN消息发送函数
 *
 * @param msg
 * @return
 */
static bool lin_send_msg(const lin_msg_t *msg)
{
    if (!msg) return false;
    int ret = bsp_lin_master_send_frame(msg->id, msg->data, msg->len);
    return (ret == 0);
}

/**
 * @brief 校验接收到的校验和是否正确
 *
 * @param id 消息ID
 * @param data 数据数组
 * @param len 数据长度
 * @param checksum 接收到的校验和
 * @return 校验通过返回true，否则返回false
 */
static bool lin_check_checksum(uint8_t id, const uint8_t *data, uint8_t len, uint8_t checksum)
{
    uint8_t calculated_checksum = lin_calculate_checksum(id, data, len);

    return (calculated_checksum == checksum);
}

/**
 * @brief 动态调度API实现
 *
 * @param entry
 * @return
 */
int lin_add_schedule_entry(const lin_schedule_entry_t *entry)
{
    for (int i = 0; i < LIN_SCHEDULE_TABLE_SIZE; i++) {
        if (!g_lin_schedule_table[i].valid) {
            memcpy(&g_lin_schedule_table[i], entry, sizeof(lin_schedule_entry_t));
            g_lin_schedule_table[i].valid = 1;
            return 0;
        }
    }
    return -1;
}

/**
 * @brief 删除调度表项
 *
 * @param msg_id
 * @return
 */
int lin_remove_schedule_entry(uint8_t msg_id)
{
    for (int i = 0; i < LIN_SCHEDULE_TABLE_SIZE; i++) {
        if (g_lin_schedule_table[i].valid && g_lin_schedule_table[i].msg_id == msg_id) {
            memset(&g_lin_schedule_table[i], 0, sizeof(lin_schedule_entry_t));
            g_lin_schedule_table[i].valid = 0;
            return 0;
        }
    }
    return -1;
}

/**
 * @brief 修改调度表项
 *
 * @param msg_id
 * @param entry
 * @return
 */
int lin_modify_schedule_entry(uint8_t msg_id, const lin_schedule_entry_t *entry)
{
    for (int i = 0; i < LIN_SCHEDULE_TABLE_SIZE; i++) {
        if (g_lin_schedule_table[i].valid && g_lin_schedule_table[i].msg_id == msg_id) {
            memcpy(&g_lin_schedule_table[i], entry, sizeof(lin_schedule_entry_t));
            g_lin_schedule_table[i].valid = 1;
            return 0;
        }
    }
    return -1;
}

/**
 * @brief 触发事件帧
 *
 * @param msg_id
 */
void lin_trigger_event_frame(uint8_t msg_id)
{
    for (int i = 0; i < LIN_SCHEDULE_TABLE_SIZE; i++) {
        if (g_lin_schedule_table[i].msg_id == msg_id &&
            g_lin_schedule_table[i].frame_type == LIN_FRAME_EVENT) {
            g_lin_schedule_table[i].event_flag = 1;
            break;
        }
    }
}

/**
 * @brief 设置调度周期
 *
 * @param msg_id
 * @param new_period_ms
 */
void lin_set_schedule_period(uint8_t msg_id, uint16_t new_period_ms)
{
    for (int i = 0; i < LIN_SCHEDULE_TABLE_SIZE; i++) {
        if (g_lin_schedule_table[i].msg_id == msg_id) {
            g_lin_schedule_table[i].period_ms = new_period_ms;
            break;
        }
    }
}

/**
 * @brief 判断调度表项是否有效
 *
 * @param entry 调度表项
 * @return
 */
static bool lin_entry_is_valid(const lin_schedule_entry_t *entry)
{
    return entry->valid == 1;
}

/**
 * @brief 判断是否需要调度
 *
 * @param entry 调度表项
 * @param now_tick 当前时间戳
 * @return
 */
static bool lin_entry_need_schedule(lin_schedule_entry_t *entry, uint32_t now_tick)
{
    switch (entry->frame_type) {
        case LIN_FRAME_EVENT:
            if (entry->event_flag) {
                entry->event_flag = 0;
                return true;
            }
            break;
        case LIN_FRAME_SPORADIC:
            if (entry->need_exec) {
                entry->need_exec = false;
                return true;
            }
            break;
        case LIN_FRAME_UNCONDITIONAL:
            if ((now_tick - entry->last_tick) >= pdMS_TO_TICKS(entry->period_ms)) {
                return true;
            }
            break;
        default:
            break;
    }
    return false;
}

/**
 * @brief 执行调度表项
 *
 * @param entry 调度表项
 * @param now_tick 当前时间戳
 */
static void lin_entry_execute(lin_schedule_entry_t *entry, uint32_t now_tick)
{
    lin_msg_t msg    = {0};
    lin_msg_t rx_msg = {0};
    msg.id           = entry->msg_id;

    if (entry->op_type == LIN_OP_WRITE) {
        memcpy(msg.data, entry->write_data, entry->write_len);
        msg.len            = entry->write_len;
        bool ret           = lin_send_msg(&msg);
        entry->last_result = ret;
        entry->error_type  = ret ? LIN_ERR_NONE : LIN_ERR_UNKNOWN;
        if (!ret) entry->error_count++;
    } else if (entry->op_type == LIN_OP_READ) {
        bool send_ok = lin_send_msg(&msg);
        if (!send_ok) {
            lin_entry_handle_error(entry, LIN_ERR_UNKNOWN);
        } else {
            if (lin_receive_msg(&rx_msg, entry->expect_len + 1, entry->response_timeout_ms)) {
                // 使用增强校验和检查（LIN 2.0协议）
                bool checksum_ok = lin_check_checksum(msg.id, rx_msg.data, rx_msg.len, rx_msg.checksum);
                if (checksum_ok) {
                    // 将接收到的有效数据（不包含校验和）复制到entry
                    memcpy(entry->read_data, rx_msg.data, rx_msg.len);
                    entry->read_len    = rx_msg.len;
                    entry->last_result = true;
                    entry->error_type  = LIN_ERR_NONE;

                    // 按消息ID分发回调
                    lin_invoke_callback(entry);
                } else {
                    lin_entry_handle_error(entry, LIN_ERR_CHECKSUM);
                }
            } else {
                lin_entry_handle_error(entry, LIN_ERR_TIMEOUT);
            }
        }
    }
    entry->last_tick = now_tick;
}

/**
 * @brief 处理错误
 *
 * @param entry 调度表项
 * @param err_type 错误类型
 */
static void lin_entry_handle_error(lin_schedule_entry_t *entry, lin_error_type_t err_type)
{
    entry->last_result = false;
    entry->error_type  = err_type;
    entry->error_count++;

    switch (err_type) {
        case LIN_ERR_CHECKSUM:
            LIN_LOG_ERROR("LIN校验和错误: ID=0x%02X, 从机ID=0x%02X, 错误计数=%d",
                          entry->msg_id, entry->slave_id, entry->error_count);
            break;
        case LIN_ERR_TIMEOUT:
            LIN_LOG_ERROR("LIN超时错误: ID=0x%02X, 从机ID=0x%02X, 超时时间=%dms, 错误计数=%d",
                          entry->msg_id, entry->slave_id, entry->response_timeout_ms, entry->error_count);
            break;
        default:
            LIN_LOG_ERROR("LIN未知错误: ID=0x%02X, 从机ID=0x%02X, 错误计数=%d",
                          entry->msg_id, entry->slave_id, entry->error_count);
            break;
    }
}

/**
 * @brief 发送长帧（分段响应主机读取，每次8字节，发送完自动清零）
 * 新协议：第0字节=总包数，第1字节=包号，2~7字节为数据
 * @param id 从机ID
 * @param frame_buf 帧数据（首次调用时传入，后续可为NULL）
 * @param frame_len 帧长度（首次调用时传入，后续可为0）
 * @return 0: 继续发送，1: 发送完毕，-1: 失败
 */
int lin_slave_send_long_frame(uint8_t id, const uint8_t *frame_buf, uint16_t frame_len)
{
    // 新协议每包6字节数据
    const uint8_t DATA_PER_PACKET = 6;
    // 第一次调用，初始化长帧发送状态
    if (!s_long_frame_send.sending && frame_buf != NULL && frame_len > 0) {
        s_long_frame_send.id = id;
        memcpy(s_long_frame_send.buf, frame_buf, frame_len);
        s_long_frame_send.total_len = frame_len;
        s_long_frame_send.offset    = 0;
        s_long_frame_send.sending   = 1;
    }

    if (s_long_frame_send.sending && s_long_frame_send.id == id) {
        uint8_t total_packets = (s_long_frame_send.total_len + DATA_PER_PACKET - 1) / DATA_PER_PACKET;
        uint8_t packet_index  = (s_long_frame_send.offset / DATA_PER_PACKET) + 1;
        uint8_t send_buf[8]   = {0};
        send_buf[0]           = total_packets;
        send_buf[1]           = packet_index;
        uint8_t remain        = s_long_frame_send.total_len - s_long_frame_send.offset;
        uint8_t copy_len      = (remain > DATA_PER_PACKET) ? DATA_PER_PACKET : remain;
        memcpy(&send_buf[2], s_long_frame_send.buf + s_long_frame_send.offset, copy_len);
        bsp_lin_slave_respond(id, send_buf, 8);
        s_long_frame_send.offset += copy_len;
        if (packet_index >= total_packets) {
            // 发送完毕，清零所有状态
            memset(&s_long_frame_send, 0, sizeof(s_long_frame_send));
            return 1;
        }
    }
    return 0;
}

/**
 * @brief 处理长帧
 *
 * @param data 数据
 * @param len 数据长度
 */
static void lin_slave_process_long_frame(uint8_t *data, uint16_t len)
{
    // 处理LIN长帧
    char log_buf[256] = {0};
    int offset        = snprintf(log_buf, sizeof(log_buf), "LIN处理长帧: len=%d, data=", len);

    for (uint16_t i = 0; i < len && offset < sizeof(log_buf) - 3; i++) {
        offset += snprintf(log_buf + offset, sizeof(log_buf) - offset, "%02X ", data[i]);
    }

    // 处理LIN长帧
    protocol_event_t *event = (protocol_event_t *)data;
    protocol_event_handle(event, len);

    LIN_LOG_INFO("%s", log_buf);
}

/**
 * @brief 处理接收到的数据（新协议分包组包）
 * @param data 数据
 * @param len 数据长度
 */
static void lin_slave_handle_received_data(uint8_t *data, uint16_t len)
{
    // 新协议：每帧8字节，第0字节=总包数，第1字节=包号，2~7字节为数据
    static uint8_t long_frame_buf[FRAME_BUF_SIZE] = {0};
    static uint8_t long_frame_recv_map[32]        = {0}; // 最多支持32包
    static uint8_t long_frame_total               = 0;
    static uint8_t long_frame_recv_cnt            = 0;
    static uint16_t long_frame_data_len           = 0;
    const uint8_t DATA_PER_PACKET                 = 6;
    static uint16_t last_total_data_len           = 0; // 记录本次完整数据长度

    if (len == 8) {
        uint8_t total_packets = data[0];
        uint8_t packet_index  = data[1];
        if (total_packets == 0 || packet_index == 0 || packet_index > total_packets || total_packets > 32) {
            // 协议错误
            return;
        }
        // 只处理第一次出现的包
        if (!(long_frame_recv_map[packet_index - 1])) {
            long_frame_recv_map[packet_index - 1] = 1;
            if (packet_index == 1) {
                long_frame_total    = total_packets;
                long_frame_data_len = 0;
                last_total_data_len = 0;
            }
            uint8_t copy_len = DATA_PER_PACKET;
            // 最后一包实际数据长度
            if (packet_index == total_packets) {
                // 计算总数据长度
                uint16_t total_data_len = (total_packets - 1) * DATA_PER_PACKET + DATA_PER_PACKET;
                // 数据长度校验
                if (total_data_len > FRAME_BUF_SIZE) total_data_len = FRAME_BUF_SIZE;
                // 计算最后一包实际数据长度
                uint16_t remain = FRAME_BUF_SIZE - (total_packets - 1) * DATA_PER_PACKET;
                copy_len        = remain > DATA_PER_PACKET ? DATA_PER_PACKET : remain;
                // 记录总数据长度
                last_total_data_len = (total_packets - 1) * DATA_PER_PACKET + copy_len;
            }
            // 数据长度校验
            if (copy_len > DATA_PER_PACKET) copy_len = DATA_PER_PACKET;
            if (long_frame_data_len + copy_len > FRAME_BUF_SIZE) copy_len = FRAME_BUF_SIZE - long_frame_data_len;
            // 数据拷贝
            memcpy(long_frame_buf + (packet_index - 1) * DATA_PER_PACKET, &data[2], copy_len);
            long_frame_recv_cnt++;
        }
        // 收齐所有包
        if (long_frame_recv_cnt == long_frame_total) {
            uint16_t use_len = last_total_data_len ? last_total_data_len : long_frame_total * DATA_PER_PACKET;
            lin_slave_process_long_frame(long_frame_buf, use_len);
            memset(long_frame_buf, 0, sizeof(long_frame_buf));
            memset(long_frame_recv_map, 0, sizeof(long_frame_recv_map));
            long_frame_total    = 0;
            long_frame_recv_cnt = 0;
            long_frame_data_len = 0;
            last_total_data_len = 0;
        }
    }
}

/**
 * @brief LIN从机任务入口
 */
static void lin_slave_task_entry(void *pvParameters)
{
    uint8_t id, data[16], len;
    for (;;) {
        if (bsp_lin_slave_receive(&id, data, &len, 1000)) {
            if (id == LIN_READ_PID) {
                // 判断是否有数据需要上传
                if (lin_slave_send_frame_len > 0) {
                    // 等待完整数据帧上传完成
                    if (lin_slave_send_long_frame(id, lin_slave_send_frame_buf, lin_slave_send_frame_len)) {
                        // 清空数据缓冲区
                        memset(lin_slave_send_frame_buf, 0, sizeof(lin_slave_send_frame_buf));
                        lin_slave_send_frame_len = 0;
                    }
                } else {
                    // 发送空数据包
                    bsp_lin_slave_respond(id, lin_slave_send_frame_buf, 8);
                }
                LIN_LOG_INFO("LIN从机收到主机读请求: id=0x%02X, len=%d", id, 8);
            } else if (id == LIN_WRITE_PID) {
                lin_slave_handle_received_data(data, len);
            }
        }
    }
}

/**
 * @brief LIN主机任务入口（支持回调通知切换为从机）
 */
static void lin_master_task_entry(void *pvParameters)
{
    for (;;) {
        uint32_t now_tick = xTaskGetTickCount();
        for (int i = 0; i < LIN_SCHEDULE_TABLE_SIZE; i++) {
            lin_schedule_entry_t *entry = &g_lin_schedule_table[i];
            if (!lin_entry_is_valid(entry)) continue;
            if (!lin_entry_need_schedule(entry, now_tick)) continue;
            lin_entry_execute(entry, now_tick);
        }
        // 检查是否收到回调通知（切换为从机）
        if (ulTaskNotifyTake(pdTRUE, 0)) {
            // 等待100ms，确保从机任务已初始化
            vTaskDelay(pdMS_TO_TICKS(100));

            LIN_LOG_INFO("主机任务收到切换为从机模式的通知");

            g_lin_mode = LIN_MODE_SLAVE;
            bsp_lin_init(19200, LIN_ROLE_SLAVE);
            xTaskCreate(lin_slave_task_entry,
                        "lin_slave",
                        APP_LIN_TASK_STACK_SIZE,
                        NULL,
                        APP_LIN_PRIO,
                        NULL);
            vTaskDelete(g_lin_master_task_handle); // 删除主机任务
        }
        vTaskDelay(pdMS_TO_TICKS(10));
    }
}

/**
 * @brief LIN任务初始化（默认主机模式）
 */
void lin_task_init(void)
{
    g_lin_mode = LIN_MODE_MASTER;
    bsp_lin_init(19200, LIN_ROLE_MASTER);
    lin_schedule_table_init();
    extern void lin_schedule_register_all(void);
    lin_schedule_register_all();
    xTaskCreate(lin_master_task_entry,
                "lin_master",
                APP_LIN_TASK_STACK_SIZE,
                NULL,
                APP_LIN_PRIO,
                &g_lin_master_task_handle);
    LIN_LOG_INFO("LIN主机任务已启动");
}

/**
 * @brief 切换到从机模式（由回调触发）
 */
void lin_task_switch_to_slave(void)
{
    if (g_lin_mode == LIN_MODE_MASTER && g_lin_master_task_handle != NULL) {
        // 通知主机任务切换为从机
        xTaskNotifyGive(g_lin_master_task_handle);
    }
}

/**
 * @brief 发送帧
 *
 * @param id 从机ID
 * @param frame_buf 帧数据
 * @param frame_len 帧长度
 */
void lin_slave_send_frame(const uint8_t *frame_buf, uint16_t frame_len)
{
    memcpy(lin_slave_send_frame_buf, frame_buf, frame_len);
    lin_slave_send_frame_len = frame_len;
}