#ifndef __BSP_LED_H__
#define __BSP_LED_H__

#include "config.h"

#define SYSLED_PORT         HC_GPIOB
#define WIFILED_PORT        HC_GPIOA
#define BLELED_PORT         HC_GPIOA

#define SYSLED_PIN          GPIO_PIN_01
#define WIFILED_PIN         GPIO_PIN_05
#define BLELED_PIN          GPIO_PIN_06

#define SYSLED_PIN_SET()    GPIO_PB01_SET()
#define WIFILED_PIN_SET()   GPIO_PA05_SET()
#define BLELED_PIN_SET()    GPIO_PA06_SET()

#define SYSLED_PIN_RESET()  GPIO_PB01_RESET()
#define WIFILED_PIN_RESET() GPIO_PA05_RESET()
#define BLELED_PIN_RESET()  GPIO_PA06_RESET()

#define SYSLED_TOGGLE()     HC_GPIOB->OUT ^= GPIO_PIN_01
#define WIFILED_TOGGLE()    HC_GPIOA->OUT ^= GPIO_PIN_05
#define BLELED_TOGGLE()     HC_GPIOA->OUT ^= GPIO_PIN_06

extern void bsp_LedInit(void);

#endif