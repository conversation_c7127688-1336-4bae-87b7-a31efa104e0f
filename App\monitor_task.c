/**
 * @file monitor_task.c
 * <AUTHOR> (<EMAIL>)
 * @brief 任务监控任务
 * @version 0.1
 * @date 2025-05-21
 *
 * @copyright Copyright (c) 2025
 *
 */

#include "monitor_task.h"

#if MONITOR_ENABLE

#define MONITOR_TASK_PERIOD_MS 1000

static TaskHandle_t s_monitor_task_handle = NULL;

static char InfoBuffer[512] = {0};

/**
 * @brief 任务监控任务入口函数
 * @param pvParameters 任务参数
 */
static void monitor_task_entry(void *pvParameters)
{
    (void)pvParameters;

    for (;;) {
        // 打印剩余总堆栈大小
        printf("剩余总堆栈大小: %d\r\n", xPortGetFreeHeapSize());
        printf("最小的栈空间大小：%d\r\n", xPortGetMinimumEverFreeHeapSize() * 4);
        vTaskList((char *)&InfoBuffer);
        printf("任务名      任务状态    优先级    剩余栈    任务序号\r\n");
        printf("\r\n%s\r\n", InfoBuffer);

        vTaskDelay(pdMS_TO_TICKS(MONITOR_TASK_PERIOD_MS));
    }
}

/**
 * @brief 任务监控任务初始化
 */
void monitor_task_init(void)
{
    if (s_monitor_task_handle == NULL) {
        BaseType_t xReturn = xTaskCreate(monitor_task_entry,
                                         "monitor_task",
                                         APP_MONITOR_TASK_STACK_SIZE,
                                         NULL,
                                         APP_MONITOR_PRIO,
                                         &s_monitor_task_handle);
        if (xReturn != pdPASS) {
            MONITOR_LOG_ERROR("monitor_task_init: xTaskCreate failed");
        }
    }
}

#endif