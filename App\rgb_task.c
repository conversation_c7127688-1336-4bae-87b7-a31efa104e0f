/**
 * @file rgb_task.c
 * <AUTHOR> (<EMAIL>)
 * @brief RGB任务实现   
 * @version 0.1
 * @date 2025-05-06
 * 
 * @copyright Copyright (c) 2025
 * 
 */
#include "rgb_task.h"

// =================== 静态变量 ===================
static RgbState rgb_state[RGB_CHANNEL_MAX];
static RgbType currentRgbType              = RGB_TYPE_SINGLE;
static RgbResponseMode currentResponseMode = RGB_RESPONSE_FAST;
static QueueHandle_t rgb_evt_queue         = NULL;
static TaskHandle_t rgb_task_handle        = NULL;

/**
 * @brief 初始化PWM输出
 */
static void rgb_initPwm(void)
{
    RGB_SET_PWM_DUTY(RGB_SINGLE_PWM_PORT, 100);
    RGB_SET_PWM_DUTY(RGB_RED_PWM_PORT, 100);
    RGB_SET_PWM_DUTY(RGB_GREEN_PWM_PORT, 100);
    RGB_SET_PWM_DUTY(RGB_BLUE_PWM_PORT, 100);
}

/**
 * @brief 更新RGB输出
 *
 * @param channel 通道
 * @param state 状态参数
 */
static void rgb_updateOutput(RgbChannel channel, const RgbState *state)
{
    bool updateNeeded = false;
    uint8_t duty = 0, duty_r = 0, duty_g = 0, duty_b = 0;
    static struct {
        bool isOn;
        uint8_t brightness;
        uint8_t r;
        uint8_t g;
        uint8_t b;
        uint8_t duty;
        uint8_t duty_r;
        uint8_t duty_g;
        uint8_t duty_b;
    } lastPwmState[RGB_CHANNEL_MAX] = {0};

    // 判断是否需要关闭输出
    if (state->isOn == 0 ||
        (channel == RGB_CHANNEL_SINGLE && state->brightness == 0) ||
        (channel == RGB_CHANNEL_RGB && state->r == 0 && state->g == 0 && state->b == 0)) {
        if (lastPwmState[channel].isOn) {
            lastPwmState[channel].isOn       = false;
            lastPwmState[channel].brightness = 0;
            lastPwmState[channel].r          = 0;
            lastPwmState[channel].g          = 0;
            lastPwmState[channel].b          = 0;
            if (channel == RGB_CHANNEL_SINGLE) {
                RGB_SET_PWM_DUTY(RGB_SINGLE_PWM_PORT, 100);
            } else {
                RGB_SET_PWM_DUTY(RGB_RED_PWM_PORT, 100);
                RGB_SET_PWM_DUTY(RGB_GREEN_PWM_PORT, 100);
                RGB_SET_PWM_DUTY(RGB_BLUE_PWM_PORT, 100);
            }
        }
        return;
    }

    if (currentRgbType == RGB_TYPE_SINGLE) {
        uint8_t brightness = (state->brightness > RGB_MAX_BRIGHTNESS) ? RGB_MAX_BRIGHTNESS : state->brightness;
        duty               = 100 - ((brightness * RGB_MAX_PWM_LIMIT) / RGB_MAX_BRIGHTNESS);
        if (!lastPwmState[channel].isOn ||
            lastPwmState[channel].brightness != brightness ||
            lastPwmState[channel].duty != duty) {
            updateNeeded                     = true;
            lastPwmState[channel].isOn       = true;
            lastPwmState[channel].brightness = brightness;
            lastPwmState[channel].duty       = duty;
            RGB_SET_PWM_DUTY(RGB_SINGLE_PWM_PORT, duty);
        }
    } else {
        uint8_t r = (state->r > 255) ? 255 : state->r;
        uint8_t g = (state->g > 255) ? 255 : state->g;
        uint8_t b = (state->b > 255) ? 255 : state->b;
        duty_r    = 100 - ((r * RGB_MAX_PWM_LIMIT) / 255);
        duty_g    = 100 - ((g * RGB_MAX_PWM_LIMIT) / 255);
        duty_b    = 100 - ((b * RGB_MAX_PWM_LIMIT) / 255);
        if (!lastPwmState[channel].isOn ||
            lastPwmState[channel].r != r ||
            lastPwmState[channel].g != g ||
            lastPwmState[channel].b != b ||
            lastPwmState[channel].duty_r != duty_r ||
            lastPwmState[channel].duty_g != duty_g ||
            lastPwmState[channel].duty_b != duty_b) {
            updateNeeded                 = true;
            lastPwmState[channel].isOn   = true;
            lastPwmState[channel].r      = r;
            lastPwmState[channel].g      = g;
            lastPwmState[channel].b      = b;
            lastPwmState[channel].duty_r = duty_r;
            lastPwmState[channel].duty_g = duty_g;
            lastPwmState[channel].duty_b = duty_b;
            RGB_SET_PWM_DUTY(RGB_RED_PWM_PORT, duty_r);
            RGB_SET_PWM_DUTY(RGB_GREEN_PWM_PORT, duty_g);
            RGB_SET_PWM_DUTY(RGB_BLUE_PWM_PORT, duty_b);
        }
    }
}

/**
 * @brief 更新RGB灯效
 *
 * @param state 状态参数
 */
static void rgb_updateEffect(RgbState *state)
{
    // 静态/无灯效时直接跳到最大步进
    if (state->effect == (uint8_t)RGB_EFFECT_STATIC || state->effect == (uint8_t)RGB_EFFECT_NONE) {
        if (currentResponseMode == RGB_RESPONSE_FAST && state->step < 255) {
            state->step = 255;
            return;
        } else if (state->step == 255) {
            return;
        }
    }
    uint32_t oldTickCount = state->tickCount;
    state->tickCount += RGB_TICK_MS;
    bool periodReset = false;
    if (state->tickCount >= state->period && state->period > 0) {
        state->tickCount = 0;
        periodReset      = true;
    }
    // 呼吸/彩虹灯效分步处理
    if (!periodReset && oldTickCount > 0 &&
        (state->effect == (uint8_t)RGB_EFFECT_BREATH || state->effect == (uint8_t)RGB_EFFECT_RAINBOW)) {
        if ((state->tickCount % (state->period / 20)) != 0) {
            return;
        }
    }
    switch (state->effect) {
        case (uint8_t)RGB_EFFECT_BREATH: {
            uint8_t oldStep = state->step;
            if (state->direction == 0) {
                if (state->step < (255 - RGB_BREATH_STEP)) {
                    state->step += RGB_BREATH_STEP;
                } else {
                    state->step      = 255;
                    state->direction = 1;
                }
            } else {
                if (state->step > RGB_BREATH_STEP) {
                    state->step -= RGB_BREATH_STEP;
                } else {
                    state->step      = 0;
                    state->direction = 0;
                }
            }
            // 快速响应模式下直接跳步
            if (currentResponseMode == RGB_RESPONSE_FAST) {
                state->step += (state->direction == 0) ? RGB_BREATH_STEP : -RGB_BREATH_STEP;
                if (state->step > 255) state->step = 255;
                if (state->step < 0) state->step = 0;
            }
            if (state->step != oldStep) {
                state->r = state->step;
                state->g = state->step;
                state->b = state->step;
            }
            break;
        }
        case (uint8_t)RGB_EFFECT_RAINBOW: {
            uint8_t oldStep = state->step;
            uint8_t oldR    = state->r;
            uint8_t oldG    = state->g;
            uint8_t oldB    = state->b;
            if (periodReset || oldTickCount == 0) {
                state->step = (state->step + 1) % 6;
            }
            // 彩虹灯分段变色
            switch (state->step) {
                case 0:
                    state->r = 255;
                    state->g = state->tickCount * 255 / state->period;
                    state->b = 0;
                    break;
                case 1:
                    state->r = 255 - (state->tickCount * 255 / state->period);
                    state->g = 255;
                    state->b = 0;
                    break;
                case 2:
                    state->r = 0;
                    state->g = 255;
                    state->b = state->tickCount * 255 / state->period;
                    break;
                case 3:
                    state->r = 0;
                    state->g = 255 - (state->tickCount * 255 / state->period);
                    state->b = 255;
                    break;
                case 4:
                    state->r = state->tickCount * 255 / state->period;
                    state->g = 0;
                    state->b = 255;
                    break;
                case 5:
                    state->r = 255;
                    state->g = 0;
                    state->b = 255 - (state->tickCount * 255 / state->period);
                    break;
            }
            // 防止颜色抖动
            if (abs(state->r - oldR) < 5 && abs(state->g - oldG) < 5 && abs(state->b - oldB) < 5 &&
                state->step == oldStep && oldTickCount > 0) {
                state->r = oldR;
                state->g = oldG;
                state->b = oldB;
            }
            break;
        }
        case (uint8_t)RGB_EFFECT_FLASH: {
            // 闪烁灯效
            bool is_on  = (state->tickCount < state->period / 2);
            bool was_on = (oldTickCount < state->period / 2);
            if (is_on != was_on || oldTickCount == 0) {
                state->r = is_on ? 255 : 0;
                state->g = is_on ? 255 : 0;
                state->b = is_on ? 255 : 0;
            }
            break;
        }
        default:
            break;
    }
}

/**
 * @brief RGB任务主循环
 *
 * @param pvParameters 传入参数
 */
static void rgb_task_entry(void *pvParameters)
{
    rgb_initPwm();
    memset(rgb_state, 0, sizeof(rgb_state));
    TickType_t last_wake = xTaskGetTickCount();

    for (;;) {
        RgbEvt evt;
        // 处理事件队列
        if (xQueueReceive(rgb_evt_queue, &evt, RGB_TICK_MS / portTICK_PERIOD_MS) == pdPASS) {
            // 处理事件，更新 rgb_state
            switch (evt.effect) {
                case RGB_EFFECT_BREATH:
                case RGB_EFFECT_RAINBOW:
                case RGB_EFFECT_FLASH:
                    rgb_state[evt.channel].effect = evt.effect;
                    rgb_state[evt.channel].period = evt.period;
                    rgb_state[evt.channel].isOn   = 1;
                    break;
                case RGB_EFFECT_STATIC:
                default:
                    if (evt.channel == RGB_CHANNEL_SINGLE) {
                        rgb_state[evt.channel].brightness = evt.brightness;
                    } else {
                        rgb_state[evt.channel].r = evt.color.r;
                        rgb_state[evt.channel].g = evt.color.g;
                        rgb_state[evt.channel].b = evt.color.b;
                    }
                    rgb_state[evt.channel].effect = evt.effect;
                    rgb_state[evt.channel].period = evt.period;
                    rgb_state[evt.channel].isOn   = 1;
                    break;
            }
        }
        // 灯效处理
        for (int i = 0; i < RGB_CHANNEL_MAX; i++) {
            if (rgb_state[i].isOn) {
                if (rgb_state[i].effect == RGB_EFFECT_BREATH || rgb_state[i].effect == RGB_EFFECT_RAINBOW || rgb_state[i].effect == RGB_EFFECT_FLASH) {
                    rgb_updateEffect(&rgb_state[i]);
                }
                rgb_updateOutput(i, &rgb_state[i]);
            }
        }
        vTaskDelayUntil(&last_wake, RGB_TICK_MS / portTICK_PERIOD_MS);
    }
}

/**
 * @brief 初始化RGB任务
 *
 * @return 无
 */
void rgb_task_init(void)
{
    rgb_evt_queue = xQueueCreate(8, sizeof(RgbEvt));
    if (rgb_evt_queue == NULL) {
        RGB_LOG_ERROR("rgb_task_init: xQueueCreate failed");
    }   

    BaseType_t xReturn = xTaskCreate(rgb_task_entry,
                                     "rgb_task",
                                     APP_RGB_TASK_STACK_SIZE,
                                     NULL,
                                     APP_RGB_PRIO,
                                     &rgb_task_handle);
    if (xReturn != pdPASS) {
        RGB_LOG_ERROR("rgb_task_init: xTaskCreate failed");
    }
}

/**
 * @brief 打开RGB
 *
 * @param channel 通道
 */
void RGB_TurnOn(RgbChannel channel)
{
    RgbEvt evt  = {0};
    evt.channel = channel;
    evt.effect  = RGB_EFFECT_STATIC;
    evt.period  = RGB_DEFAULT_PERIOD;
    xQueueSend(rgb_evt_queue, &evt, 0);
}

/**
 * @brief 关闭RGB
 *
 * @param channel 通道
 */
void RGB_TurnOff(RgbChannel channel)
{
    rgb_state[channel].isOn = 0;
    rgb_updateOutput(channel, &rgb_state[channel]);
}

/**
 * @brief 设置RGB颜色
 *
 * @param channel 通道
 * @param r 红色
 * @param g 绿色
 * @param b 蓝色
 */
void RGB_SetColor(RgbChannel channel, uint8_t r, uint8_t g, uint8_t b)
{
    RgbEvt evt  = {0};
    evt.channel = channel;
    evt.color.r = r;
    evt.color.g = g;
    evt.color.b = b;
    evt.effect  = RGB_EFFECT_STATIC;
    evt.period  = RGB_DEFAULT_PERIOD;
    xQueueSend(rgb_evt_queue, &evt, 0);
}

/**
 * @brief 设置RGB亮度
 *
 * @param channel 通道
 * @param brightness 亮度
 */
void RGB_SetBrightness(RgbChannel channel, uint8_t brightness)
{
    RgbEvt evt     = {0};
    evt.channel    = channel;
    evt.brightness = brightness;
    evt.effect     = RGB_EFFECT_STATIC;
    evt.period     = RGB_DEFAULT_PERIOD;
    xQueueSend(rgb_evt_queue, &evt, 0);
}

/**
 * @brief 设置RGB灯效
 *
 * @param channel 通道
 * @param effect 灯效
 * @param period 周期
 */
void RGB_SetEffect(RgbChannel channel, RgbEffect effect, uint32_t period)
{
    RgbEvt evt  = {0};
    evt.channel = channel;
    evt.effect  = effect;
    evt.period  = period;
    xQueueSend(rgb_evt_queue, &evt, 0);
}

/**
 * @brief 设置RGB响应模式
 *
 * @param mode 响应模式
 */
void RGB_SetResponseMode(RgbResponseMode mode)
{
    if (mode < RGB_RESPONSE_MAX)
        currentResponseMode = mode;
}

/**
 * @brief 获取RGB响应模式
 *
 * @return 响应模式
 */
RgbResponseMode RGB_GetResponseMode(void)
{
    return currentResponseMode;
}

/**
 * @brief 设置RGB类型
 *
 * @param type RGB类型
 */
void RGB_SetType(RgbType type)
{
    if (type < RGB_TYPE_MAX && type != currentRgbType) {
        currentRgbType = type;
        for (int i = 0; i < RGB_CHANNEL_MAX; i++) {
            RGB_TurnOff(i);
        }
    }
}

/**
 * @brief 获取RGB类型
 *
 * @return RGB类型
 */
RgbType RGB_GetType(void)
{
    return currentRgbType;
}