#include "protocol.h"
#include "msg_task.h"
#include "motor_task.h"
#include "lin/lin_task.h"

/**
 * @brief 计算CRC校验值
 *
 * @param ptr 数据指针
 * @param len 数据长度
 * @return uint32_t CRC校验值
 */
uint32_t Sum_Check(uint8_t *ptr, uint16_t len)
{
    uint16_t i;
    uint32_t crc = 0;
    for (i = 0; i < len; i++) {
        crc += *ptr++;
    }
    return crc;
}

// 协议应答函数声明
static void protocol_send_status_response(const protocol_frame_t *req_frame, uint8_t status, const uint8_t *data, uint16_t data_len);

/**
 * @brief 部位控制
 *
 * @param frame 协议帧
 * @param msg 控制消息
 * @return uint8_t 状态
 */
static uint8_t part_ctrl_handle(const protocol_frame_t *frame, control_msg_t *msg)
{
    uint8_t status = PROTOCOL_SUCCESS_CMD;

    msg->src = MSG_SRC_NET;
    msg->cmd = CTRL_CMD_HEAD;
    if (frame->payload.cmd_data[1] == HEAD_PART) {
        msg->param.bed_move.part = MOTOR_ID_BACK1;
    } else if (frame->payload.cmd_data[1] == BACK_PART) {
        msg->param.bed_move.part = MOTOR_ID_BACK2;
    } else if (frame->payload.cmd_data[1] == WAIST_PART) {
        msg->param.bed_move.part = MOTOR_ID_WAIST;
    } else if (frame->payload.cmd_data[1] == LEG_PART) {
        msg->param.bed_move.part = MOTOR_ID_LEG;
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    if (frame->payload.cmd_data[2] == MOVE_UP) {
        msg->param.bed_move.action = BED_MOVE_UP;
    } else if (frame->payload.cmd_data[2] == MOVE_DOWN) {
        msg->param.bed_move.action = BED_MOVE_DOWN;
    } else if (frame->payload.cmd_data[2] == STOP_MOVE) {
        msg->param.bed_move.action = BED_MOVE_STOP;
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    msg_send(msg);

    return status;
}

/**
 * @brief 按摩控制
 *
 * @param frame 协议帧
 * @param msg 控制消息
 * @return uint8_t 状态
 */
static uint8_t massage_ctrl_handle(const protocol_frame_t *frame, control_msg_t *msg)
{
    uint8_t status = PROTOCOL_SUCCESS_CMD;

    msg->src = MSG_SRC_NET;
    msg->cmd = CTRL_CMD_MASSAGE;
    if (frame->payload.cmd_data[0] == LEFT_SIDE) {
    } else if (frame->payload.cmd_data[0] == RIGHT_SIDE) {
    } else if (frame->payload.cmd_data[0] == BOTH_SIDE) {
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    if (frame->payload.cmd_data[1] == MASSAGE_PART_BACK) {
        msg->param.massage.part = MASSAGE_PART_BACK;
    } else if (frame->payload.cmd_data[1] == MASSAGE_PART_LEG) {
        msg->param.massage.part = MASSAGE_PART_LEG;
    } else if (frame->payload.cmd_data[1] == MASSAGE_PART_ALL) {
        msg->param.massage.part = MASSAGE_PART_ALL;
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    if (frame->payload.cmd_data[2] == MASSAGE_STRENGTH_LEVEL_1) {
        msg->param.massage.strength = MASSAGE_STRENGTH_1;
    } else if (frame->payload.cmd_data[2] == MASSAGE_STRENGTH_LEVEL_2) {
        msg->param.massage.strength = MASSAGE_STRENGTH_2;
    } else if (frame->payload.cmd_data[2] == MASSAGE_STRENGTH_LEVEL_3) {
        msg->param.massage.strength = MASSAGE_STRENGTH_3;
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    msg_send(msg);

    return status;
}

/**
 * @brief 灯光控制
 *
 * @param frame 协议帧
 * @param msg 控制消息
 * @return uint8_t 状态
 */
static uint8_t light_ctrl_handle(const protocol_frame_t *frame, control_msg_t *msg)
{
    uint8_t status = PROTOCOL_SUCCESS_CMD;

    msg->src = MSG_SRC_NET;
    msg->cmd = CTRL_CMD_LIGHT;
    if (frame->payload.cmd_data[1] == LIGHT_ON) {
        msg->param.value = RGB_ON;
    } else if (frame->payload.cmd_data[1] == LIGHT_OFF) {
        msg->param.value = RGB_OFF;
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    msg_send(msg);

    return status;
}

/**
 * @brief 工作模式控制
 *
 * @param frame 协议帧
 * @param msg 控制消息
 * @return uint8_t 状态
 */
static uint8_t work_mode_ctrl_handle(const protocol_frame_t *frame, control_msg_t *msg)
{
    uint8_t status = PROTOCOL_SUCCESS_CMD;

    msg->src = MSG_SRC_NET;
    msg->cmd = CTRL_CMD_WORK_MODE;
    if (frame->payload.cmd_data[0] == LEFT_SIDE) {
    } else if (frame->payload.cmd_data[0] == RIGHT_SIDE) {
    } else if (frame->payload.cmd_data[0] == BOTH_SIDE) {
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    if (frame->payload.cmd_data[1] == CAUSUAL_WORKMODE) {
        msg->param.value = BED_MODE_CASUAL;
    } else if (frame->payload.cmd_data[1] == SLEEP_WORKMODE) {
        msg->param.value = BED_MODE_SLEEP;
    } else if (frame->payload.cmd_data[1] == SNORE_WORKMODE) {
        msg->param.value = BED_MODE_SNORE;
    } else if (frame->payload.cmd_data[1] == SPACE_WORKMODE) {
        msg->param.value = BED_MODE_DRINK;
    } else if (frame->payload.cmd_data[1] == READ_WORKMODE) {
        msg->param.value = BED_MODE_RELAX;
    } else if (frame->payload.cmd_data[1] == FEEDING_WORKMODE) {
        msg->param.value = BED_MODE_SPACE;
    } else if (frame->payload.cmd_data[1] == YOGA_WORKMODE) {
        msg->param.value = BED_MODE_SOFT_WAKEUP;
    } else if (frame->payload.cmd_data[1] == WAIST_WORKMODE) {
        msg->param.value = BED_MODE_PILATES;
    } else if (frame->payload.cmd_data[1] == MASSAGE_WORKMODE) {
        msg->param.value = BED_MODE_RECOVERY;
    } else if (frame->payload.cmd_data[1] == SHOW_WORKMODE) {
        msg->param.value = BED_MODE_SHOW;
    } else if (frame->payload.cmd_data[1] == DRINK_WORKMODE) {
        msg->param.value = BED_MODE_MASSAGE;
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    msg_send(msg);

    return status;
}

/**
 * @brief 停止控制
 *
 * @param frame 协议帧
 * @param msg 控制消息
 * @return uint8_t 状态
 */
static uint8_t stop_ctrl_handle(const protocol_frame_t *frame, control_msg_t *msg)
{
    uint8_t status = PROTOCOL_SUCCESS_CMD;

    if (frame->payload.cmd_data[0] == LEFT_SIDE) {
    } else if (frame->payload.cmd_data[0] == RIGHT_SIDE) {
    } else if (frame->payload.cmd_data[0] == BOTH_SIDE) {
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    if (frame->payload.cmd_data[1] == 0x01) {
        msg->src = MSG_SRC_NET;
        msg->cmd = CTRL_CMD_STOP;
        msg_send(msg);
    } else {
        status = PROTOCOL_ERROR_CMD;
    }

    return status;
}

/**
 * @brief 整体控制
 *
 * @param frame 协议帧
 * @param msg 控制消息
 * @return uint8_t 状态
 */
static uint8_t full_ctrl_handle(const protocol_frame_t *frame, control_msg_t *msg)
{
    uint8_t status = PROTOCOL_SUCCESS_CMD;

    msg->src = MSG_SRC_NET;
    msg->cmd = CTRL_CMD_FULL;
    if (frame->payload.cmd_data[0] == LEFT_SIDE) {
    } else if (frame->payload.cmd_data[0] == RIGHT_SIDE) {
    } else if (frame->payload.cmd_data[0] == BOTH_SIDE) {
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    if (frame->payload.cmd_data[1] == MOVE_UP) {
        msg->param.value = BED_MOVE_UP;
    } else if (frame->payload.cmd_data[1] == MOVE_DOWN) {
        msg->param.value = BED_MOVE_DOWN;
    } else if (frame->payload.cmd_data[1] == STOP_MOVE) {
        msg->param.value = BED_MOVE_STOP;
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    msg_send(msg);

    return status;
}

/**
 * @brief 记忆控制
 *
 * @param frame 协议帧
 * @param msg 控制消息
 * @return uint8_t 状态
 */
static uint8_t memory_ctrl_handle(const protocol_frame_t *frame, control_msg_t *msg)
{
    uint8_t status = PROTOCOL_SUCCESS_CMD;

    msg->src = MSG_SRC_NET;
    msg->cmd = CTRL_CMD_MEMORY;
    if (frame->payload.cmd_data[0] == MEMORY1_MODE) {
        if (frame->payload.cmd_data[1] == GET_MEMORY_ACTION) {
            msg->param.value = GET_MEMORY1;
        } else if (frame->payload.cmd_data[1] == SET_MEMORY_ACTION) {
            msg->param.value = SET_MEMORY1;
        } else {
            status = PROTOCOL_ERROR_CMD;
        }
    } else if (frame->payload.cmd_data[0] == MEMORY2_MODE) {
        if (frame->payload.cmd_data[1] == GET_MEMORY_ACTION) {
            msg->param.value = GET_MEMORY2;
        } else if (frame->payload.cmd_data[1] == SET_MEMORY_ACTION) {
            msg->param.value = SET_MEMORY2;
        } else {
            status = PROTOCOL_ERROR_CMD;
        }
    } else if (frame->payload.cmd_data[0] == MEMORY3_MODE) {
        if (frame->payload.cmd_data[1] == GET_MEMORY_ACTION) {
            msg->param.value = GET_MEMORY3;
        } else if (frame->payload.cmd_data[1] == SET_MEMORY_ACTION) {
            msg->param.value = SET_MEMORY3;
        } else {
            status = PROTOCOL_ERROR_CMD;
        }
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    msg_send(msg);

    return status;
}

/**
 * @brief 按摩模式控制
 *
 * @param frame 协议帧
 * @param msg 控制消息
 * @return uint8_t 状态
 */
static uint8_t massage_mode_ctrl_handle(const protocol_frame_t *frame, control_msg_t *msg)
{
    uint8_t status = PROTOCOL_SUCCESS_CMD;

    msg->src = MSG_SRC_NET;
    msg->cmd = CTRL_CMD_MASSAGE_MODE;
    if (frame->payload.cmd_data[0] == LEFT_SIDE) {
    } else if (frame->payload.cmd_data[0] == RIGHT_SIDE) {
    } else if (frame->payload.cmd_data[0] == BOTH_SIDE) {
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    if (frame->payload.cmd_data[1] != 0) {
        msg->param.massage.time = frame->payload.cmd_data[0];
    }
    if (frame->payload.cmd_data[2] == CONSTANT_MASSAGE_MODE) {
        msg->param.massage.mode = MASSAGE_MODE_CONSTANT;
    } else if (frame->payload.cmd_data[2] == PULSE1_MASSAGE_MODE) {
        msg->param.massage.mode = MASSAGE_MODE_PULSE1;
    } else if (frame->payload.cmd_data[2] == PULSE2_MASSAGE_MODE) {
        msg->param.massage.mode = MASSAGE_MODE_PULSE2;
    } else if (frame->payload.cmd_data[2] == PULSE3_MASSAGE_MODE) {
        msg->param.massage.mode = MASSAGE_MODE_PULSE3;
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    msg_send(msg);

    return status;
}

/**
 * @brief 按摩强度控制
 *
 * @param frame 协议帧
 * @param msg 控制消息
 * @return uint8_t 状态
 */
static uint8_t massage_strength_ctrl_handle(const protocol_frame_t *frame, control_msg_t *msg)
{
    uint8_t status = PROTOCOL_SUCCESS_CMD;

    msg->src = MSG_SRC_NET;
    msg->cmd = CTRL_CMD_MASSAGE_STRENGTH;
    if (frame->payload.cmd_data[0] == LEFT_SIDE) {
    } else if (frame->payload.cmd_data[0] == RIGHT_SIDE) {
    } else if (frame->payload.cmd_data[0] == BOTH_SIDE) {
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    if (frame->payload.cmd_data[1] == HEAD_PART) {
    } else if (frame->payload.cmd_data[1] == BACK_PART) {
    } else if (frame->payload.cmd_data[1] == WAIST_PART) {
    } else if (frame->payload.cmd_data[1] == LEG_PART) {
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    if (frame->payload.cmd_data[2] == MASSAGE_STRENGTH_LEVEL_1) {
        msg->param.massage.strength = MASSAGE_STRENGTH_1;
    } else if (frame->payload.cmd_data[2] == MASSAGE_STRENGTH_LEVEL_2) {
        msg->param.massage.strength = MASSAGE_STRENGTH_2;
    } else if (frame->payload.cmd_data[2] == MASSAGE_STRENGTH_LEVEL_3) {
        msg->param.massage.strength = MASSAGE_STRENGTH_3;
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    msg_send(msg);

    return status;
}

/**
 * @brief 关闭按摩控制
 *
 * @param frame 协议帧
 * @param msg 控制消息
 * @return uint8_t 状态
 */
static uint8_t close_massage_ctrl_handle(const protocol_frame_t *frame, control_msg_t *msg)
{
    uint8_t status = PROTOCOL_SUCCESS_CMD;

    if (frame->payload.cmd_data[0] == LEFT_SIDE) {
    } else if (frame->payload.cmd_data[0] == RIGHT_SIDE) {
    } else if (frame->payload.cmd_data[0] == BOTH_SIDE) {
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    if (frame->payload.cmd_data[1] != 0) {
        msg->src         = MSG_SRC_NET;
        msg->cmd         = CTRL_CMD_MASSAGE_POWER;
        msg->param.value = MASSAGE_POWER_OFF;
        msg_send(msg);
    } else {
        status = PROTOCOL_ERROR_CMD;
    }

    return status;
}

/**
 * @brief 智能模式控制
 *
 * @param frame 协议帧
 * @param msg 控制消息
 * @return uint8_t 状态
 */
static uint8_t smart_mode_ctrl_handle(const protocol_frame_t *frame, control_msg_t *msg)
{
    uint8_t status = PROTOCOL_SUCCESS_CMD;

    msg->src = MSG_SRC_NET;
    msg->cmd = CTRL_CMD_SMART_MODE;
    if (frame->payload.cmd_data[0] == LEFT_SIDE) {
    } else if (frame->payload.cmd_data[0] == RIGHT_SIDE) {
    } else if (frame->payload.cmd_data[0] == BOTH_SIDE) {
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    if (frame->payload.cmd_data[1] == NO_SMART_MODE) {
    } else if (frame->payload.cmd_data[1] == SMART_SLEEP_MODE) {
    } else if (frame->payload.cmd_data[1] == SMART_YOGA_MODE) {
    } else if (frame->payload.cmd_data[1] == SMART_RECOVERY_MODE) {
    } else if (frame->payload.cmd_data[1] == SMART_WAIST_CARE_MODE) {
    } else if (frame->payload.cmd_data[1] == SMART_WAKEUP_MODE) {
    } else if (frame->payload.cmd_data[1] == SMART_AUTO_SNORE_MODE) {
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    if (frame->payload.cmd_data[2] == SMART_MODE_OFF) {
        msg->param.value = SMART_MODE_OFF;
    } else if (frame->payload.cmd_data[2] == SMART_MODE_ON) {
        msg->param.value = SMART_MODE_ON;
    } else if (frame->payload.cmd_data[2] == SMART_MODE_CHECK) {
        msg->param.value = SMART_MODE_CHECK;
    } else {
        status = PROTOCOL_ERROR_CMD;
    }
    msg_send(msg);

    return status;
}

/**
 * @brief 获取按摩状态
 *
 * @param resp_data 响应数据
 * @param resp_data_len 响应数据长度
 */
static void get_massage_status_handle(uint8_t *resp_data, uint16_t *resp_data_len)
{
    resp_data[0]   = 0x01;
    resp_data[1]   = 0x02;
    resp_data[2]   = 0x03;
    *resp_data_len = 3;
}

/**
 * @brief 处理协议事件
 *
 * @param event 协议事件
 * @param len 事件长度
 */
void protocol_event_handle(protocol_event_t *event, uint8_t len)
{
    protocol_frame_t *frame = &event->frame;
    control_msg_t msg;
    uint8_t status         = PROTOCOL_SUCCESS_CMD; // 默认成功
    uint8_t resp_data[8]   = {0};
    uint16_t resp_data_len = 0;

    // 检查最小长度（固定头部 + 最小payload + CRC + 帧尾）
    if (len < (sizeof(uint16_t) + sizeof(uint8_t) + sizeof(frame_ctl_t) +
               sizeof(uint16_t) + sizeof(uint32_t) + sizeof(uint16_t) +
               sizeof(protocol_payload_t) + sizeof(uint16_t) + sizeof(uint16_t))) {
        return;
    }

    // 检查帧头
    if (Swap16(frame->head) != PROTOCOL_HEAD) {
        return;
    }

    // 获取payload长度（注意大端序）
    uint16_t payload_len = Swap16(frame->payload_len);

    // 计算完整帧长度
    uint16_t total_len = PROTOCOL_DEFAULT_FRONT_LENGTH + payload_len + PROTOCOL_DEFAULT_CRC_LENGTH + PROTOCOL_DEFAULT_TAIL_LENGTH;

    // 检查接收到的数据长度是否足够
    if (len < total_len) {
        return;
    }

    // 计算CRC校验值（从帧头到payload结束）
    uint16_t calc_crc = Sum_Check((uint8_t *)&frame->head,
                                  PROTOCOL_DEFAULT_FRONT_LENGTH +
                                      payload_len);

    // 获取帧中的CRC值
    uint16_t frame_crc = Swap16(*(uint16_t *)((uint8_t *)&frame->head +
                                              PROTOCOL_DEFAULT_FRONT_LENGTH +
                                              payload_len));

    // 检查CRC
    if (calc_crc != frame_crc) {
        return;
    }

    // 获取帧尾
    uint16_t frame_tail = Swap16(*(uint16_t *)((uint8_t *)&frame->head + total_len - sizeof(uint16_t)));

    // 检查帧尾
    if (frame_tail != PROTOCOL_TAIL) {
        return;
    }

    // 处理协议事件
    switch (frame->payload.main_cmd) {
        case PROTOCOL_APP_CMD:
            switch (frame->payload.sub_cmd) {
                // 部位控制
                case PROTOCOL_PARTCTRL_CMD:
                    status = part_ctrl_handle(frame, &msg);
                    // 应答
                    resp_data[0]  = frame->payload.cmd_data[0];
                    resp_data[1]  = frame->payload.cmd_data[1];
                    resp_data[2]  = frame->payload.cmd_data[2];
                    resp_data_len = 3;
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;

                // 按摩控制
                case PROTOCOL_MASSAGECTRL_CMD:
                    status = massage_ctrl_handle(frame, &msg);

                    resp_data[0]  = frame->payload.cmd_data[0];
                    resp_data[1]  = frame->payload.cmd_data[1];
                    resp_data[2]  = frame->payload.cmd_data[2];
                    resp_data_len = 3;
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;

                // 灯光控制
                case PROTOCOL_LIGHTCTRL_CMD:
                    status = light_ctrl_handle(frame, &msg);

                    resp_data[0]  = frame->payload.cmd_data[0];
                    resp_data[1]  = frame->payload.cmd_data[1];
                    resp_data_len = 2;
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;

                // 工作模式控制
                case PROTOCOL_WORKMODECTRL_CMD:
                    status = work_mode_ctrl_handle(frame, &msg);

                    resp_data[0]  = frame->payload.cmd_data[0];
                    resp_data[1]  = frame->payload.cmd_data[1];
                    resp_data_len = 2;
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;

                // 停止控制
                case PROTOCOL_STOPCTRL_CMD:
                    status = stop_ctrl_handle(frame, &msg);

                    resp_data[0]  = frame->payload.cmd_data[0];
                    resp_data[1]  = frame->payload.cmd_data[1];
                    resp_data_len = 2;
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;

                // 整体控制
                case PROTOCOL_FULLCTRL_CMD:
                    status = full_ctrl_handle(frame, &msg);

                    resp_data[0]  = frame->payload.cmd_data[0];
                    resp_data[1]  = frame->payload.cmd_data[1];
                    resp_data_len = 2;
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;

                // 记忆控制
                case PROTOCOL_MEMORYCTRL_CMD:
                    status = memory_ctrl_handle(frame, &msg);

                    resp_data[0]  = frame->payload.cmd_data[0];
                    resp_data[1]  = frame->payload.cmd_data[1];
                    resp_data_len = 2;
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;

                // 按摩模式控制
                case PROTOCOL_MASSAGEMODECTRL_CMD:
                    status = massage_mode_ctrl_handle(frame, &msg);

                    resp_data[0]  = frame->payload.cmd_data[0];
                    resp_data[1]  = frame->payload.cmd_data[1];
                    resp_data[1]  = frame->payload.cmd_data[2];
                    resp_data_len = 3;
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;

                // 按摩强度控制
                case PROTOCOL_MASSAGESTRENTHCTRL_CMD:
                    status = massage_strength_ctrl_handle(frame, &msg);

                    resp_data[0]  = frame->payload.cmd_data[0];
                    resp_data[1]  = frame->payload.cmd_data[1];
                    resp_data[2]  = frame->payload.cmd_data[2];
                    resp_data_len = 3;
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;

                // 按摩状态获取
                case PROTOCOL_MASSAGESTATUS_CMD:
                    get_massage_status_handle(resp_data, &resp_data_len);

                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;

                // 关闭按摩控制
                case PROTOCOL_CLOSEMASSAGE_CMD:
                    status = close_massage_ctrl_handle(frame, &msg);

                    resp_data[0]  = frame->payload.cmd_data[0];
                    resp_data[1]  = frame->payload.cmd_data[1];
                    resp_data_len = 2;
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;

                // 智能模式控制
                case PROTOCOL_SMARTMODECTRL_CMD:
                    status = smart_mode_ctrl_handle(frame, &msg);

                    resp_data[0]  = frame->payload.cmd_data[0];
                    resp_data[1]  = frame->payload.cmd_data[1];
                    resp_data[2]  = frame->payload.cmd_data[2];
                    resp_data_len = 3;
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;

                // 软件版本控制
                case PROTOCOL_SOFTWAREVERSION_CMD:
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;

                // 心跳控制
                case PROTOCOL_HARTBEAT_CMD:
                    break;

                // 硬件版本控制
                case PROTOCOL_HARDWAREVERSION_CMD:
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;

                default:
                    break;
            }
            break;

        // OTA控制
        case PROTOCOL_OTA_CMD:
            switch (frame->payload.sub_cmd) {
                case PROTOCOL_STARTOTA_CMD:
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;
                case PROTOCOL_OTATRANSFER_CMD:
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;
                case PROTOCOL_ENDOTA_CMD:
                    protocol_send_status_response(frame, status, resp_data, resp_data_len);
                    break;
                default:
                    protocol_send_status_response(frame, 0x01, NULL, 0);
                    break;
            }
            break;

        // 测试控制
        case PROTOCOL_TEST_CMD:
            protocol_send_status_response(frame, status, resp_data, resp_data_len);
            break;

        default:
            break;
    }
}

/**
 * @brief 发送状态响应
 *
 * @param req_frame 请求帧
 * @param status 状态
 * @param data 数据
 * @param data_len 数据长度
 */
static void protocol_send_status_response(const protocol_frame_t *req_frame, uint8_t status, const uint8_t *data, uint16_t data_len)
{
    uint8_t buf[64]        = {0};
    protocol_frame_t *resp = (protocol_frame_t *)buf;
    uint16_t payload_len   = 3 + data_len; // 状态1字节+数据
    // 填充帧头
    resp->head                = Swap16(PROTOCOL_HEAD);
    resp->version             = req_frame->version;
    resp->frame_ctl.value     = req_frame->frame_ctl.value;
    resp->seq_num             = req_frame->seq_num;
    resp->timestamp           = req_frame->timestamp;
    resp->payload_len         = Swap16(payload_len);
    resp->payload.main_cmd    = req_frame->payload.main_cmd;
    resp->payload.sub_cmd     = req_frame->payload.sub_cmd;
    resp->payload.cmd_len     = Swap16(payload_len);
    resp->payload.cmd_data[0] = status;
    if (data_len && data) {
        memcpy(&resp->payload.cmd_data[1], data, data_len);
    }
    // CRC
    uint16_t crc                                                                        = Sum_Check((uint8_t *)&resp->head, PROTOCOL_DEFAULT_FRONT_LENGTH + payload_len);
    *(uint16_t *)((uint8_t *)&resp->head + PROTOCOL_DEFAULT_FRONT_LENGTH + payload_len) = Swap16(crc);
    // 帧尾
    *(uint16_t *)((uint8_t *)&resp->head + PROTOCOL_DEFAULT_FRONT_LENGTH + payload_len + 2) = Swap16(PROTOCOL_TAIL);
    // 发送
    lin_slave_send_frame((const uint8_t *)resp, PROTOCOL_DEFAULT_FRONT_LENGTH + payload_len + 4);
}
