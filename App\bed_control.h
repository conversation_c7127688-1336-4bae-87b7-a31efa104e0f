#ifndef __BED_CONTROL_H__
#define __BED_CONTROL_H__

#include <stdint.h>
#include <stdbool.h>

#define CUSTOM_MODE_NUM 3

/**
 * @brief 多电机床位参数结构体
 * 用于描述每种模式下各电机的目标角度、速度和保持时间
 */
typedef struct {
    uint8_t back1_angle; // 背部1角度
    uint8_t back2_angle; // 背部2角度
    uint8_t waist_angle; // 腰部角度
    uint8_t leg_angle;   // 腿部角度
    uint8_t speed;       // 运动速度(1-100)
} BedMultiMotorParam;

void bed_control_set_mode(uint8_t mode);
void bed_control_up(uint8_t motor_id);
void bed_control_down(uint8_t motor_id);
void bed_control_stop(uint8_t motor_id);
void bed_control_set_custom_mode(uint8_t index);
bool bed_control_get_custom_mode(uint8_t index);    
void bed_control_rgb_on(void);
void bed_control_rgb_off(void);
void bed_control_massage_power_ctrl(void);
void bed_control_back_massage_strength_ctrl(void);
void bed_control_leg_massage_strength_ctrl(void);
void bed_control_massage_mode_ctrl(void);
void bed_control_massage_time_ctrl(void);
void bed_control_set_massage_strength(uint8_t part, uint8_t strength);
void bed_control_set_massage_mode(uint8_t part, uint8_t mode);
void bed_control_set_massage_time(uint8_t part, uint32_t time_ms, bool is_custom);
void bed_control_set_massage_power(bool on);

#endif // __BED_CONTROL_H__
