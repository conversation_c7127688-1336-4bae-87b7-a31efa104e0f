#include "bsp.h"

extern void xPortSysTickHandler(void);

/**
 * @brief 系统滴答计数器
 */
static unsigned int sysTick = 0;

/**
 * @brief 串口0输出函数
 *
 * @param buffer 输出缓冲区
 * @param size 输出数据大小
 */
static void log_output(const char *buffer, uint32_t size)
{
    // 直接查询发送数据
    // printf("%s", buffer);
    usart_send_buf(USART2, (unsigned char *)buffer, size);
}

/**
 * @brief bsp初始化
 *
 */
void bsp_init(void)
{
    bsp_ClockInit();

    LOG_CONFIG(115200); // 配置日志串口
    /* 初始化日志系统，设置日志级别为DEBUG */
    LOG_INIT(LOG_LEVEL_DEBUG);
    LOG_SET_OUTPUT(log_output); // 设置日志输出函数
    BSP_LOG_INFO("=======================================");
    BSP_LOG_INFO("          日志系统初始化完成              ");
    BSP_LOG_INFO("=======================================");

    bsp_AdcInit();
    BSP_LOG_DEBUG("ADC初始化完成");

    bsp_PwmInit();
    BSP_LOG_DEBUG("PWM初始化完成");

    bsp_BuzzerInit();
    BSP_LOG_DEBUG("蜂鸣器初始化完成");

    bsp_RadarInit();
    BSP_LOG_DEBUG("雷达初始化完成");

    bsp_LedInit();
    BSP_LOG_DEBUG("LED初始化完成");

    bsp_LvdInit();
    BSP_LOG_INFO("LVD初始化完成");

    // bsp_WatchdogInit();
    // BSP_LOG_INFO("看门狗初始化完成");

    BSP_LOG_INFO("BSP初始化完成");
}

/**
 * @brief Systick中断处理
 *
 */
void SysTick_IRQHandler(void)
{
    sysTick++;

    if (xTaskGetSchedulerState() != taskSCHEDULER_NOT_STARTED) {
        xPortSysTickHandler();
    }
}

/**
 * @brief 获取当前时间
 *
 * @return uint32_t 当前时间
 */
uint32_t BSP_GetTickCount(void)
{
    uint32_t ticks;

    taskENTER_CRITICAL();

    ticks = sysTick;

    taskEXIT_CRITICAL();

    return ticks;
}
