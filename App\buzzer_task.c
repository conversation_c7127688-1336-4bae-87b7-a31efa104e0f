/**
 * @file buzzer_task.c
 * <AUTHOR> (<EMAIL>)
 * @brief 蜂鸣器任务实现
 * @version 0.1
 * @date 2025-05-06
 *
 * @copyright Copyright (c) 2025
 *
 */
#include "buzzer_task.h"

// 任务与队列句柄
static QueueHandle_t s_buzzer_queue      = NULL; // 消息队列句柄
static TaskHandle_t s_buzzer_task_handle = NULL; // 任务句柄

// ======================== 状态变量 ========================

static uint8_t s_buzzer_playing            = 0;                  // 播放状态标志
static uint8_t s_buzzer_paused             = 0;                  // 暂停状态标志
static uint8_t s_buzzer_volume             = 100;                // 当前音量(0-100)
static buzzer_play_mode_t s_buzzer_mode    = BUZZER_MODE_ONCE;   // 当前播放模式
static uint8_t s_buzzer_repeat_count       = 0;                  // 总重复次数(0=无限)
static uint8_t s_buzzer_repeat_index       = 0;                  // 当前已重复次数
static const buzzer_note_t *s_buzzer_notes = NULL;               // 当前音符序列指针
static uint8_t s_buzzer_note_count         = 0;                  // 当前音符数量
static uint8_t s_buzzer_note_index         = 0;                  // 当前音符索引
static uint16_t s_buzzer_elapsed_time      = 0;                  // 当前音符已播放时长(ms)
static buzzer_note_t s_buzzer_chord_buf[BUZZER_MAX_CHORD_NOTES]; // 音符缓冲区
static uint16_t s_buzzer_repeat_interval = 0;                    // 新增：重复播放间隔(ms)

// ======================== 内部辅助函数声明 ========================

/**
 * @brief 播放单个音符
 * @param note 音符指针
 */
static void buzzer_play_note(const buzzer_note_t *note);

/**
 * @brief 停止播放，重置状态
 */
static void buzzer_stop_playback(void);

/**
 * @brief 状态变量重置
 */
static void buzzer_task_reset_state(void);

// ======================== 主任务循环 ========================

/**
 * @brief 蜂鸣器任务主循环
 * @param param 任务参数(未用)
 */
static void buzzer_task_entry(void *param)
{
    buzzer_msg_t msg;
    TickType_t last_wake       = xTaskGetTickCount();
    uint8_t need_gap           = 0;
    uint16_t gap_timer         = 0;
    uint8_t need_play_new_note = 1;
    uint16_t repeat_gap_timer  = 0; // 新增：重复间隔计时
    uint8_t need_repeat_gap    = 0; // 新增：是否需要重复间隔
    (void)param;
    buzzer_task_reset_state();

    for (;;) {
        if (xQueueReceive(s_buzzer_queue, &msg, 0) == pdPASS) {
            switch (msg.type) {
                case BUZZER_MSG_PLAY_MUSIC:
                case BUZZER_MSG_PLAY_MUSIC_EX: {
                    const buzzer_music_t *music = Buzzer_GetMusicData(msg.data.play_music.music_type);
                    if (music && music->notes && music->note_count > 0 && music->note_count <= BUZZER_MAX_CHORD_NOTES) {
                        memcpy(s_buzzer_chord_buf, music->notes, sizeof(buzzer_note_t) * music->note_count);
                        s_buzzer_notes           = s_buzzer_chord_buf;
                        s_buzzer_note_count      = music->note_count;
                        s_buzzer_note_index      = 0;
                        s_buzzer_elapsed_time    = 0;
                        s_buzzer_mode            = msg.data.play_music.mode;
                        s_buzzer_repeat_count    = msg.data.play_music.repeat;
                        s_buzzer_repeat_index    = 0;
                        s_buzzer_playing         = 1;
                        s_buzzer_paused          = 0;
                        need_gap                 = 0;
                        need_play_new_note       = 1;
                        s_buzzer_repeat_interval = msg.data.play_music.repeat_interval; // 修正：全局变量赋值
                        repeat_gap_timer         = 0;
                        need_repeat_gap          = 0;
                    } else {
                        buzzer_stop_playback();
                    }
                    break;
                }
                case BUZZER_MSG_PLAY_CHORD:
                case BUZZER_MSG_PLAY_CHORD_EX: {
                    if (msg.data.play_chord.notes && msg.data.play_chord.count > 0 && msg.data.play_chord.count <= BUZZER_MAX_CHORD_NOTES) {
                        memcpy(s_buzzer_chord_buf, msg.data.play_chord.notes, sizeof(buzzer_note_t) * msg.data.play_chord.count);
                        s_buzzer_notes        = s_buzzer_chord_buf;
                        s_buzzer_note_count   = msg.data.play_chord.count;
                        s_buzzer_note_index   = 0;
                        s_buzzer_elapsed_time = 0;
                        s_buzzer_mode         = msg.data.play_chord.mode;
                        s_buzzer_repeat_count = msg.data.play_chord.repeat;
                        s_buzzer_repeat_index = 0;
                        s_buzzer_playing      = 1;
                        s_buzzer_paused       = 0;
                        need_gap              = 0;
                        need_play_new_note    = 1;
                    } else {
                        buzzer_stop_playback();
                    }
                    break;
                }
                case BUZZER_MSG_STOP:
                    buzzer_stop_playback();
                    need_play_new_note = 1;
                    break;
                case BUZZER_MSG_PAUSE:
                    if (s_buzzer_playing && !s_buzzer_paused) {
                        s_buzzer_paused = 1;
                        bsp_BuzzerOff();
                    }
                    break;
                case BUZZER_MSG_RESUME:
                    if (s_buzzer_playing && s_buzzer_paused) {
                        s_buzzer_paused    = 0;
                        need_play_new_note = 1;
                    }
                    break;
                case BUZZER_MSG_SET_VOLUME:
                    s_buzzer_volume = (msg.data.volume > 100) ? 100 : msg.data.volume;
                    break;
                case BUZZER_MSG_BEEP: {
                    buzzer_note_t note = {
                        .freq         = msg.data.beep.freq,
                        .duration     = msg.data.beep.time_ms,
                        .duty         = 70,
                        .power_stable = 0,
                        .attack       = BUZZER_DEFAULT_ATTACK,
                        .decay        = BUZZER_DEFAULT_DECAY,
                        .sustain      = BUZZER_DEFAULT_SUSTAIN,
                        .release      = BUZZER_DEFAULT_RELEASE,
                        .tone         = BUZZER_TONE_SQUARE};
                    memcpy(s_buzzer_chord_buf, &note, sizeof(buzzer_note_t));
                    s_buzzer_notes        = s_buzzer_chord_buf;
                    s_buzzer_note_count   = 1;
                    s_buzzer_note_index   = 0;
                    s_buzzer_elapsed_time = 0;
                    s_buzzer_mode         = BUZZER_MODE_ONCE;
                    s_buzzer_repeat_count = 0;
                    s_buzzer_repeat_index = 0;
                    s_buzzer_playing      = 1;
                    s_buzzer_paused       = 0;
                    need_gap              = 0;
                    need_play_new_note    = 1;
                    break;
                }
                default:
                    break;
            }
        }
        if (s_buzzer_playing && !s_buzzer_paused) {
            if (s_buzzer_notes && s_buzzer_note_index < s_buzzer_note_count) {
                const buzzer_note_t *note = &s_buzzer_notes[s_buzzer_note_index];
                if (need_gap) {
                    if (gap_timer < BUZZER_NOTE_GAP_MS) {
                        gap_timer += BUZZER_TICK_MS;
                    } else {
                        gap_timer          = 0;
                        need_gap           = 0;
                        need_play_new_note = 1;
                    }
                } else {
                    if (need_play_new_note) {
                        buzzer_play_note(note);
                        need_play_new_note = 0;
                    }
                    // 每tick动态推进包络
                    if (note->freq > 0) {
                        uint8_t base_duty = (note->duty > 0) ? note->duty : 70;
                        uint8_t duty      = ((base_duty * s_buzzer_volume) / 100);
                        if (note->freq > 2000) {
                            duty = (duty * 80) / 100;
                        }
                        if (duty < 10) duty = 10;
                        if (duty > 95) duty = 95;
                        bsp_BuzzerSetFreq(note->freq, duty, s_buzzer_elapsed_time, note->duration);
                    }
                    s_buzzer_elapsed_time += BUZZER_TICK_MS;
                    if (s_buzzer_elapsed_time >= s_buzzer_notes[s_buzzer_note_index].duration) {
                        bsp_BuzzerOff();
                        s_buzzer_note_index++;
                        s_buzzer_elapsed_time = 0;
                        if (s_buzzer_note_index < s_buzzer_note_count) {
                            need_gap           = 1;
                            gap_timer          = 0;
                            need_play_new_note = 1;
                        }
                    }
                }
            } else if (s_buzzer_notes && s_buzzer_note_index >= s_buzzer_note_count) {
                // 音符序列播放完毕，进入repeat间隔等待
                if (s_buzzer_mode == BUZZER_MODE_REPEAT && (s_buzzer_repeat_count == 0 || s_buzzer_repeat_index + 1 < s_buzzer_repeat_count)) {
                    if (s_buzzer_repeat_interval > 0) {
                        if (!need_repeat_gap) {
                            need_repeat_gap  = 1;
                            repeat_gap_timer = 0;
                        }
                        if (repeat_gap_timer < s_buzzer_repeat_interval) {
                            repeat_gap_timer += BUZZER_TICK_MS;
                        } else {
                            need_repeat_gap  = 0;
                            repeat_gap_timer = 0;
                            if (s_buzzer_repeat_count > 0) s_buzzer_repeat_index++;
                            s_buzzer_note_index   = 0;
                            s_buzzer_elapsed_time = 0;
                            need_gap              = 0;
                            need_play_new_note    = 1;
                        }
                    } else {
                        if (s_buzzer_repeat_count > 0) s_buzzer_repeat_index++;
                        s_buzzer_note_index   = 0;
                        s_buzzer_elapsed_time = 0;
                        need_gap              = 0;
                        need_play_new_note    = 1;
                    }
                } else {
                    buzzer_stop_playback();
                    need_play_new_note = 1;
                }
            }
        }
        // 关键：每次循环只延时一次，保证tick准确
        vTaskDelayUntil(&last_wake, pdMS_TO_TICKS(BUZZER_TICK_MS));
    }
}

// ======================== 内部辅助函数实现 ========================

/**
 * @brief 播放单个音符，设置ADSR、频率、占空比等
 * @param note 音符指针
 */
static void buzzer_play_note(const buzzer_note_t *note)
{
    if (note->freq > 0) {
        uint8_t attack  = note->attack > 0 ? note->attack : BUZZER_DEFAULT_ATTACK;
        uint8_t decay   = note->decay > 0 ? note->decay : BUZZER_DEFAULT_DECAY;
        uint8_t sustain = note->sustain > 0 ? note->sustain : BUZZER_DEFAULT_SUSTAIN;
        uint8_t release = note->release > 0 ? note->release : BUZZER_DEFAULT_RELEASE;
        bsp_BuzzerSetADSR(attack, decay, sustain, release);
        bsp_BuzzerOn(note->freq);
    } else {
        bsp_BuzzerOff();
    }
}

/**
 * @brief 停止播放，重置所有状态
 */
static void buzzer_stop_playback(void)
{
    bsp_BuzzerOff();
    buzzer_task_reset_state();
}

/**
 * @brief 状态变量重置
 */
static void buzzer_task_reset_state(void)
{
    s_buzzer_playing      = 0;
    s_buzzer_paused       = 0;
    s_buzzer_notes        = NULL;
    s_buzzer_note_count   = 0;
    s_buzzer_note_index   = 0;
    s_buzzer_elapsed_time = 0;
    s_buzzer_repeat_count = 0;
    s_buzzer_repeat_index = 0;
    s_buzzer_mode         = BUZZER_MODE_ONCE;
}

// ======================== 任务初始化与API实现 ========================

/**
 * @brief 初始化蜂鸣器任务
 */
void buzzer_task_init(void)
{
    if (s_buzzer_queue == NULL) {
        s_buzzer_queue = xQueueCreate(BUZZER_QUEUE_LEN, sizeof(buzzer_msg_t));
        if (s_buzzer_queue == NULL) {
            BUZZER_LOG_ERROR("buzzer_task_init: xQueueCreate failed");
        }
    }
    if (s_buzzer_task_handle == NULL) {
        BaseType_t xReturn = xTaskCreate(buzzer_task_entry,
                                         "buzzer_task",
                                         APP_BUZZER_TASK_STACK_SIZE,
                                         NULL,
                                         APP_BUZZER_PRIO,
                                         &s_buzzer_task_handle);
        if (xReturn != pdPASS) {
            BUZZER_LOG_ERROR("buzzer_task_init: xTaskCreate failed");
        }
    }
}

/**
 * @brief 播放预定义音乐
 * @param type 预定义音乐类型
 */
void Buzzer_PlayMusic(buzzer_music_type_t type)
{
    buzzer_msg_t msg               = {0};
    msg.type                       = BUZZER_MSG_PLAY_MUSIC;
    msg.data.play_music.music_type = type;
    msg.data.play_music.mode       = BUZZER_MODE_ONCE;
    msg.data.play_music.repeat     = 0;
    if (s_buzzer_queue) {
        xQueueSend(s_buzzer_queue, &msg, 0);
    }
}

/**
 * @brief 扩展参数播放预定义音乐
 * @param type 预定义音乐类型
 * @param mode 播放模式
 * @param repeat 重复次数
 * @param repeat_interval_ms 每次重复播放间隔(ms)
 */
void Buzzer_PlayMusicEx(buzzer_music_type_t type, buzzer_play_mode_t mode, uint8_t repeat, uint16_t repeat_interval_ms)
{
    buzzer_msg_t msg                    = {0};
    msg.type                            = BUZZER_MSG_PLAY_MUSIC_EX;
    msg.data.play_music.music_type      = type;
    msg.data.play_music.mode            = mode;
    msg.data.play_music.repeat          = repeat;
    msg.data.play_music.repeat_interval = repeat_interval_ms;
    if (s_buzzer_queue) {
        xQueueSend(s_buzzer_queue, &msg, 0);
    }
}

/**
 * @brief 播放自定义和弦
 * @param notes 音符序列指针
 * @param count 音符数量
 */
void Buzzer_PlayChord(const buzzer_note_t *notes, uint8_t count)
{
    buzzer_msg_t msg           = {0};
    msg.type                   = BUZZER_MSG_PLAY_CHORD;
    msg.data.play_chord.notes  = notes;
    msg.data.play_chord.count  = count;
    msg.data.play_chord.mode   = BUZZER_MODE_ONCE;
    msg.data.play_chord.repeat = 0;
    if (s_buzzer_queue) {
        xQueueSend(s_buzzer_queue, &msg, 0);
    }
}

/**
 * @brief 扩展参数播放自定义和弦
 * @param notes 音符序列指针
 * @param count 音符数量
 * @param mode 播放模式
 * @param repeat 重复次数
 */
void Buzzer_PlayChordEx(const buzzer_note_t *notes, uint8_t count, buzzer_play_mode_t mode, uint8_t repeat)
{
    buzzer_msg_t msg           = {0};
    msg.type                   = BUZZER_MSG_PLAY_CHORD_EX;
    msg.data.play_chord.notes  = notes;
    msg.data.play_chord.count  = count;
    msg.data.play_chord.mode   = mode;
    msg.data.play_chord.repeat = repeat;
    if (s_buzzer_queue) {
        xQueueSend(s_buzzer_queue, &msg, 0);
    }
}

/**
 * @brief 停止播放
 */
void Buzzer_Stop(void)
{
    buzzer_msg_t msg = {0};
    msg.type         = BUZZER_MSG_STOP;
    if (s_buzzer_queue) {
        xQueueSend(s_buzzer_queue, &msg, 0);
    }
}

/**
 * @brief 暂停播放
 */
void Buzzer_Pause(void)
{
    buzzer_msg_t msg = {0};
    msg.type         = BUZZER_MSG_PAUSE;
    if (s_buzzer_queue) {
        xQueueSend(s_buzzer_queue, &msg, 0);
    }
}

/**
 * @brief 恢复播放
 */
void Buzzer_Resume(void)
{
    buzzer_msg_t msg = {0};
    msg.type         = BUZZER_MSG_RESUME;
    if (s_buzzer_queue) {
        xQueueSend(s_buzzer_queue, &msg, 0);
    }
}

/**
 * @brief 设置音量(0-100)
 * @param volume 音量百分比
 */
void Buzzer_SetVolume(uint8_t volume)
{
    buzzer_msg_t msg = {0};
    msg.type         = BUZZER_MSG_SET_VOLUME;
    msg.data.volume  = volume;
    if (s_buzzer_queue) {
        xQueueSend(s_buzzer_queue, &msg, 0);
    }
}

/**
 * @brief 播放单音beep
 * @param freq 频率(Hz)
 * @param time_ms 持续时间(ms)
 */
void Buzzer_Beep(uint16_t freq, uint16_t time_ms)
{
    buzzer_msg_t msg      = {0};
    msg.type              = BUZZER_MSG_BEEP;
    msg.data.beep.freq    = freq;
    msg.data.beep.time_ms = time_ms;
    if (s_buzzer_queue) {
        xQueueSend(s_buzzer_queue, &msg, 0);
    }
}

/**
 * @brief 获取播放状态
 * @return 1:正在播放 0:未播放
 */
uint8_t Buzzer_IsPlaying(void)
{
    return s_buzzer_playing;
}

/**
 * @brief 获取暂停状态
 * @return 1:已暂停 0:未暂停
 */
uint8_t Buzzer_IsPaused(void)
{
    return s_buzzer_paused;
}