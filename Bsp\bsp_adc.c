/**
 * @file bsp_adc.c
 * <AUTHOR> (<EMAIL>)
 * @brief adc驱动
 * @version 0.1
 * @date 2024-09-11
 *
 * @copyright Copyright (c) 2024
 *
 */
#include "bsp.h"
#include "stdlib.h"
#include <stdio.h>

#ifdef USE_ADC

#define PRINT(window, fmt, args...) printf("{" #window "}" fmt "\n", ##args);

// 静态变量定义
static uint16_t adc_buffer[AD_FILTER_NUM][ADC_CHANNEL_NUM]; // dma数据缓冲区
static struct adFilter_t adCheck[ADC_CHANNEL_NUM];          // 电流检测结构体
static uint16_t commutation_count[ADC_CHANNEL_NUM] = {0};   // 换相计数

// ==== ADC融合算法参数全局变量及初始化 ====
adc_param_t g_adc_param = {
    .filter_window = AD_FILTER_NUM,
    .iir_alpha     = 0.1f};

// ==== 卡尔曼滤波结构体与函数实现 ====
static kalman_filter_t kalman_filters[ADC_CHANNEL_NUM];

/**
 * @brief 卡尔曼滤波器初始化
 *
 * @param kf 卡尔曼滤波器结构体指针
 * @param q 过程噪声协方差
 * @param r 测量噪声协方差
 * @param init_x 初始估计值
 */
void kalman_init(kalman_filter_t *kf, float q, float r, float init_x)
{
    kf->q     = q;
    kf->r     = r;
    kf->x_hat = init_x;
    kf->p     = 1.0f;
}

/**
 * @brief 卡尔曼滤波更新
 *
 * @param kf 卡尔曼滤波器结构体指针
 * @param measurement 测量值
 * @return 滤波后的估计值
 */
float kalman_update(kalman_filter_t *kf, float measurement)
{
    kf->p += kf->q;
    kf->k = kf->p / (kf->p + kf->r);
    kf->x_hat += kf->k * (measurement - kf->x_hat);
    kf->p *= (1.0f - kf->k);
    return kf->x_hat;
}

/**
 * @brief ADC融合算法参数初始化
 * @param param 参数结构体指针
 */
void adc_param_init(const adc_param_t *param)
{
    if (param) {
        g_adc_param = *param;
    }
}

/**
 * @brief DMA通道配置
 * @param channel_id DMA通道
 */
static void adc_dma_config(en_dma_channel_t channel_id)
{
    stc_dmac_channel_init_t stcDmacChInit = {0};

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralDma);

    stcDmacChInit.u32Priority      = DMA_FIX_PRIORITY;
    stcDmacChInit.u32TrigSrc       = DMA_TRISRC_ADC0_SINGLE_EOC;
    stcDmacChInit.u32BlockCnt      = 1; // 单次传输
    stcDmacChInit.u32TransferCnt   = ADC_CHANNEL_NUM * AD_FILTER_NUM;
    stcDmacChInit.u32TransferMode  = DMA_BLOCK_MODE;
    stcDmacChInit.u32DataSize      = DMA_DATA_HALFWORD;
    stcDmacChInit.u32SrcAddrFix    = DMA_SRCADDR_FIX;
    stcDmacChInit.u32DstAddrFix    = DMA_DSTADDR_INC;
    stcDmacChInit.u32BTCntReload   = DMA_BTCNT_RELOAD_ENABLE;
    stcDmacChInit.u32SrcAddrReload = DMA_SRCADDR_RELOAD_ENABLE;
    stcDmacChInit.u32DstAddrReload = DMA_DSTADDR_RELOAD_ENABLE;
    stcDmacChInit.u32SrcAddr       = (uint32_t)(&(HC_ADC0->RESULT));
    stcDmacChInit.u32DstAddr       = (uint32_t)&adc_buffer;
    stcDmacChInit.u32ChanelEnMsk   = DMA_KEEP_ENABLE;

    DMACCH_Init(HC_DMAC, channel_id, &stcDmacChInit);

    ADC_LOG_DEBUG("DMA通道%d配置完成", channel_id);
}

/**
 * @brief ADC采样端口初始化
 */
static void adc_port_init(void)
{
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpioa);
    SYSCTRL_PeriphClkEnable(SysctrlPeripheralGpioc);

    GPIO_PA00_ANALOG_SET(); // MOTOR_LEG1_CURR_CH
    GPIO_PA01_ANALOG_SET(); // MOTOR_HEAD2_CURR_CH
    GPIO_PC00_ANALOG_SET(); // MOTOR_VIB2_CURR_CH
    GPIO_PC01_ANALOG_SET(); // MOTOR_VIB1_CURR_CH
    GPIO_PC02_ANALOG_SET(); // MOTOR_LEG2_CURR_CH
    GPIO_PC03_ANALOG_SET(); // MOTOR_HEAD1_CURR_CH
}

/**
 * @brief ADC模块初始化
 */
static void adc_module_config(void)
{
    stc_adc_cfg_t stcAdcCfg = {0};

    SYSCTRL_PeriphClkEnable(SysctrlPeripheralAdc0);
    ADC_BGR_Enable(HC_ADC0);

    stcAdcCfg.AdcSampCycleSel = ADC_SAMPLINGTIME_3CYCLE;
    stcAdcCfg.AdcOpBuf        = ADC_BUFF_DISABLE;
    stcAdcCfg.AdcRefVolSel    = ADC_REF_VOLTAGE_AVCC;
    stcAdcCfg.AdcClkDiv       = ADC_CLOCK_PCLK_DIV4;
    stcAdcCfg.RaccEn          = ADC_RACCCLR_ENABLE;
    stcAdcCfg.OvMode          = ADC_OVMD_ENABLE;
    stcAdcCfg.AdcAlign        = ADC_RIGHT_ALIGN_ENABLE;
    stcAdcCfg.AdcMode         = ADC_CONVERSION_MODE_CONTINUOUS;

    ADC_Init(HC_ADC0, &stcAdcCfg);
    ADC_ClearFlag_ALL(HC_ADC0);
}

/**
 * @brief ADC通道配置
 */
static void adc_channel_config(void)
{
    // 按照电机通道枚举顺序配置
    ADC_SetChannelInputSource(HC_ADC0, ADC_SQRCH0MUX, MOTOR1_CURR_CH);     // 头部电机1
    ADC_SetChannelInputSource(HC_ADC0, ADC_SQRCH1MUX, MOTOR2_CURR_CH);     // 头部电机2
    ADC_SetChannelInputSource(HC_ADC0, ADC_SQRCH2MUX, MOTOR3_CURR_CH);     // 腿部电机1
    ADC_SetChannelInputSource(HC_ADC0, ADC_SQRCH3MUX, MOTOR4_CURR_CH);     // 腿部电机2
    ADC_SetChannelInputSource(HC_ADC0, ADC_SQRCH4MUX, MOTOR_VIB1_CURR_CH); // 振动电机1
    ADC_SetChannelInputSource(HC_ADC0, ADC_SQRCH5MUX, MOTOR_VIB2_CURR_CH); // 振动电机2

    ADC_Set_SQR_ChCnt(HC_ADC0, ADC_CHANNEL_NUM - 1);
    ADC_SetExtTrigger0Source(HC_ADC0, ADCMSKTRIGGTIMER0); // 定时器0触发

    // ADC_ClearFlag(HC_ADC0, ADC_FLAG_EOS | ADC_FLAG_EOC);
    // ADC_EnableIT(HC_ADC0, ADC_IT_EOS | ADC_IT_EOC);
    // EnableNvic(ADC0_ADC1_IRQn, IrqLevel4, TRUE);
}

/**
 * @brief ADC校准函数
 */
static void adc_calibration(void)
{
    delay1ms(100);

    for (uint8_t ch = 0; ch < ADC_CHANNEL_NUM; ch++) {
        uint32_t sum     = 0;
        uint16_t min     = 0xFFFF;
        uint16_t max     = 0;
        uint16_t samples = 0;

        // 用DMA采集到的历史数据做统计
        for (uint16_t i = 0; i < AD_FILTER_NUM; i++) {
            uint16_t value = adc_buffer[i][ch];
            sum += value;
            if (value < min) min = value;
            if (value > max) max = value;
            samples++;
        }

        // 判断数据是否有效
        if ((max - min) > ADC_MAX_VARIATION) {
            ADC_LOG_WARN("ADC校准数据无效，通道%u: 变化范围%u", (unsigned int)ch, (unsigned int)(max - min));
            adCheck[ch].offset = ADC_RESOLUTION / 2; // 用默认值
            ADC_LOG_ERROR("ADC通道%u校准失败，使用默认偏移量", (unsigned int)ch);
        } else {
            adCheck[ch].offset = (uint16_t)(sum / samples); // 用均值做偏置
            ADC_LOG_INFO("ADC通道%u校准完成: 偏移量=%u, 样本数=%u", (unsigned int)ch, (unsigned int)adCheck[ch].offset, (unsigned int)samples);
        }

        // 其它参数初始化
        adCheck[ch].filtered_value   = 0;
        adCheck[ch].peak_value       = 0;
        adCheck[ch].current_integral = 0;
    }
}

/**
 * @brief ADC中断处理函数
 */
void Adc_IRQHandler(void)
{
    if (TRUE == ADC_IsActiveFlag(HC_ADC0, ADC_FLAG_EOS)) {
        ADC_ClearFlag(HC_ADC0, ADC_FLAG_EOS);
    }

    if (TRUE == ADC_IsActiveFlag(HC_ADC0, ADC_FLAG_EOC)) {
        ADC_ClearFlag(HC_ADC0, ADC_FLAG_EOC);
    }
}

/**
 * @brief 将ADC值转换为电流值(mA)
 * @param adc_value ADC采样值
 * @param offset ADC偏置值
 * @return float 电流值(mA)
 */
static float adc_to_current(uint16_t adc_value, uint16_t offset)
{
    // 1. 计算实际ADC值（减去偏置）
    int32_t actual_adc = (adc_value > offset) ? (adc_value - offset) : 0;

    // 2. 计算电压值(mV)
    // ADC_VREF单位是mV，ADC_RESOLUTION是分辨率位数对应的满程值
    float voltage_mv = (float)actual_adc * ADC_VREF / ADC_RESOLUTION;

    // 3. 计算实际电流值(mA)
    // 低端电流检测计算过程：
    // 1) 采样电阻两端电压 = 运放输出电压 / 运放增益
    // 2) 实际电流 = 采样电阻两端电压 / 采样电阻值
    // 3) 转换为mA单位
    float current = (voltage_mv / CURRENT_AMP_GAIN) / CURRENT_SENSE_RESISTOR;

    // 4. 添加合理性检查
    if (current < 0.0f) current = 0.0f;

    return current;
}

/**
 * @brief 电流检测处理
 * @param ch 通道号
 * @param mean 均值
 */
static void current_detect_process(uint8_t ch, float mean)
{
    static float iir_filtered[ADC_CHANNEL_NUM] = {0};
    // IIR滤波
    iir_filtered[ch] = (1.0f - g_adc_param.iir_alpha) * iir_filtered[ch] + g_adc_param.iir_alpha * mean;
    // 转换为电流值（不再减偏置，offset传0）
    adCheck[ch].current_ma = adc_to_current((uint16_t)iir_filtered[ch], 0);
    // 更新峰值电流
    if (iir_filtered[ch] > adCheck[ch].peak_value) {
        adCheck[ch].peak_value = (uint16_t)iir_filtered[ch];
    }
    // --- 积分改进算法 ---
    // 先衰减
    adCheck[ch].current_integral *= BLOCK_DECAY_FACTOR;
    // 只对超出堵转阈值部分积分
    float delta = adCheck[ch].current_ma - BLOCK_CURRENT_THRESHOLD;
    if (delta > 0) {
        adCheck[ch].current_integral += delta;
    }
    if (adCheck[ch].current_integral < 0) adCheck[ch].current_integral = 0;
    // 堵转保护
    if (adCheck[ch].current_integral > BLOCK_INTEGRAL_LIMIT) {
        if (commutation_count[ch] < BLOCK_COMMUTATION_MIN_COUNT) {
            adCheck[ch].is_blocked = true;
        }
    }
    // 恢复判据
    if (adCheck[ch].is_blocked) {
        if (adCheck[ch].current_integral < BLOCK_RECOVERY_THRESHOLD &&
            commutation_count[ch] > BLOCK_RECOVERY_COMM_COUNT) {
            adCheck[ch].is_blocked       = false;
            adCheck[ch].current_integral = 0;
        }
    }
    // --- 过流保护接口 ---
    if (adCheck[ch].current_ma > OVERCURRENT_THRESHOLD) {
        adCheck[ch].is_overcurrent = true;
    } else {
        adCheck[ch].is_overcurrent = false;
    }
}

/**
 * @brief ADC数据处理
 */
void adc_data_process(const uint8_t *dir_array)
{
    for (uint8_t ch = 0; ch < ADC_CHANNEL_NUM; ch++) {
        float kalman_buf[AD_FILTER_NUM];
        // 统计噪声水平（先采样后再赋值Q/R）
        float sum = 0, sum_sq = 0;
        for (uint16_t i = 0; i < g_adc_param.filter_window; i++) {
            int32_t val = (int32_t)adc_buffer[i][ch] - (int32_t)adCheck[ch].offset;
            if (val < 0) val = 0;
            sum += val;
            sum_sq += val * val;
        }
        // 计算均值
        float mean = sum / g_adc_param.filter_window;
        // 计算噪声水平
        float variance = (sum_sq / g_adc_param.filter_window) - (mean * mean);
        // 计算标准差
        float stddev = sqrtf(variance > 0 ? variance : 0);
        // --- 卡尔曼Q/R自适应 ---
        float R = stddev * 1.0f;
        if (R < 0.5f) R = 0.5f;
        if (R > 10.0f) R = 10.0f;
        // float Q = (dir_array[ch] != 0) ? 0.1f : 0.01f;
        float Q              = 0.001f + 0.01f * stddev; // 线性响应标准差
        kalman_filters[ch].q = Q;
        kalman_filters[ch].r = R;
        // 卡尔曼滤波
        for (uint16_t i = 0; i < g_adc_param.filter_window; i++) {
            int32_t val = (int32_t)adc_buffer[i][ch] - (int32_t)adCheck[ch].offset;
            if (val < 0) val = 0;
            kalman_buf[i] = kalman_update(&kalman_filters[ch], (float)val);
        }
        // 统计噪声水平
        float sum_buf = 0, sum_sq_buf = 0;
        for (uint16_t i = 0; i < g_adc_param.filter_window; i++) {
            sum_buf += kalman_buf[i];
            sum_sq_buf += kalman_buf[i] * kalman_buf[i];
        }
        float mean_buf     = sum_buf / g_adc_param.filter_window;
        float variance_buf = (sum_sq_buf / g_adc_param.filter_window) - (mean_buf * mean_buf);
        float stddev_buf   = sqrtf(variance_buf > 0 ? variance_buf : 0);
        // 自适应斜率阈值
        float adaptive_coef   = (dir_array[ch] != 0) ? 0.15f : 2.0f; // 运动时灵敏，静止时保守
        float slope_threshold = stddev_buf * adaptive_coef;
        if (slope_threshold < 1.0f) slope_threshold = 1.0f;
        if (slope_threshold > 50.0f) slope_threshold = 50.0f;

        // 换相检测和计数（只在运动时）
        if (dir_array[ch] != 0) {
            // 斜率检测
            bool commutated = false;
            for (uint16_t i = 1; i < g_adc_param.filter_window; i++) {
                // float diff = kalman_buf[i] - kalman_buf[i-1];
                float diff = (kalman_buf[i + 1] - kalman_buf[i - 1]) / 2.0f;
                if (fabsf(diff) > slope_threshold) {
                    commutation_count[ch]++;
                    commutated = true;
                    break;
                }
            }
            // 极值检测（可选增强）
            if (!commutated) {
                for (uint16_t i = 2; i < g_adc_param.filter_window - 2; i++) {
                    float diff = (kalman_buf[i + 1] - kalman_buf[i - 1]) / 2.0f;
                    if (kalman_buf[i] > kalman_buf[i - 1] && kalman_buf[i] > kalman_buf[i + 1] &&
                        (kalman_buf[i] - kalman_buf[i - 1] > slope_threshold) &&
                        (kalman_buf[i] - kalman_buf[i + 1] > slope_threshold)) {
                        if (fabsf(diff) > slope_threshold) {
                            commutation_count[ch]++;
                            break;
                        }
                    }
                }
            }

            // 电流检测与保护逻辑（始终执行）
            current_detect_process(ch, mean);

            // 优化调试输出：每10ms只输出一行关键特征参数
            // if (ch == 0) {
            //     int comm_index = -1;
            //     for (uint16_t i = 1; i < g_adc_param.filter_window; i++) {
            //         float diff = kalman_buf[i] - kalman_buf[i-1];
            //         if (fabsf(diff) > slope_threshold && comm_index == -1) {
            //             comm_index = i;
            //         }
            //     }
            //     PRINT(Debug, "%.2f,%.2f,%.2f,%d", mean, stddev, slope_threshold, comm_index);
            // }
        }
    }
}

/**
 * @brief ADC初始化
 */
void bsp_AdcInit(void)
{
    ADC_LOG_INFO("开始初始化ADC模块");

    adc_dma_config(DMA_CHANNEL0);
    ADC_LOG_DEBUG("ADC DMA配置完成");

    adc_port_init();
    ADC_LOG_DEBUG("ADC端口初始化完成");

    adc_module_config();
    ADC_LOG_DEBUG("ADC模块配置完成");

    adc_channel_config();
    ADC_LOG_DEBUG("ADC通道配置完成");

    ADC_Enable_EOC_DMATransfer(HC_ADC0);
    ADC_LOG_DEBUG("ADC EOC DMA传输使能完成");

    DMACCH_Enable(HC_DMAC, DMA_CHANNEL0);
    DMAC_Enable();

    ADC_Enable(HC_ADC0);
    ADC_LOG_DEBUG("ADC模块使能完成");

    bsp_timer_init();
    BSP_LOG_DEBUG("定时器初始化完成");

    adc_calibration();
    ADC_LOG_DEBUG("ADC校准完成");

    ADC_LOG_INFO("ADC初始化完成");

    adc_param_init(&g_adc_param);
    // ==== 初始化卡尔曼滤波器 ====
    for (uint8_t ch = 0; ch < ADC_CHANNEL_NUM; ch++) {
        float init_x = (float)adc_buffer[0][ch] - (float)adCheck[ch].offset;
        kalman_init(&kalman_filters[ch], 0.1f, 2.0f, init_x); // Q=0.1, R=2.0, 初始值用首采样点
    }
}

/**
 * @brief 获取滤波后的电流值(mA)
 * @param ch 通道号
 * @return 电流值(mA)
 */
uint16_t bsp_GetFilteredCurrent(uint8_t ch)
{
    if (ch >= ADC_CHANNEL_NUM) {
        ADC_LOG_ERROR("无效的ADC通道: %d", ch);
        return 0;
    }

    return (uint16_t)adCheck[ch].current_ma;
}

/**
 * @brief 获取换相计数
 * @param ch 通道号
 * @return 换相次数
 */
uint16_t get_commutation_count(uint8_t ch)
{
    if (ch < ADC_CHANNEL_NUM) {
        return commutation_count[ch];
    }
    return 0;
}

/**
 * @brief 重置换相计数
 * @param ch 通道号
 */
void commutation_count_reset(uint8_t ch)
{
    if (ch < ADC_CHANNEL_NUM) {
        commutation_count[ch] = 0;
    }
}

/**
 * @brief 获取堵转标志位
 * @param ch 通道号
 * @return 堵转标志位
 */
bool get_blocked_flag(uint8_t ch)
{
    if (ch < ADC_CHANNEL_NUM) {
        return adCheck[ch].is_blocked;
    }
    return false;
}

/**
 * @brief 获取过流标志位
 * @param ch 通道号
 * @return 过流标志位
 */
bool get_overcurrent_flag(uint8_t ch)
{
    if (ch < ADC_CHANNEL_NUM) {
        return adCheck[ch].is_overcurrent;
    }
    return false;
}

/**
 * @brief 获取电流峰值
 * @param ch 通道号
 * @return 电流峰值
 */
uint16_t get_peak_current(uint8_t ch)
{
    if (ch < ADC_CHANNEL_NUM) {
        return adCheck[ch].peak_value;
    }
    return 0;
}

#endif
