#include "lin_task.h"

#define CAN2LIN_MSG_ID         0x60 // CAN转LIN模块（周期性读取CAN侧数据）
#define CAN2LIN_CMD_ID         0x61 // CAN转LIN模块（下发命令到CAN侧，事件帧）
#define ANGLE1_MSG_ID          0x20 // 角度检测模块1
#define ANGLE2_MSG_ID          0x30 // 角度检测模块2
#define PIR_MSG_ID             0x40 // 多人体红外感应模块

#define CAN2LIN_SLAVE_ID       0x01 // CAN转LIN模块从机ID
#define ANGLE1_SLAVE_ID        0x02 // 角度检测模块1从机ID
#define ANGLE2_SLAVE_ID        0x03 // 角度检测模块2从机ID
#define PIR_SLAVE_ID           0x04 // 多人体红外感应模块从机ID

#define CAN2LIN_PERIOD_MS      100 // CAN转LIN模块周期性读取时间间隔
#define ANGLE1_PERIOD_MS       50  // 角度检测模块1周期性读取时间间隔
#define ANGLE2_PERIOD_MS       50  // 角度检测模块2周期性读取时间间隔
#define PIR_PERIOD_MS          100 // 多人体红外感应模块周期性读取时间间隔

#define CAN2LIN_CMD_TIMEOUT_MS 50 // CAN转LIN模块命令超时时间 - 增加超时时间
#define ANGLE1_CMD_TIMEOUT_MS  30 // 角度检测模块1命令超时时间 - 增加超时时间
#define ANGLE2_CMD_TIMEOUT_MS  30 // 角度检测模块2命令超时时间 - 增加超时时间
#define PIR_CMD_TIMEOUT_MS     50 // 多人体红外感应模块命令超时时间 - 增加超时时间

#define CAN2LIN_DETECT_THRESHOLD 3

static int can2lin_found_count = 0;

static void can2lin_callback(const lin_schedule_entry_t *entry);
static void angle_callback(const lin_schedule_entry_t *entry);
static void pir_callback(const lin_schedule_entry_t *entry);

/**
 * @brief 注册所有LIN调度表项
 *        如需扩展，直接在本文件添加新的调度项注册即可
 */
void lin_schedule_register_all(void)
{
    lin_schedule_entry_t entry;

    // CAN转LIN模块（周期性读取CAN侧数据）
    memset(&entry, 0, sizeof(entry));
    entry.slave_id            = CAN2LIN_SLAVE_ID;
    entry.msg_id              = CAN2LIN_MSG_ID;
    entry.op_type             = LIN_OP_READ;
    entry.frame_type          = LIN_FRAME_UNCONDITIONAL;
    entry.period_ms           = CAN2LIN_PERIOD_MS;
    entry.response_timeout_ms = CAN2LIN_CMD_TIMEOUT_MS;
    entry.valid               = 1;
    entry.expect_len          = 8;
    lin_add_schedule_entry(&entry);

    // CAN转LIN模块（下发命令到CAN侧，事件帧）
    // memset(&entry, 0, sizeof(entry));
    // entry.slave_id            = CAN2LIN_SLAVE_ID;
    // entry.msg_id              = CAN2LIN_CMD_ID;
    // entry.op_type             = LIN_OP_WRITE;
    // entry.frame_type          = LIN_FRAME_EVENT;
    // entry.write_data[0]       = 0xA5; // 示例命令
    // entry.write_len           = 1;
    // entry.response_timeout_ms = CAN2LIN_CMD_TIMEOUT_MS;
    // entry.valid               = 1;
    // entry.expect_len          = 9;
    // lin_add_schedule_entry(&entry);

    // 角度检测模块1
    // memset(&entry, 0, sizeof(entry));
    // entry.slave_id            = ANGLE1_SLAVE_ID;
    // entry.msg_id              = ANGLE1_MSG_ID;
    // entry.op_type             = LIN_OP_READ;
    // entry.frame_type          = LIN_FRAME_UNCONDITIONAL;
    // entry.period_ms           = ANGLE1_PERIOD_MS;
    // entry.response_timeout_ms = ANGLE1_CMD_TIMEOUT_MS;
    // entry.valid               = 1;
    // lin_add_schedule_entry(&entry);

    // 角度检测模块2
    // memset(&entry, 0, sizeof(entry));
    // entry.slave_id            = ANGLE2_SLAVE_ID;
    // entry.msg_id              = ANGLE2_MSG_ID;
    // entry.op_type             = LIN_OP_READ;
    // entry.frame_type          = LIN_FRAME_UNCONDITIONAL;
    // entry.period_ms           = ANGLE2_PERIOD_MS;
    // entry.response_timeout_ms = ANGLE2_CMD_TIMEOUT_MS;
    // entry.valid               = 1;
    // lin_add_schedule_entry(&entry);

    // 多人体红外感应模块
    // memset(&entry, 0, sizeof(entry));
    // entry.slave_id            = PIR_SLAVE_ID;
    // entry.msg_id              = PIR_MSG_ID;
    // entry.op_type             = LIN_OP_READ;
    // entry.frame_type          = LIN_FRAME_UNCONDITIONAL;
    // entry.period_ms           = PIR_PERIOD_MS;
    // entry.response_timeout_ms = PIR_CMD_TIMEOUT_MS;
    // entry.valid               = 1;
    // lin_add_schedule_entry(&entry);

    // 注册回调函数
    lin_register_msgid_callback(CAN2LIN_MSG_ID, can2lin_callback);
    lin_register_msgid_callback(CAN2LIN_CMD_ID, can2lin_callback);
    lin_register_msgid_callback(ANGLE1_MSG_ID, angle_callback);
    lin_register_msgid_callback(ANGLE2_MSG_ID, angle_callback);
    lin_register_msgid_callback(PIR_MSG_ID, pir_callback);
}

/**
 * @brief CAN转LIN模块回调函数
 * @param msg 接收到的LIN消息
 */
static void can2lin_callback(const lin_schedule_entry_t *entry)
{
    if (entry->msg_id == CAN2LIN_MSG_ID) {
        // 打印当前id的读取数据（根据实际长度打印）
        char log_buf[100] = {0};
        char *p           = log_buf;
        p += sprintf(p, "can2lin_callback: msg_id = %02X, read data(%d): ", entry->msg_id, entry->read_len);
        for (int i = 0; i < entry->read_len && i < 8; i++) {
            p += sprintf(p, "%02X ", entry->read_data[i]);
        }
        LIN_LOG_INFO("%s", log_buf);
        // 检查是否收到有效数据，统计次数
        if (entry->last_result) {
            can2lin_found_count++;
            if (can2lin_found_count >= CAN2LIN_DETECT_THRESHOLD) {
                LIN_LOG_INFO("检测到CAN转LIN模块，通知切换为从机模式");
                lin_task_switch_to_slave();
                can2lin_found_count = 0; // 防止重复切换
            }
        } else {
            can2lin_found_count = 0;
        }
    } else if (entry->msg_id == CAN2LIN_CMD_ID) {
        LIN_LOG_INFO("can2lin_callback: msg_id = %02X, data = %02X", entry->msg_id, entry->write_data[0]);
    }
}

/**
 * @brief 角度检测模块1回调函数
 * @param msg 接收到的LIN消息
 */
static void angle_callback(const lin_schedule_entry_t *entry)
{
    if (entry->msg_id == ANGLE1_MSG_ID) {
        LIN_LOG_INFO("angle1_callback: msg_id = %02X, data = %02X", entry->msg_id, entry->read_data[0]);
    } else if (entry->msg_id == ANGLE2_MSG_ID) {
        LIN_LOG_INFO("angle2_callback: msg_id = %02X, data = %02X", entry->msg_id, entry->read_data[0]);
    }
}

/**
 * @brief 多人体红外感应模块回调函数
 * @param msg 接收到的LIN消息
 */
static void pir_callback(const lin_schedule_entry_t *entry)
{
    if (entry->msg_id == PIR_MSG_ID) {
        LIN_LOG_INFO("pir_callback: msg_id = %02X, data = %02X", entry->msg_id, entry->read_data[0]);
    }
}